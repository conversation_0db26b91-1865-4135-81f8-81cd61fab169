# Agent Guidelines for myCRM

## Build/Test Commands
- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build production version
- `npm run lint` - Run ESLint
- `npm test` - Run all Jest tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage
- `npm test -- -t "test name"` - Run specific test by name
- `npm test -- path/to/test-file.test.tsx` - Run specific test file

## Code Style Guidelines
- **TypeScript**: Use strict typing; follow tsconfig.json settings
- **Naming**: React components in PascalCase, variables/functions in camelCase
- **Files**: Components in PascalCase.tsx, API/libs in kebab-case.ts
- **Imports**: Use path aliases (@/lib/*, @/components/*, @/app/*)
- **Styling**: Use Tailwind CSS utility classes
- **React**: Follow Next.js App Router best practices for Server/Client Components
- **Testing**: Jest + React Testing Library; tests in __tests__/ directory
- **Error Handling**: Proper try/catch with meaningful error messages
- **Git**: Conventional Commits format (feat:, fix:, docs:, etc.)
- **Architecture**: Supabase for auth and database, NextJS for frontend/backend