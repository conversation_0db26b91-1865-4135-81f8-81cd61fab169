#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('\n===== CRM Environment Setup =====\n');
console.log('This script will help you set up your environment variables for the CRM application.');
console.log('You will need your Supabase project URL and anonymous key.\n');

const envPath = path.join(__dirname, '..', '.env.local');

// Check if .env.local already exists
if (fs.existsSync(envPath)) {
  console.log('Warning: A .env.local file already exists. Creating a new one will overwrite it.');
  rl.question('Continue? (y/n): ', (answer) => {
    if (answer.toLowerCase() !== 'y') {
      console.log('Operation cancelled.');
      rl.close();
      return;
    }
    promptForEnvVars();
  });
} else {
  promptForEnvVars();
}

function promptForEnvVars() {
  rl.question('Enter your Supabase URL: ', (supabaseUrl) => {
    rl.question('Enter your Supabase Anonymous Key: ', (supabaseAnonKey) => {
      // Create the .env.local file
      const envContent = `# Supabase configuration
NEXT_PUBLIC_SUPABASE_URL=${supabaseUrl}
NEXT_PUBLIC_SUPABASE_ANON_KEY=${supabaseAnonKey}

# Environment
NODE_ENV=development
`;

      fs.writeFileSync(envPath, envContent);
      console.log('\n.env.local file created successfully at:', envPath);
      console.log('\nIMPORTANT: Never commit this file to version control!');
      console.log('           It has been added to .gitignore for your security.\n');
      rl.close();
    });
  });
} 