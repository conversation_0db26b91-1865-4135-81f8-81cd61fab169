/**
 * Airtable to Supabase Migration Script
 * This script extracts data from Airtable and imports it to Supabase.
 * 
 * Instructions:
 * 1. Add your Airtable API key as AIRTABLE_API_KEY in your environment variables
 * 2. Add your Supabase service key and URL as SUPABASE_SERVICE_ROLE_KEY and SUPABASE_URL
 * 3. Run the script with: node migrate-airtable-to-supabase.js
 */

const Airtable = require('airtable');
const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');

// Load environment variables
dotenv.config();

// Initialize Airtable
const airtable = new Airtable({
  apiKey: process.env.AIRTABLE_API_KEY
});

// Initialize Supabase
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Airtable base ID - replace with your CRM base ID
const BASE_ID = 'appV1iuGpaZWPSRUx'; // My CRM System

// Map to store records by ID to handle relationships
const recordMap = {
  companies: {},
  contacts: {},
  projects: {},
  credentials: {},
  activities: {},
  expenses: {},
  invoices: {},
  invoice_items: {},
  client_portals: {}
};

// Utility to generate a UUID (for mapping Airtable IDs to Supabase UUIDs)
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Utility to safely access nested properties
function getNestedValue(obj, path, defaultValue = null) {
  const parts = path.split('.');
  let current = obj;
  for (const part of parts) {
    if (current === null || current === undefined || typeof current !== 'object') {
      return defaultValue;
    }
    current = current[part];
  }
  return current !== undefined ? current : defaultValue;
}

// Function to fetch all records from an Airtable table
async function fetchAirtableRecords(tableId) {
  const base = airtable.base(BASE_ID);
  const table = base.table(tableId);
  
  console.log(`Fetching records from Airtable table: ${tableId}`);
  
  let records = [];
  await table.select().eachPage((pageRecords, fetchNextPage) => {
    records = [...records, ...pageRecords];
    fetchNextPage();
  });
  
  console.log(`Fetched ${records.length} records from ${tableId}`);
  return records;
}

// Convert Airtable company records to Supabase format
function convertCompanyRecords(airtableRecords) {
  return airtableRecords.map(record => {
    const id = generateUUID();
    recordMap.companies[record.id] = id;
    
    return {
      id,
      name: record.get('Name') || 'Unnamed Company',
      industry: record.get('Industry'),
      size: record.get('Size'),
      website: record.get('Website'),
      address: record.get('Address'),
      logo_url: getNestedValue(record.get('Logo'), '0.url'),
      status: record.get('Status'),
      notes: record.get('Notes'),
      created_at: new Date(record.get('Created') || new Date()).toISOString(),
      updated_at: new Date().toISOString()
    };
  });
}

// Convert Airtable contact records to Supabase format
function convertContactRecords(airtableRecords) {
  return airtableRecords.map(record => {
    const id = generateUUID();
    recordMap.contacts[record.id] = id;
    
    // Handle company relationship
    let companyId = null;
    const companyRecords = record.get('Company');
    if (companyRecords && companyRecords.length > 0) {
      const airtableCompanyId = companyRecords[0];
      companyId = recordMap.companies[airtableCompanyId];
    }
    
    return {
      id,
      company_id: companyId,
      name: record.get('Name') || 'Unnamed Contact',
      email: record.get('Email'),
      phone: record.get('Phone'),
      position: record.get('Position'),
      is_primary: record.get('Primary Contact') || false,
      notes: record.get('Notes'),
      created_at: new Date(record.get('Created') || new Date()).toISOString(),
      updated_at: new Date().toISOString()
    };
  });
}

// Convert Airtable project records to Supabase format
function convertProjectRecords(airtableRecords) {
  return airtableRecords.map(record => {
    const id = generateUUID();
    recordMap.projects[record.id] = id;
    
    // Handle company relationship
    let companyId = null;
    const companyRecords = record.get('Company');
    if (companyRecords && companyRecords.length > 0) {
      const airtableCompanyId = companyRecords[0];
      companyId = recordMap.companies[airtableCompanyId];
    }
    
    return {
      id,
      company_id: companyId,
      name: record.get('Name') || 'Unnamed Project',
      description: record.get('Description'),
      value: record.get('Value') || 0,
      phase: record.get('Phase'),
      start_date: record.get('Start Date'),
      expected_end_date: record.get('Expected End Date'),
      actual_end_date: record.get('Actual End Date'),
      status: record.get('Status'),
      notes: record.get('Notes'),
      created_at: new Date(record.get('Created') || new Date()).toISOString(),
      updated_at: new Date().toISOString()
    };
  });
}

// Convert Airtable credential records to Supabase format
function convertCredentialRecords(airtableRecords) {
  return airtableRecords.map(record => {
    const id = generateUUID();
    recordMap.credentials[record.id] = id;
    
    // Handle company and project relationships
    let companyId = null;
    const companyRecords = record.get('Company');
    if (companyRecords && companyRecords.length > 0) {
      const airtableCompanyId = companyRecords[0];
      companyId = recordMap.companies[airtableCompanyId];
    }
    
    let projectId = null;
    const projectRecords = record.get('Project');
    if (projectRecords && projectRecords.length > 0) {
      const airtableProjectId = projectRecords[0];
      projectId = recordMap.projects[airtableProjectId];
    }
    
    return {
      id,
      company_id: companyId,
      project_id: projectId,
      name: record.get('Name') || 'Unnamed Credential',
      credential_type: record.get('Type'),
      url: record.get('URL'),
      username: record.get('Username'),
      password: record.get('Password'),
      expiry_date: record.get('Expiry Date'),
      notes: record.get('Notes'),
      created_at: new Date(record.get('Created') || new Date()).toISOString(),
      updated_at: new Date().toISOString(),
      password_last_updated: new Date().toISOString()
    };
  });
}

// Convert Airtable activity records to Supabase format
function convertActivityRecords(airtableRecords) {
  return airtableRecords.map(record => {
    const id = generateUUID();
    recordMap.activities[record.id] = id;
    
    // Handle company, project, and contact relationships
    let companyId = null;
    const companyRecords = record.get('Company');
    if (companyRecords && companyRecords.length > 0) {
      const airtableCompanyId = companyRecords[0];
      companyId = recordMap.companies[airtableCompanyId];
    }
    
    let projectId = null;
    const projectRecords = record.get('Project');
    if (projectRecords && projectRecords.length > 0) {
      const airtableProjectId = projectRecords[0];
      projectId = recordMap.projects[airtableProjectId];
    }
    
    let contactId = null;
    const contactRecords = record.get('Contact');
    if (contactRecords && contactRecords.length > 0) {
      const airtableContactId = contactRecords[0];
      contactId = recordMap.contacts[airtableContactId];
    }
    
    return {
      id,
      company_id: companyId,
      project_id: projectId,
      contact_id: contactId,
      name: record.get('Name') || 'Unnamed Activity',
      activity_type: record.get('Type'),
      date: record.get('Date'),
      details: record.get('Details'),
      status: record.get('Status'),
      created_at: new Date(record.get('Created') || new Date()).toISOString(),
      updated_at: new Date().toISOString()
    };
  });
}

// Convert Airtable expense records to Supabase format
function convertExpenseRecords(airtableRecords) {
  return airtableRecords.map(record => {
    const id = generateUUID();
    recordMap.expenses[record.id] = id;
    
    // Handle company and project relationships
    let companyId = null;
    const companyRecords = record.get('Company');
    if (companyRecords && companyRecords.length > 0) {
      const airtableCompanyId = companyRecords[0];
      companyId = recordMap.companies[airtableCompanyId];
    }
    
    let projectId = null;
    const projectRecords = record.get('Project');
    if (projectRecords && projectRecords.length > 0) {
      const airtableProjectId = projectRecords[0];
      projectId = recordMap.projects[airtableProjectId];
    }
    
    return {
      id,
      company_id: companyId,
      project_id: projectId,
      name: record.get('Name') || 'Unnamed Expense',
      amount: record.get('Amount') || 0,
      category: record.get('Category'),
      date: record.get('Date'),
      payment_method: record.get('Payment Method'),
      receipt_url: getNestedValue(record.get('Receipt'), '0.url'),
      notes: record.get('Notes'),
      is_reimbursable: record.get('Reimbursable') || false,
      reimbursement_status: record.get('Reimbursement Status'),
      created_at: new Date(record.get('Created') || new Date()).toISOString(),
      updated_at: new Date().toISOString()
    };
  });
}

// Convert Airtable invoice records to Supabase format
function convertInvoiceRecords(airtableRecords) {
  return airtableRecords.map(record => {
    const id = generateUUID();
    recordMap.invoices[record.id] = id;
    
    // Handle company and project relationships
    let companyId = null;
    const companyRecords = record.get('Company');
    if (companyRecords && companyRecords.length > 0) {
      const airtableCompanyId = companyRecords[0];
      companyId = recordMap.companies[airtableCompanyId];
    }
    
    let projectId = null;
    const projectRecords = record.get('Project');
    if (projectRecords && projectRecords.length > 0) {
      const airtableProjectId = projectRecords[0];
      projectId = recordMap.projects[airtableProjectId];
    }
    
    return {
      id,
      company_id: companyId,
      project_id: projectId,
      invoice_number: record.get('Invoice Number') || `INV-${Math.floor(Math.random() * 10000)}`,
      issue_date: record.get('Issue Date') || new Date().toISOString().split('T')[0],
      due_date: record.get('Due Date') || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      amount: record.get('Amount') || 0,
      status: record.get('Status'),
      payment_date: record.get('Payment Date'),
      notes: record.get('Notes'),
      pdf_url: getNestedValue(record.get('PDF'), '0.url'),
      created_at: new Date(record.get('Created') || new Date()).toISOString(),
      updated_at: new Date().toISOString()
    };
  });
}

// Convert Airtable invoice item records to Supabase format
function convertInvoiceItemRecords(airtableRecords) {
  return airtableRecords.map(record => {
    const id = generateUUID();
    recordMap.invoice_items[record.id] = id;
    
    // Handle invoice and expense relationships
    let invoiceId = null;
    const invoiceRecords = record.get('Invoice');
    if (invoiceRecords && invoiceRecords.length > 0) {
      const airtableInvoiceId = invoiceRecords[0];
      invoiceId = recordMap.invoices[airtableInvoiceId];
    }
    
    let expenseId = null;
    const expenseRecords = record.get('Expense');
    if (expenseRecords && expenseRecords.length > 0) {
      const airtableExpenseId = expenseRecords[0];
      expenseId = recordMap.expenses[airtableExpenseId];
    }
    
    return {
      id,
      invoice_id: invoiceId,
      description: record.get('Description') || 'Unnamed Item',
      quantity: record.get('Quantity') || 1,
      unit_price: record.get('Unit Price') || 0,
      amount: record.get('Amount') || 0,
      expense_id: expenseId,
      service_id: null // This would need to be handled if there's a service relation
    };
  });
}

// Convert Airtable client portal records to Supabase format
function convertClientPortalRecords(airtableRecords) {
  return airtableRecords.map(record => {
    const id = generateUUID();
    recordMap.client_portals[record.id] = id;
    
    // Handle company relationship
    let companyId = null;
    const companyRecords = record.get('Company');
    if (companyRecords && companyRecords.length > 0) {
      const airtableCompanyId = companyRecords[0];
      companyId = recordMap.companies[airtableCompanyId];
    }
    
    return {
      id,
      company_id: companyId,
      name: record.get('Name') || 'Client Portal',
      access_token: record.get('Access Token') || crypto.randomBytes(32).toString('hex'),
      is_active: record.get('Active') !== false,
      last_accessed: record.get('Last Accessed'),
      created_at: new Date(record.get('Created') || new Date()).toISOString(),
      updated_at: new Date().toISOString()
    };
  });
}

// Insert records into Supabase
async function insertIntoSupabase(tableName, records) {
  if (!records || records.length === 0) {
    console.log(`No records to insert into ${tableName}`);
    return;
  }
  
  console.log(`Inserting ${records.length} records into ${tableName}...`);
  
  // Insert in batches of 100 to avoid potential issues with large datasets
  const batchSize = 100;
  for (let i = 0; i < records.length; i += batchSize) {
    const batch = records.slice(i, i + batchSize);
    const { data, error } = await supabase
      .from(tableName)
      .insert(batch)
      .select();
    
    if (error) {
      console.error(`Error inserting batch into ${tableName}:`, error);
    } else {
      console.log(`Successfully inserted batch ${i/batchSize + 1} (${batch.length} records) into ${tableName}`);
    }
  }
}

// The main migration function
async function migrateAirtableToSupabase() {
  try {
    console.log('Starting Airtable to Supabase migration...');

    // Step 1: Fetch all records from Airtable
    console.log('Fetching records from Airtable...');
    
    // Fetch companies first (these are usually at the top of the relationship hierarchy)
    const companiesTable = await fetchAirtableRecords('Companies');
    
    // Then fetch other related tables
    const contactsTable = await fetchAirtableRecords('Contacts');
    const projectsTable = await fetchAirtableRecords('Projects');
    const credentialsTable = await fetchAirtableRecords('Credentials');
    const activitiesTable = await fetchAirtableRecords('Activities');
    const expensesTable = await fetchAirtableRecords('Expenses');
    const invoicesTable = await fetchAirtableRecords('Invoices');
    const invoiceItemsTable = await fetchAirtableRecords('Invoice Items');
    const clientPortalsTable = await fetchAirtableRecords('Client Portals');
    
    // Step 2: Convert the records to Supabase format
    console.log('Converting records to Supabase format...');
    
    const supabaseCompanies = convertCompanyRecords(companiesTable);
    const supabaseContacts = convertContactRecords(contactsTable);
    const supabaseProjects = convertProjectRecords(projectsTable);
    const supabaseCredentials = convertCredentialRecords(credentialsTable);
    const supabaseActivities = convertActivityRecords(activitiesTable);
    const supabaseExpenses = convertExpenseRecords(expensesTable);
    const supabaseInvoices = convertInvoiceRecords(invoicesTable);
    const supabaseInvoiceItems = convertInvoiceItemRecords(invoiceItemsTable);
    const supabaseClientPortals = convertClientPortalRecords(clientPortalsTable);
    
    // Step 3: Save the record maps to a JSON file for reference (useful for debugging)
    fs.writeFileSync(
      path.join(__dirname, 'airtable-to-supabase-map.json'),
      JSON.stringify(recordMap, null, 2)
    );
    
    // Step 4: Insert records into Supabase (in the correct order for foreign key constraints)
    console.log('Inserting records into Supabase...');
    
    // Companies first (no foreign key dependencies)
    await insertIntoSupabase('companies', supabaseCompanies);
    
    // Then tables with company foreign keys
    await insertIntoSupabase('contacts', supabaseContacts);
    await insertIntoSupabase('projects', supabaseProjects);
    await insertIntoSupabase('client_portals', supabaseClientPortals);
    
    // Then tables with project, company, and contact foreign keys
    await insertIntoSupabase('credentials', supabaseCredentials);
    await insertIntoSupabase('activities', supabaseActivities);
    await insertIntoSupabase('expenses', supabaseExpenses);
    await insertIntoSupabase('invoices', supabaseInvoices);
    
    // Finally, tables with dependencies on all the above
    await insertIntoSupabase('invoice_items', supabaseInvoiceItems);
    
    console.log('Migration completed successfully!');
    
  } catch (error) {
    console.error('Error during migration:', error);
  }
}

// Run the migration
migrateAirtableToSupabase().catch(console.error); 