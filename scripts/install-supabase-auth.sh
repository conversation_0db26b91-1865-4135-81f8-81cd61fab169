#!/bin/bash

# Install Supabase Auth packages
echo "Installing Supabase Auth packages..."
npm install @supabase/ssr @supabase/supabase-js

# Remove Clerk packages
echo "Removing Clerk packages..."
npm uninstall @clerk/clerk-react @clerk/nextjs svix

echo "Installation complete!"
echo "Please update your .env.local file with your Supabase credentials."
echo "See .env.local.example for the required variables."
