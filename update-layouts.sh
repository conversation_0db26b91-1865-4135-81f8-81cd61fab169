#!/bin/bash

# This script updates all layout files to add max-w-7xl mx-auto to the main element

# Find all layout.tsx files
find app -name "layout.tsx" | while read -r file; do
  # Skip files that already have max-w-7xl mx-auto
  if grep -q "max-w-7xl mx-auto" "$file"; then
    echo "Skipping $file (already updated)"
    continue
  fi
  
  # Update the file
  sed -i '' 's/<main className="p-6 mt-16">/<main className="p-6 mt-16 max-w-7xl mx-auto">/' "$file"
  echo "Updated $file"
done

echo "All layout files updated!"
