{"version": 2, "buildCommand": "npm run build", "installCommand": "npm install --legacy-peer-deps", "framework": "nextjs", "regions": ["sfo1"], "env": {"NEXT_PUBLIC_SUPABASE_URL": "https://dwjmtrwiwypetulfhcgj.supabase.co", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR3am10cndpd3lwZXR1bGZoY2dqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3MjgwNTAsImV4cCI6MjA2MTMwNDA1MH0.n8TX8w50Qm1V9lsP1RlCJxKFee1RlJdSDWW2WxIoa-k"}, "build": {"env": {"NEXT_PUBLIC_SUPABASE_URL": "https://dwjmtrwiwypetulfhcgj.supabase.co", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR3am10cndpd3lwZXR1bGZoY2dqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3MjgwNTAsImV4cCI6MjA2MTMwNDA1MH0.n8TX8w50Qm1V9lsP1RlCJxKFee1RlJdSDWW2WxIoa-k"}}}