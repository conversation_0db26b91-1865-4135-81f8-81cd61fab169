{"name": "mycrm", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup": "node scripts/generate-env.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hello-pangea/dnd": "^16.6.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.75.7", "@types/react-datepicker": "^6.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.509.0", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^15.0.0-alpha.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.18", "eslint": "^9", "eslint-config-next": "15.3.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.38", "typescript": "^5"}}