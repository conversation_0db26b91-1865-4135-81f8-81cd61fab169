'use client'

import Link from 'next/link'
import { ThemeToggle } from './theme-toggle'
import { User, Settings, Menu, X } from 'lucide-react'
import { useState } from 'react'

export function AppHeader() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen)
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 max-w-screen-2xl items-center">
        <div className="mr-4 flex">
          <Link href="/dashboard" className="mr-6 flex items-center space-x-2">
            <span className="hidden font-bold sm:inline-block">myCRM</span>
          </Link>
          
          <nav className="hidden md:flex items-center space-x-4 lg:space-x-6">
            <Link href="/dashboard" className="text-sm font-medium transition-colors hover:text-primary">
              Dashboard
            </Link>
            <Link href="/companies" className="text-sm font-medium transition-colors hover:text-primary">
              Companies
            </Link>
            <Link href="/projects" className="text-sm font-medium transition-colors hover:text-primary">
              Projects
            </Link>
            <Link href="/invoices" className="text-sm font-medium transition-colors hover:text-primary">
              Invoices
            </Link>
            <Link href="/credentials" className="text-sm font-medium transition-colors hover:text-primary">
              Credentials
            </Link>
          </nav>
        </div>

        <div className="flex flex-1 items-center justify-end space-x-2">
          <nav className="flex items-center space-x-2">
            <ThemeToggle />
            <Link href="/profile" className="p-2 rounded-md hover:bg-accent">
              <User className="h-5 w-5" />
              <span className="sr-only">Profile</span>
            </Link>
            <Link href="/settings" className="p-2 rounded-md hover:bg-accent">
              <Settings className="h-5 w-5" />
              <span className="sr-only">Settings</span>
            </Link>
            <button
              className="block md:hidden p-2 rounded-md hover:bg-accent"
              onClick={toggleMobileMenu}
            >
              {mobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
              <span className="sr-only">Toggle menu</span>
            </button>
          </nav>
        </div>
      </div>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden border-t border-border/40 py-4 px-8 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <nav className="flex flex-col space-y-4">
            <Link href="/dashboard" className="text-sm font-medium transition-colors hover:text-primary">
              Dashboard
            </Link>
            <Link href="/companies" className="text-sm font-medium transition-colors hover:text-primary">
              Companies
            </Link>
            <Link href="/projects" className="text-sm font-medium transition-colors hover:text-primary">
              Projects
            </Link>
            <Link href="/invoices" className="text-sm font-medium transition-colors hover:text-primary">
              Invoices
            </Link>
            <Link href="/credentials" className="text-sm font-medium transition-colors hover:text-primary">
              Credentials
            </Link>
          </nav>
        </div>
      )}
    </header>
  )
}