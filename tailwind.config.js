/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
	],
  theme: {
  	container: {
  		center: true,
  		padding: '2rem',
  		screens: {
  			'sm': '640px',
  			'md': '768px',
  			'lg': '1024px',
  			'xl': '1280px',
  			'2xl': '1400px'
  		}
  	},
  	extend: {
  		fontFamily: {
  			sans: [
  				'Inter var',
  				'Inter',
  				'system-ui',
  				'sans-serif'
  			],
  			display: [
  				'Inter var',
  				'Inter',
  				'system-ui',
  				'sans-serif'
  			],
  			mono: [
  				'Geist Mono',
  				'monospace'
  			]
  		},
  		colors: {
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			primary: {
  				'50': 'hsl(224 100% 98%)',
  				'100': 'hsl(224 100% 95%)',
  				'200': 'hsl(224 95% 90%)',
  				'300': 'hsl(224 90% 80%)',
  				'400': 'hsl(224 85% 70%)',
  				'500': 'hsl(224 80% 65%)',
  				'600': 'hsl(224 80% 60%)',
  				'700': 'hsl(224 70% 50%)',
  				'800': 'hsl(224 65% 40%)',
  				'900': 'hsl(224 60% 30%)',
  				'950': 'hsl(224 55% 15%)',
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				'50': 'hsl(252 100% 98%)',
  				'100': 'hsl(252 100% 95%)',
  				'200': 'hsl(252 95% 90%)',
  				'300': 'hsl(252 90% 80%)',
  				'400': 'hsl(252 85% 70%)',
  				'500': 'hsl(252 80% 65%)',
  				'600': 'hsl(252 80% 60%)',
  				'700': 'hsl(252 70% 50%)',
  				'800': 'hsl(252 65% 40%)',
  				'900': 'hsl(252 60% 30%)',
  				'950': 'hsl(252 55% 15%)',
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			success: {
  				'50': 'hsl(142 100% 98%)',
  				'100': 'hsl(142 100% 95%)',
  				'200': 'hsl(142 95% 90%)',
  				'300': 'hsl(142 90% 80%)',
  				'400': 'hsl(142 85% 70%)',
  				'500': 'hsl(142 80% 65%)',
  				'600': 'hsl(142 80% 60%)',
  				'700': 'hsl(142 70% 50%)',
  				DEFAULT: 'hsl(var(--success))',
  				foreground: 'hsl(var(--success-foreground))'
  			},
  			warning: {
  				'50': 'hsl(48 100% 98%)',
  				'100': 'hsl(48 100% 95%)',
  				'200': 'hsl(48 95% 90%)',
  				'300': 'hsl(48 90% 80%)',
  				'400': 'hsl(48 85% 70%)',
  				'500': 'hsl(48 80% 65%)',
  				'600': 'hsl(48 80% 60%)',
  				'700': 'hsl(48 70% 50%)',
  				DEFAULT: 'hsl(var(--warning))',
  				foreground: 'hsl(var(--warning-foreground))'
  			},
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		boxShadow: {
  			none: 'none',
  			xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  			sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  			DEFAULT: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  			md: '0 6px 10px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  			lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  			xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  			'2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  			inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  			subtle: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  			card: '0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03)',
  			'card-hover': '0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.03)',
  			elevated: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
  		},
  		typography: {
  			DEFAULT: {
  				css: {
  					maxWidth: '65ch',
  					color: 'hsl(var(--foreground))',
  					a: {
  						color: 'hsl(var(--primary))',
  						textDecoration: 'underline',
  						textUnderlineOffset: '3px',
  						fontWeight: '500',
  						'&:hover': {
  							color: 'hsl(var(--primary)/0.9)'
  						}
  					},
  					strong: {
  						color: 'hsl(var(--foreground))',
  						fontWeight: '600'
  					},
  					h1: {
  						color: 'hsl(var(--foreground))',
  						fontWeight: '700'
  					},
  					h2: {
  						color: 'hsl(var(--foreground))',
  						fontWeight: '600'
  					},
  					h3: {
  						color: 'hsl(var(--foreground))',
  						fontWeight: '500'
  					},
  					h4: {
  						color: 'hsl(var(--foreground))',
  						fontWeight: '500'
  					},
  					code: {
  						color: 'hsl(var(--foreground))',
  						fontWeight: '400',
  						backgroundColor: 'hsl(var(--muted))',
  						padding: '0.2em 0.4em',
  						borderRadius: '0.25rem'
  					},
  					'code::before': {
  						content: '"'
  					},
  					'code::after': {
  						content: '"'
  					},
  					pre: {
  						backgroundColor: 'hsl(var(--muted))',
  						borderRadius: '0.5rem',
  						padding: '1rem'
  					},
  					blockquote: {
  						color: 'hsl(var(--muted-foreground))',
  						borderLeftColor: 'hsl(var(--border))'
  					}
  				}
  			}
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: 0
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: 0
  				}
  			},
  			'pulse-subtle': {
  				'0%, 100%': {
  					opacity: 1
  				},
  				'50%': {
  					opacity: 0.8
  				}
  			},
  			'fade-in': {
  				from: {
  					opacity: 0
  				},
  				to: {
  					opacity: 1
  				}
  			},
  			'fade-out': {
  				from: {
  					opacity: 1
  				},
  				to: {
  					opacity: 0
  				}
  			},
  			'slide-up': {
  				from: {
  					transform: 'translateY(10px)',
  					opacity: 0
  				},
  				to: {
  					transform: 'translateY(0)',
  					opacity: 1
  				}
  			},
  			'slide-down': {
  				from: {
  					transform: 'translateY(-10px)',
  					opacity: 0
  				},
  				to: {
  					transform: 'translateY(0)',
  					opacity: 1
  				}
  			},
  			'scale-in': {
  				from: {
  					transform: 'scale(0.95)',
  					opacity: 0
  				},
  				to: {
  					transform: 'scale(1)',
  					opacity: 1
  				}
  			},
  			'scale-out': {
  				from: {
  					transform: 'scale(1)',
  					opacity: 1
  				},
  				to: {
  					transform: 'scale(0.95)',
  					opacity: 0
  				}
  			},
  			'enter-from-right': {
  				from: {
  					transform: 'translateX(10px)',
  					opacity: 0
  				},
  				to: {
  					transform: 'translateX(0)',
  					opacity: 1
  				}
  			},
  			'enter-from-left': {
  				from: {
  					transform: 'translateX(-10px)',
  					opacity: 0
  				},
  				to: {
  					transform: 'translateX(0)',
  					opacity: 1
  				}
  			},
  			'exit-to-right': {
  				from: {
  					transform: 'translateX(0)',
  					opacity: 1
  				},
  				to: {
  					transform: 'translateX(10px)',
  					opacity: 0
  				}
  			},
  			'exit-to-left': {
  				from: {
  					transform: 'translateX(0)',
  					opacity: 1
  				},
  				to: {
  					transform: 'translateX(-10px)',
  					opacity: 0
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out',
  			'pulse-subtle': 'pulse-subtle 2s ease-in-out infinite',
  			'fade-in': 'fade-in 0.3s ease-out',
  			'fade-out': 'fade-out 0.3s ease-out',
  			'slide-up': 'slide-up 0.3s ease-out',
  			'slide-down': 'slide-down 0.3s ease-out',
  			'scale-in': 'scale-in 0.2s ease-out',
  			'scale-out': 'scale-out 0.2s ease-in',
  			'enter-from-right': 'enter-from-right 0.25s ease-out',
  			'enter-from-left': 'enter-from-left 0.25s ease-out',
  			'exit-to-right': 'exit-to-right 0.25s ease-in',
  			'exit-to-left': 'exit-to-left 0.25s ease-in'
  		},
  		transitionProperty: {
  			height: 'height',
  			spacing: 'margin, padding',
  			width: 'width',
  			transform: 'transform'
  		},
  		transitionTimingFunction: {
  			'in-out-cubic': 'cubic-bezier(0.65, 0, 0.35, 1)',
  			'out-back': 'cubic-bezier(0.34, 1.56, 0.64, 1)'
  		}
  	}
  },
  plugins: [
    require('tailwindcss-animate'),
    require('@tailwindcss/typography'),
  ],
}