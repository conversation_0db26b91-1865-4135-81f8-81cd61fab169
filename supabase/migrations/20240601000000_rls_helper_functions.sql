-- Create a function to get all RLS policies
CREATE OR REPLACE FUNCTION public.get_policies()
RETURNS TABLE (
  table_name text,
  policy_name text,
  roles text[],
  cmd text,
  qual text,
  with_check text,
  schema_name text
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT
    t.relname::text AS table_name,
    p.polname::text AS policy_name,
    p.polroles::text[] AS roles,
    CASE p.polcmd
      WHEN 'r' THEN 'SELECT'
      WHEN 'a' THEN 'INSERT'
      WHEN 'w' THEN 'UPDATE'
      WHEN 'd' THEN 'DELETE'
      WHEN '*' THEN 'ALL'
    END AS cmd,
    pg_catalog.pg_get_expr(p.polqual, p.polrelid, true) AS qual,
    pg_catalog.pg_get_expr(p.polwithcheck, p.polrelid, true) AS with_check,
    n.nspname::text AS schema_name
  FROM pg_policy p
  JOIN pg_class t ON t.oid = p.polrelid
  JOIN pg_namespace n ON n.oid = t.relnamespace
  WHERE n.nspname = 'public'
  ORDER BY t.relname, p.polname;
$$;

-- Create a function to check if RLS is enabled for a table
CREATE OR REPLACE FUNCTION public.is_rls_enabled(table_name text)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT c.relrowsecurity
  FROM pg_class c
  JOIN pg_namespace n ON n.oid = c.relnamespace
  WHERE n.nspname = 'public' AND c.relname = table_name;
$$;

-- Create a function to get tables with RLS status
CREATE OR REPLACE FUNCTION public.get_tables_rls_status()
RETURNS TABLE (
  table_name text,
  rls_enabled boolean
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT 
    c.relname::text AS table_name,
    c.relrowsecurity AS rls_enabled
  FROM pg_class c
  JOIN pg_namespace n ON n.oid = c.relnamespace
  WHERE n.nspname = 'public' AND c.relkind = 'r'
  ORDER BY c.relname;
$$;
