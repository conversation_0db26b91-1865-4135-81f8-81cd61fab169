-- Create users table to sync with <PERSON>
CREATE TABLE IF NOT EXISTS public.users (
  id UUID PRIMARY KEY,
  email TEXT UNIQUE,
  first_name TEXT,
  last_name TEXT,
  image_url TEXT,
  clerk_id TEXT UNIQUE,
  is_admin <PERSON><PERSON><PERSON>EAN DEFAULT FALSE,
  is_deleted B<PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create client_users table for client portal access
CREATE TABLE IF NOT EXISTS public.client_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  first_name TEXT,
  last_name TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  last_login TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES public.users(id)
);

-- Create client_sessions table
CREATE TABLE IF NOT EXISTS public.client_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  token TEXT UNIQUE NOT NULL,
  user_id UUID NOT NULL REFERENCES public.client_users(id) ON DELETE CASCADE,
  company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Create function to verify client credentials
CREATE OR REPLACE FUNCTION public.verify_client_credentials(
  p_email TEXT,
  p_password TEXT
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_stored_hash TEXT;
  v_is_active BOOLEAN;
BEGIN
  -- Get the stored password hash and active status
  SELECT password_hash, is_active INTO v_stored_hash, v_is_active
  FROM public.client_users
  WHERE email = p_email;
  
  -- If no user found or user is inactive, return false
  IF v_stored_hash IS NULL OR NOT v_is_active THEN
    RETURN FALSE;
  END IF;
  
  -- Check if password matches (using pgcrypto's crypt function)
  RETURN v_stored_hash = crypt(p_password, v_stored_hash);
END;
$$;

-- Create function to create a new client user
CREATE OR REPLACE FUNCTION public.create_client_user(
  p_company_id UUID,
  p_email TEXT,
  p_password TEXT,
  p_first_name TEXT,
  p_last_name TEXT,
  p_created_by UUID
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
BEGIN
  -- Check if email already exists
  IF EXISTS (SELECT 1 FROM public.client_users WHERE email = p_email) THEN
    RAISE EXCEPTION 'Email already exists';
  END IF;
  
  -- Insert new client user
  INSERT INTO public.client_users (
    company_id,
    email,
    password_hash,
    first_name,
    last_name,
    created_by
  ) VALUES (
    p_company_id,
    p_email,
    crypt(p_password, gen_salt('bf')),
    p_first_name,
    p_last_name,
    p_created_by
  ) RETURNING id INTO v_user_id;
  
  RETURN v_user_id;
END;
$$;

-- Create function to reset client password
CREATE OR REPLACE FUNCTION public.reset_client_password(
  p_user_id UUID,
  p_new_password TEXT
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE public.client_users
  SET password_hash = crypt(p_new_password, gen_salt('bf')),
      updated_at = NOW()
  WHERE id = p_user_id;
  
  RETURN FOUND;
END;
$$;

-- Create RLS policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_sessions ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view their own data" ON public.users
  FOR SELECT USING (auth.uid() = id);
  
CREATE POLICY "Admins can view all users" ON public.users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND is_admin = TRUE
    )
  );

-- Client users policies
CREATE POLICY "Admins can manage client users" ON public.client_users
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND is_admin = TRUE
    )
  );

-- Client sessions policies
CREATE POLICY "Admins can view client sessions" ON public.client_sessions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND is_admin = TRUE
    )
  );
