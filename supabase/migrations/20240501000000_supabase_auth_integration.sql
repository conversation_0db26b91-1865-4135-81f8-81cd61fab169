-- Update users table to work with <PERSON>pa<PERSON> Auth
ALTER TABLE public.users DROP COLUMN IF EXISTS clerk_id;

-- Add RLS policies for Supabase Auth
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Users can view and update their own data
CREATE POLICY "Users can view their own data" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own data" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- <PERSON><PERSON> can view and manage all users
CREATE POLICY "Ad<PERSON> can view all users" ON public.users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND is_admin = TRUE
    )
  );

CREATE POLICY "Admins can update all users" ON public.users
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND is_admin = TRUE
    )
  );

-- Create function to add admin role
CREATE OR REPLACE FUNCTION public.add_admin_role(p_user_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE public.users
  SET is_admin = TRUE,
      updated_at = NOW()
  WHERE id = p_user_id;
END;
$$;

-- Create function to add team member role
CREATE OR REPLACE FUNCTION public.add_team_member_role(p_user_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE public.users
  SET is_admin = FALSE,
      updated_at = NOW()
  WHERE id = p_user_id;
END;
$$;

-- Create function to remove user role
CREATE OR REPLACE FUNCTION public.remove_user_role(p_user_id UUID, p_role TEXT)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF p_role = 'admin' THEN
    UPDATE public.users
    SET is_admin = FALSE,
        updated_at = NOW()
    WHERE id = p_user_id;
  END IF;
END;
$$;
