import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// This middleware only handles environment variable checks for Supabase
// Authentication is handled by the main middleware.ts file
export async function middleware(request: NextRequest) {
  // Check for environment variables in development mode
  if (process.env.NODE_ENV === 'development') {
    const missingVars = [];

    if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
      missingVars.push('NEXT_PUBLIC_SUPABASE_URL');
    }

    if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      missingVars.push('NEXT_PUBLIC_SUPABASE_ANON_KEY');
    }

    // If there are missing variables, redirect to setup page
    if (missingVars.length > 0) {
      // If already on the setup page, don't redirect to avoid loops
      if (request.nextUrl.pathname === '/setup') {
        return NextResponse.next();
      }

      // Create URL for setup page with missing vars info
      const url = request.nextUrl.clone();
      url.pathname = '/setup';
      url.search = `?missing=${missingVars.join(',')}`;

      return NextResponse.redirect(url);
    }
  }

  return NextResponse.next()
}

// Configure which routes use this middleware
export const config = {
  matcher: [
    // Only run this middleware for the setup page and root
    '/',
    '/setup',
  ],
}