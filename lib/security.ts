/**
 * Security utilities for handling sensitive information
 * These utilities provide a client-side abstraction for security features
 * implemented in Supabase functions
 */

import { createBrowserClient } from '@supabase/ssr';
import { Tables } from './supabase/types';
import { NextRequest } from 'next/server';

// Rate limiting maps (in a production app, use Redis or similar)
const apiRateLimitMap = new Map<string, { count: number, timestamp: number }>();
const loginRateLimitMap = new Map<string, { count: number, timestamp: number }>();

// Rate limit constants
const API_MAX_REQUESTS = 20; // Max 20 API requests
const LOGIN_MAX_ATTEMPTS = 5; // Max 5 login attempts
const API_TIME_WINDOW = 60 * 1000; // 1 minute for API requests
const LOGIN_TIME_WINDOW = 15 * 60 * 1000; // 15 minutes for login attempts

/**
 * Securely retrieve credentials while ensuring proper authorization
 * @param credentialId The ID of the credential to retrieve
 * @returns The decrypted credential or null if unauthorized
 */
export async function getDecryptedCredential(credentialId: string) {
  try {
    console.log('Fetching credential with ID:', credentialId);

    // Create a Supabase client
    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    // First check if the user is authenticated
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      console.error('No active session found when fetching credential');
      return null;
    }

    console.log('User authenticated, fetching credential details');

    // Fetch the credential with company information
    const { data, error } = await supabase
      .from('credentials')
      .select('*, companies:company_id(id, name)')
      .eq('id', credentialId)
      .single();

    if (error) {
      console.error('Error fetching credential:', error);
      return null;
    }

    console.log('Credential fetched successfully');

    // Check if we need to decrypt the password
    let decryptedPassword = null;
    if (data.encrypted_password) {
      try {
        // Import the decryption function dynamically
        const { decryptData, isEncryptedData } = await import('./encryption');

        // Check if the password is encrypted
        if (isEncryptedData(data.encrypted_password)) {
          decryptedPassword = await decryptData(data.encrypted_password);
        } else {
          // If not encrypted, use as is
          decryptedPassword = data.encrypted_password;
        }
      } catch (decryptError) {
        console.error('Error decrypting password:', decryptError);
        // Fall back to using the encrypted password
        decryptedPassword = data.encrypted_password;
      }
    } else if (data.password) {
      // If there's a plaintext password, use it
      decryptedPassword = data.password;
    }

    return {
      ...data,
      password: decryptedPassword
    };
  } catch (err) {
    console.error('Unexpected error fetching credential:', err);
    return null;
  }
}

/**
 * Mask sensitive information for display purposes
 * @param text The text to mask
 * @param visibleChars Number of characters to show at beginning and end
 * @returns Masked string
 */
export function maskSensitiveText(text: string, visibleChars: number = 2): string {
  if (!text) return '';
  if (text.length <= visibleChars * 2) return text;

  const start = text.substring(0, visibleChars);
  const end = text.substring(text.length - visibleChars);
  const maskedLength = text.length - (visibleChars * 2);
  const mask = '*'.repeat(maskedLength);

  return `${start}${mask}${end}`;
}

/**
 * Check if the current user has access to a specific company's data
 * @param companyId The company ID to check access for
 * @returns Boolean indicating if access is allowed
 */
export async function hasCompanyAccess(companyId: string): Promise<boolean> {
  // Create a Supabase client
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  // First check if the user is an admin or team member
  const { data: roleData } = await supabase
    .from('user_roles')
    .select('role')
    .single();

  if (roleData?.role === 'admin' || roleData?.role === 'team_member') {
    return true;
  }

  // If client, check if they have access to this specific company
  const { data: clientCompanyData, error } = await supabase
    .from('client_companies')
    .select('company_id')
    .eq('company_id', companyId)
    .single();

  return !!clientCompanyData && !error;
}

/**
 * Securely create or update a credential with encryption
 * @param credential The credential data to save
 * @returns The created/updated credential ID or null on error
 */
export async function saveCredential(credential: {
  id?: string;
  name: string;
  username?: string | null;
  password?: string | null;
  url?: string | null;
  company_id?: string | null;
  project_id?: string | null;
  credential_type?: string | null;
  notes?: string | null;
}) {
  // Create a Supabase client
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  // Use Supabase function to handle encryption
  const { data, error } = await supabase.rpc('encrypt_password', {
    password: credential.password || ''
  });

  if (error) {
    console.error('Error encrypting password:', error);
    return null;
  }

  const encryptedPassword = data;

  // Remove plaintext password and add encrypted one
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { password, ...credentialWithoutPassword } = credential;
  const credentialToSave = {
    ...credentialWithoutPassword,
    encrypted_password: encryptedPassword
  };

  // Insert or update the credential
  const operation = credential.id
    ? supabase.from('credentials').update(credentialToSave).eq('id', credential.id)
    : supabase.from('credentials').insert(credentialToSave);

  const { data: savedData, error: saveError } = await operation;

  if (saveError) {
    console.error('Error saving credential:', saveError);
    return null;
  }

  return credential.id || (savedData as unknown as Tables<'credentials'>[])?.[0]?.id || null;
}

/**
 * Check if a user is an admin
 */
export async function isUserAdmin(userId: string): Promise<boolean> {
  try {
    // Create a Supabase client
    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    // Use the regular client for this check
    const { data, error } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', userId)
      .single();

    if (error || !data) {
      console.error('Error checking admin status:', error);
      return false;
    }

    return data.role === 'admin';
  } catch (err) {
    console.error('Unexpected error checking admin status:', err);
    return false;
  }
}

/**
 * Check API rate limit
 */
export function checkApiRateLimit(userId: string): {
  allowed: boolean,
  remaining: number,
  resetAt: Date
} {
  const now = Date.now();
  const userRateLimit = apiRateLimitMap.get(userId) || { count: 0, timestamp: now };

  // Reset count if outside time window
  if (now - userRateLimit.timestamp > API_TIME_WINDOW) {
    userRateLimit.count = 0;
    userRateLimit.timestamp = now;
  }

  // Check if rate limit exceeded
  const allowed = userRateLimit.count < API_MAX_REQUESTS;

  // Increment request count if allowed
  if (allowed) {
    userRateLimit.count++;
    apiRateLimitMap.set(userId, userRateLimit);
  }

  const remaining = Math.max(0, API_MAX_REQUESTS - userRateLimit.count);
  const resetAt = new Date(userRateLimit.timestamp + API_TIME_WINDOW);

  return { allowed, remaining, resetAt };
}

/**
 * Check login rate limit
 */
export function checkLoginRateLimit(ipAddress: string): {
  allowed: boolean,
  remaining: number,
  resetAt: Date
} {
  const now = Date.now();
  const ipRateLimit = loginRateLimitMap.get(ipAddress) || { count: 0, timestamp: now };

  // Reset count if outside time window
  if (now - ipRateLimit.timestamp > LOGIN_TIME_WINDOW) {
    ipRateLimit.count = 0;
    ipRateLimit.timestamp = now;
  }

  // Check if rate limit exceeded
  const allowed = ipRateLimit.count < LOGIN_MAX_ATTEMPTS;

  // Increment attempt count if allowed
  if (allowed) {
    ipRateLimit.count++;
    loginRateLimitMap.set(ipAddress, ipRateLimit);
  }

  const remaining = Math.max(0, LOGIN_MAX_ATTEMPTS - ipRateLimit.count);
  const resetAt = new Date(ipRateLimit.timestamp + LOGIN_TIME_WINDOW);

  return { allowed, remaining, resetAt };
}

/**
 * Validate request origin (CSRF protection)
 */
export function validateRequestOrigin(request: NextRequest): boolean {
  const referer = request.headers.get('referer');
  const origin = request.headers.get('origin');
  const host = request.headers.get('host');

  if (!referer || !host || !referer.includes(host)) {
    console.warn('Possible CSRF attempt:', { referer, origin, host });
    return false;
  }

  return true;
}

/**
 * Log security event
 */
export function logSecurityEvent(
  eventType: 'login_success' | 'login_failure' | 'admin_action' | 'security_violation',
  userId: string | null,
  details: Record<string, unknown>
): void {
  // In a production app, you would log to a database or external service
  console.log(`[SECURITY EVENT] ${eventType}`, {
    timestamp: new Date().toISOString(),
    userId,
    ...details
  });
}