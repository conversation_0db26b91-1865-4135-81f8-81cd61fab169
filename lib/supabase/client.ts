import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Ensure environment variables are properly set
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Validate environment variables
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
}

// Create the client-side Supabase client
const supabase = createClient<Database>(
  supabaseUrl || '',
  supabaseAnonKey || '',
  {
    auth: {
      persistSession: true, // Enable persistent sessions
      autoRefreshToken: true, // Auto-refresh tokens
      detectSessionInUrl: true, // Detect session from URL query parameters
      // Use a consistent storage key for cookies
      storageKey: 'sb-auth-token',
    }
  }
);

export default supabase;