export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      activities: {
        Row: {
          activity_type: string | null
          company_id: string | null
          contact_id: string | null
          created_at: string | null
          date: string | null
          details: string | null
          id: string
          name: string
          project_id: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          activity_type?: string | null
          company_id?: string | null
          contact_id?: string | null
          created_at?: string | null
          date?: string | null
          details?: string | null
          id?: string
          name: string
          project_id?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          activity_type?: string | null
          company_id?: string | null
          contact_id?: string | null
          created_at?: string | null
          date?: string | null
          details?: string | null
          id?: string
          name?: string
          project_id?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "activities_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activities_contact_id_fkey"
            columns: ["contact_id"]
            isOneToOne: false
            referencedRelation: "contacts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activities_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
      client_companies: {
        Row: {
          company_id: string
          created_at: string | null
          id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          company_id: string
          created_at?: string | null
          id?: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          company_id?: string
          created_at?: string | null
          id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "client_companies_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      client_portals: {
        Row: {
          access_token: string | null
          company_id: string | null
          created_at: string | null
          id: string
          is_active: boolean | null
          last_accessed: string | null
          name: string
          updated_at: string | null
        }
        Insert: {
          access_token?: string | null
          company_id?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          last_accessed?: string | null
          name: string
          updated_at?: string | null
        }
        Update: {
          access_token?: string | null
          company_id?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          last_accessed?: string | null
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "client_portals_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      companies: {
        Row: {
          address: string | null
          created_at: string | null
          id: string
          industry: string | null
          logo_url: string | null
          name: string
          notes: string | null
          size: string | null
          status: string | null
          updated_at: string | null
          website: string | null
        }
        Insert: {
          address?: string | null
          created_at?: string | null
          id?: string
          industry?: string | null
          logo_url?: string | null
          name: string
          notes?: string | null
          size?: string | null
          status?: string | null
          updated_at?: string | null
          website?: string | null
        }
        Update: {
          address?: string | null
          created_at?: string | null
          id?: string
          industry?: string | null
          logo_url?: string | null
          name?: string
          notes?: string | null
          size?: string | null
          status?: string | null
          updated_at?: string | null
          website?: string | null
        }
        Relationships: []
      }
      contacts: {
        Row: {
          company_id: string | null
          created_at: string | null
          email: string | null
          id: string
          is_primary: boolean | null
          name: string
          notes: string | null
          phone: string | null
          position: string | null
          updated_at: string | null
        }
        Insert: {
          company_id?: string | null
          created_at?: string | null
          email?: string | null
          id?: string
          is_primary?: boolean | null
          name: string
          notes?: string | null
          phone?: string | null
          position?: string | null
          updated_at?: string | null
        }
        Update: {
          company_id?: string | null
          created_at?: string | null
          email?: string | null
          id?: string
          is_primary?: boolean | null
          name?: string
          notes?: string | null
          phone?: string | null
          position?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "contacts_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      credentials: {
        Row: {
          api_key: string | null
          api_secret: string | null
          auth_method: string | null
          company_id: string | null
          created_at: string | null
          credential_type: string | null
          encrypted_password: string | null
          environment: string | null
          expiry_date: string | null
          id: string
          is_active: boolean | null
          last_verified_at: string | null
          name: string
          notes: string | null
          oauth_client_id: string | null
          oauth_provider: string | null
          oauth_scopes: string | null
          password: string | null
          password_last_updated: string | null
          project_id: string | null
          updated_at: string | null
          url: string | null
          username: string | null
          verification_status: string | null
        }
        Insert: {
          api_key?: string | null
          api_secret?: string | null
          auth_method?: string | null
          company_id?: string | null
          created_at?: string | null
          credential_type?: string | null
          encrypted_password?: string | null
          environment?: string | null
          expiry_date?: string | null
          id?: string
          is_active?: boolean | null
          last_verified_at?: string | null
          name: string
          notes?: string | null
          oauth_client_id?: string | null
          oauth_provider?: string | null
          oauth_scopes?: string | null
          password?: string | null
          password_last_updated?: string | null
          project_id?: string | null
          updated_at?: string | null
          url?: string | null
          username?: string | null
          verification_status?: string | null
        }
        Update: {
          api_key?: string | null
          api_secret?: string | null
          auth_method?: string | null
          company_id?: string | null
          created_at?: string | null
          credential_type?: string | null
          encrypted_password?: string | null
          environment?: string | null
          expiry_date?: string | null
          id?: string
          is_active?: boolean | null
          last_verified_at?: string | null
          name?: string
          notes?: string | null
          oauth_client_id?: string | null
          oauth_provider?: string | null
          oauth_scopes?: string | null
          password?: string | null
          password_last_updated?: string | null
          project_id?: string | null
          updated_at?: string | null
          url?: string | null
          username?: string | null
          verification_status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "credentials_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "credentials_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
      expenses: {
        Row: {
          amount: number
          category: string | null
          company_id: string | null
          created_at: string | null
          date: string | null
          id: string
          is_reimbursable: boolean | null
          name: string
          notes: string | null
          payment_method: string | null
          project_id: string | null
          receipt_url: string | null
          reimbursement_status: string | null
          updated_at: string | null
        }
        Insert: {
          amount: number
          category?: string | null
          company_id?: string | null
          created_at?: string | null
          date?: string | null
          id?: string
          is_reimbursable?: boolean | null
          name: string
          notes?: string | null
          payment_method?: string | null
          project_id?: string | null
          receipt_url?: string | null
          reimbursement_status?: string | null
          updated_at?: string | null
        }
        Update: {
          amount?: number
          category?: string | null
          company_id?: string | null
          created_at?: string | null
          date?: string | null
          id?: string
          is_reimbursable?: boolean | null
          name?: string
          notes?: string | null
          payment_method?: string | null
          project_id?: string | null
          receipt_url?: string | null
          reimbursement_status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "expenses_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "expenses_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
      invoice_items: {
        Row: {
          amount: number
          description: string
          expense_id: string | null
          id: string
          invoice_id: string | null
          quantity: number | null
          service_id: string | null
          unit_price: number
        }
        Insert: {
          amount: number
          description: string
          expense_id?: string | null
          id?: string
          invoice_id?: string | null
          quantity?: number | null
          service_id?: string | null
          unit_price: number
        }
        Update: {
          amount?: number
          description?: string
          expense_id?: string | null
          id?: string
          invoice_id?: string | null
          quantity?: number | null
          service_id?: string | null
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "invoice_items_expense_id_fkey"
            columns: ["expense_id"]
            isOneToOne: false
            referencedRelation: "expenses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoice_items_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoice_items_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          amount: number
          company_id: string | null
          created_at: string | null
          due_date: string
          id: string
          invoice_number: string
          issue_date: string
          notes: string | null
          payment_date: string | null
          pdf_url: string | null
          project_id: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          amount: number
          company_id?: string | null
          created_at?: string | null
          due_date: string
          id?: string
          invoice_number: string
          issue_date: string
          notes?: string | null
          payment_date?: string | null
          pdf_url?: string | null
          project_id?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          amount?: number
          company_id?: string | null
          created_at?: string | null
          due_date?: string
          id?: string
          invoice_number?: string
          issue_date?: string
          notes?: string | null
          payment_date?: string | null
          pdf_url?: string | null
          project_id?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "invoices_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
      project_services: {
        Row: {
          price: number | null
          project_id: string
          quantity: number | null
          service_id: string
        }
        Insert: {
          price?: number | null
          project_id: string
          quantity?: number | null
          service_id: string
        }
        Update: {
          price?: number | null
          project_id?: string
          quantity?: number | null
          service_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "project_services_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "project_services_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
        ]
      }
      projects: {
        Row: {
          actual_end_date: string | null
          company_id: string | null
          created_at: string | null
          description: string | null
          expected_end_date: string | null
          id: string
          name: string
          notes: string | null
          phase: string | null
          start_date: string | null
          status: string | null
          updated_at: string | null
          value: number | null
        }
        Insert: {
          actual_end_date?: string | null
          company_id?: string | null
          created_at?: string | null
          description?: string | null
          expected_end_date?: string | null
          id?: string
          name: string
          notes?: string | null
          phase?: string | null
          start_date?: string | null
          status?: string | null
          updated_at?: string | null
          value?: number | null
        }
        Update: {
          actual_end_date?: string | null
          company_id?: string | null
          created_at?: string | null
          description?: string | null
          expected_end_date?: string | null
          id?: string
          name?: string
          notes?: string | null
          phase?: string | null
          start_date?: string | null
          status?: string | null
          updated_at?: string | null
          value?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "projects_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      services: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean | null
          name: string
          price: number | null
          service_type: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          price?: number | null
          service_type?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          price?: number | null
          service_type?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      shared_credentials: {
        Row: {
          created_at: string | null
          credential_id: string
          id: string
          is_shared_with_client: boolean | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          credential_id: string
          id?: string
          is_shared_with_client?: boolean | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          credential_id?: string
          id?: string
          is_shared_with_client?: boolean | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "shared_credentials_credential_id_fkey"
            columns: ["credential_id"]
            isOneToOne: false
            referencedRelation: "credentials"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "shared_credentials_credential_id_fkey"
            columns: ["credential_id"]
            isOneToOne: false
            referencedRelation: "decrypted_credentials"
            referencedColumns: ["id"]
          },
        ]
      }
      shared_tasks: {
        Row: {
          created_at: string | null
          id: string
          is_shared_with_client: boolean | null
          task_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_shared_with_client?: boolean | null
          task_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_shared_with_client?: boolean | null
          task_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "shared_tasks_task_id_fkey"
            columns: ["task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
        ]
      }
      tasks: {
        Row: {
          assigned_to: string | null
          created_at: string | null
          description: string | null
          due_date: string | null
          id: string
          name: string
          priority: string | null
          project_id: string | null
          status: string | null
          task_type: string | null
          updated_at: string | null
        }
        Insert: {
          assigned_to?: string | null
          created_at?: string | null
          description?: string | null
          due_date?: string | null
          id?: string
          name: string
          priority?: string | null
          project_id?: string | null
          status?: string | null
          task_type?: string | null
          updated_at?: string | null
        }
        Update: {
          assigned_to?: string | null
          created_at?: string | null
          description?: string | null
          due_date?: string | null
          id?: string
          name?: string
          priority?: string | null
          project_id?: string | null
          status?: string | null
          task_type?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tasks_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
      user_roles: {
        Row: {
          created_at: string | null
          id: string
          role: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          role: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          role?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      client_dashboard_summary: {
        Row: {
          active_projects: number | null
          company_id: string | null
          company_name: string | null
          pending_tasks: number | null
          total_outstanding: number | null
          total_projects: number | null
          unpaid_invoices: number | null
        }
        Relationships: [
          {
            foreignKeyName: "client_companies_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      dashboard_summary: {
        Row: {
          active_projects: number | null
          expiring_credentials: number | null
          pending_tasks: number | null
          total_companies: number | null
          total_outstanding: number | null
          unpaid_invoices: number | null
          upcoming_tasks: number | null
        }
        Relationships: []
      }
      decrypted_credentials: {
        Row: {
          company_id: string | null
          created_at: string | null
          credential_type: string | null
          expiry_date: string | null
          id: string | null
          name: string | null
          notes: string | null
          password: string | null
          password_last_updated: string | null
          project_id: string | null
          updated_at: string | null
          url: string | null
          username: string | null
        }
        Insert: {
          company_id?: string | null
          created_at?: string | null
          credential_type?: string | null
          expiry_date?: string | null
          id?: string | null
          name?: string | null
          notes?: string | null
          password?: never
          password_last_updated?: string | null
          project_id?: string | null
          updated_at?: string | null
          url?: string | null
          username?: string | null
        }
        Update: {
          company_id?: string | null
          created_at?: string | null
          credential_type?: string | null
          expiry_date?: string | null
          id?: string | null
          name?: string | null
          notes?: string | null
          password?: never
          password_last_updated?: string | null
          project_id?: string | null
          updated_at?: string | null
          url?: string | null
          username?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "credentials_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "credentials_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      add_admin_role: {
        Args: { p_user_id: string }
        Returns: undefined
      }
      add_team_member_role: {
        Args: { p_user_id: string }
        Returns: undefined
      }
      create_client_portal: {
        Args: { p_company_id: string; p_name: string }
        Returns: string
      }
      create_client_user: {
        Args: { p_email: string; p_password: string; p_company_id: string }
        Returns: string
      }
      decrypt_password: {
        Args: { encrypted_password: string }
        Returns: string
      }
      encrypt_password: {
        Args: { password: string }
        Returns: string
      }
      remove_user_role: {
        Args: { p_user_id: string; p_role: string }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export interface Credential {
  id: string;
  name: string | null;
  username: string | null;
  password_decrypted?: string | null;
  url: string | null;
  notes: string | null;
  credential_type: string | null;
  company_id?: string | null;
  project_id?: string | null;
  expiry_date?: string | null;
  // New enhanced fields
  auth_method?: string | null;
  api_key?: string | null;
  api_secret?: string | null;
  oauth_provider?: string | null;
  oauth_client_id?: string | null;
  oauth_scopes?: string | null;
  environment?: string | null;
  is_active?: boolean | null;
  last_verified_at?: string | null;
  verification_status?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
}
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type InsertTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type UpdateTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T]
export type Views<T extends keyof Database['public']['Views']> = Database['public']['Views'][T]['Row']