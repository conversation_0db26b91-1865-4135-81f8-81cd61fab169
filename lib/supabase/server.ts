'use server'

import { createClient as createSupabaseClient } from '@supabase/supabase-js'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { Database } from './types'

export async function createClient() {
  // Ensure environment variables are properly set
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Missing Supabase environment variables. Please check your .env file.');
  }

  const cookieStore = cookies();

  return createServerClient(
    supabaseUrl as string,
    supabaseAnonKey as string,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: { expires?: Date; path?: string }) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: { path?: string }) {
          cookieStore.set(name, '', { ...options, maxAge: 0 });
        },
      }
    }
  )
}

// Create a Supabase client with service role (for server-side operations)
export async function createAdminClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Missing Supabase environment variables for admin client.');
    throw new Error('Missing Supabase environment variables for admin client');
  }

  return createSupabaseClient(
    supabaseUrl as string,
    supabaseServiceKey as string,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}