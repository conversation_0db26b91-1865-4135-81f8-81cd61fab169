import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) {
    return 'N/A';
  }
  const date = new Date(dateString);
  // Ensure the date is valid before formatting
  if (isNaN(date.getTime())) {
    return 'N/A';
  }
  return date.toLocaleDateString('en-AU', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

export function getStatusBadgeClass(status: string | null): string {
  if (!status) return 'bg-gray-100 text-gray-800'; // Default for null, undefined or empty status
  const lowerStatus = status.toLowerCase();
  switch (lowerStatus) {
    case 'active':
    case 'in progress':
    case 'open':
      return 'bg-green-100 text-green-800';
    case 'pending':
    case 'on hold':
      return 'bg-yellow-100 text-yellow-800';
    case 'completed':
    case 'closed':
    case 'resolved':
      return 'bg-blue-100 text-blue-800';
    case 'cancelled':
    case 'failed':
    case 'error':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

export function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

export function maskSensitiveText(text: string | null | undefined, visibleChars: number = 0): string {
  if (!text) return '••••••••'; // Default for null/undefined
  if (text.length <= visibleChars * 2) return '••••'; // If too short, just mask all

  const prefix = text.substring(0, visibleChars);
  const suffix = text.substring(text.length - visibleChars);
  const maskedPart = '•'.repeat(Math.max(4, text.length - visibleChars * 2)); // Ensure at least 4 dots

  return visibleChars > 0 ? `${prefix}${maskedPart}${suffix}` : maskedPart;
}

export function getInitials(name: string | null | undefined): string {
  if (!name) return '??';
  const words = name.split(' ').filter(Boolean);
  if (words.length === 0) return '??';
  if (words.length === 1) return words[0].substring(0, 2).toUpperCase();
  return (words[0][0] + (words[words.length - 1][0] || '')).toUpperCase();
}

export function getStatusColor(status: string | null): string {
  if (!status) return 'gray';
  const lowerStatus = status.toLowerCase();
  switch (lowerStatus) {
    case 'active':
    case 'in progress':
    case 'open':
      return 'green';
    case 'pending':
    case 'on hold':
      return 'yellow';
    case 'completed':
    case 'closed':
    case 'resolved':
      return 'blue';
    case 'cancelled':
    case 'failed':
    case 'error':
      return 'red';
    default:
      return 'gray';
  }
}
