/**
 * Utility functions for exporting data to various formats
 */

/**
 * Convert an array of objects to CSV format
 * @param data Array of objects to convert
 * @param headers Optional custom headers (if not provided, will use object keys)
 * @returns CSV string
 */
export function convertToCSV<T extends Record<string, any>>(
  data: T[],
  headers?: { key: keyof T; label: string }[]
): string {
  if (!data || !data.length) return '';

  // If headers not provided, use object keys
  const keys = headers ? headers.map(h => h.key) : Object.keys(data[0]) as (keyof T)[];
  const headerRow = headers 
    ? headers.map(h => h.label) 
    : keys.map(k => String(k));

  // Create CSV header row
  let csv = headerRow.join(',') + '\n';

  // Add data rows
  data.forEach(item => {
    const row = keys.map(key => {
      const value = item[key];
      
      // Handle different value types
      if (value === null || value === undefined) return '';
      if (typeof value === 'string') {
        // Escape quotes and wrap in quotes if contains comma or newline
        const escaped = value.replace(/"/g, '""');
        return (escaped.includes(',') || escaped.includes('\n')) 
          ? `"${escaped}"` 
          : escaped;
      }
      if (typeof value === 'object' && value instanceof Date) {
        return value.toISOString();
      }
      return String(value);
    });
    
    csv += row.join(',') + '\n';
  });

  return csv;
}

/**
 * Download data as a CSV file
 * @param data Array of objects to export
 * @param filename Filename for the downloaded file
 * @param headers Optional custom headers
 */
export function downloadCSV<T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: { key: keyof T; label: string }[]
): void {
  if (!data || !data.length) return;
  
  const csv = convertToCSV(data, headers);
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Format date for export
 * @param date Date string or Date object
 * @returns Formatted date string (YYYY-MM-DD)
 */
export function formatDateForExport(date: string | Date | null | undefined): string {
  if (!date) return '';
  
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toISOString().split('T')[0];
}
