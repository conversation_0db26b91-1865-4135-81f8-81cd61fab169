/**
 * This module provides encryption and decryption utilities for sensitive data
 * It uses the Web Crypto API to securely encrypt and decrypt passwords and other sensitive information
 */

// Generate a random initialization vector (IV)
function generateIV(): Uint8Array {
  return crypto.getRandomValues(new Uint8Array(12)); // 12 bytes for AES-GCM
}

// Convert string to ArrayBuffer
function str2ab(str: string): Uint8Array {
  const enc = new TextEncoder();
  return enc.encode(str);
}

// Convert ArrayBuffer to string
function ab2str(buffer: ArrayBuffer): string {
  const dec = new TextDecoder();
  return dec.decode(buffer);
}

// Convert ArrayBuffer to Base64 string
function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}

// Convert Base64 string to ArrayBuffer
function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes.buffer;
}

// Derive a key from the master key/password
async function deriveKey(masterKey: string): Promise<CryptoKey> {
  const encoder = new TextEncoder();
  const keyMaterial = await crypto.subtle.importKey(
    'raw',
    encoder.encode(masterKey),
    { name: 'PBKDF2' },
    false,
    ['deriveKey']
  );
  
  // Use a salt (should be stored securely in production)
  const salt = str2ab('myCRMSecureSalt'); // In production, use a proper random salt stored securely
  
  // Derive an AES-GCM key
  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt,
      iterations: 100000,
      hash: 'SHA-256'
    },
    keyMaterial,
    { name: 'AES-GCM', length: 256 },
    false,
    ['encrypt', 'decrypt']
  );
}

/**
 * Encrypt sensitive data
 * @param plaintext The plain text data to encrypt
 * @returns Promise resolving to the encrypted data in format: base64(iv):base64(ciphertext)
 */
export async function encryptData(plaintext: string): Promise<string> {
  try {
    // Use environment variable for master key in production
    const masterKey = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'defaultDevKey123!';
    const key = await deriveKey(masterKey);
    const iv = generateIV();
    
    // Encrypt the data
    const encryptedData = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      key,
      str2ab(plaintext)
    );
    
    // Format as iv:ciphertext (both base64 encoded)
    // Convert Uint8Array to base64 by creating a separate conversion for Uint8Array
    let ivBase64 = '';
    for (let i = 0; i < iv.length; i++) {
      ivBase64 += String.fromCharCode(iv[i]);
    }
    ivBase64 = btoa(ivBase64);
    
    const encryptedBase64 = arrayBufferToBase64(encryptedData);
    
    return `${ivBase64}:${encryptedBase64}`;
  } catch (error) {
    console.error('Encryption failed:', error);
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Decrypt sensitive data
 * @param encryptedData The encrypted data in format: base64(iv):base64(ciphertext)
 * @returns Promise resolving to the decrypted plaintext
 */
export async function decryptData(encryptedData: string): Promise<string> {
  try {
    const [ivBase64, encryptedBase64] = encryptedData.split(':');
    if (!ivBase64 || !encryptedBase64) {
      throw new Error('Invalid encrypted data format');
    }
    
    // Use environment variable for master key in production
    const masterKey = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'defaultDevKey123!';
    const key = await deriveKey(masterKey);
    
    // Convert from base64
    const iv = base64ToArrayBuffer(ivBase64);
    const ciphertext = base64ToArrayBuffer(encryptedBase64);
    
    // Decrypt the data
    const decryptedData = await crypto.subtle.decrypt(
      { name: 'AES-GCM', iv: new Uint8Array(iv) },
      key,
      ciphertext
    );
    
    return ab2str(decryptedData);
  } catch (error) {
    console.error('Decryption failed:', error);
    throw new Error('Failed to decrypt data');
  }
}

/**
 * Check if a string is encrypted
 * Simple check for the expected format
 */
export function isEncryptedData(data: string): boolean {
  if (!data) return false;
  const parts = data.split(':');
  return parts.length === 2 && parts[0].length > 0 && parts[1].length > 0;
} 