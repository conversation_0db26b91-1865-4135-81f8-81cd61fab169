'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  ArrowLeft,
  Edit,
  Trash2,
  Save,
  Eye,
  EyeOff,
  Copy,
  ExternalLink,
  Calendar,
  Building,
  Briefcase
} from 'lucide-react'
import { useSupabase } from '../../components/SupabaseProvider'
import { formatDate } from '../../../lib/utils'

interface Credential {
  id: string
  name: string
  username: string
  password: string
  url: string | null
  notes: string | null
  credential_type: string | null
  expiry_date: string | null
  company_id: string | null
  project_id: string | null
  created_at: string
  updated_at: string
  companies?: {
    id: string
    name: string
  } | null
  projects?: {
    id: string
    name: string
  } | null
}

interface Company {
  id: string
  name: string
}

interface Project {
  id: string
  name: string
  company_id: string | null
}

export default function CredentialDetail({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { supabase } = useSupabase()
  const [credential, setCredential] = useState<Credential | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [companies, setCompanies] = useState<Company[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([])
  const [showPassword, setShowPassword] = useState(false)
  const [copySuccess, setCopySuccess] = useState('')
  
  // Form state
  const [name, setName] = useState('')
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [url, setUrl] = useState('')
  const [notes, setNotes] = useState('')
  const [credentialType, setCredentialType] = useState('')
  const [expiryDate, setExpiryDate] = useState('')
  const [companyId, setCompanyId] = useState('')
  const [projectId, setProjectId] = useState('')
  
  // Fetch credential data
  useEffect(() => {
    async function fetchCredential() {
      setIsLoading(true)
      setError(null)
      
      try {
        const { data, error } = await supabase
          .from('credentials')
          .select(`
            *,
            companies:company_id (id, name),
            projects:project_id (id, name)
          `)
          .eq('id', params.id)
          .single()
        
        if (error) throw error
        
        setCredential(data)
        
        // Initialize form state
        setName(data.name)
        setUsername(data.username)
        setPassword(data.password)
        setUrl(data.url || '')
        setNotes(data.notes || '')
        setCredentialType(data.credential_type || '')
        setExpiryDate(data.expiry_date || '')
        setCompanyId(data.company_id || '')
        setProjectId(data.project_id || '')
      } catch (err) {
        console.error('Error fetching credential:', err)
        setError('Failed to load credential details')
      } finally {
        setIsLoading(false)
      }
    }
    
    // Fetch companies and projects
    async function fetchRelatedData() {
      try {
        // Fetch companies
        const { data: companiesData, error: companiesError } = await supabase
          .from('companies')
          .select('id, name')
          .order('name')
        
        if (companiesError) {
          console.error('Error fetching companies:', companiesError)
        } else {
          setCompanies(companiesData || [])
        }
        
        // Fetch projects
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select('id, name, company_id')
          .order('name')
        
        if (projectsError) {
          console.error('Error fetching projects:', projectsError)
        } else {
          setProjects(projectsData || [])
        }
      } catch (err) {
        console.error('Unexpected error:', err)
      }
    }
    
    fetchCredential()
    fetchRelatedData()
  }, [supabase, params.id])
  
  // Filter projects when company changes
  useEffect(() => {
    if (companyId) {
      setFilteredProjects(projects.filter(project => project.company_id === companyId))
    } else {
      setFilteredProjects(projects)
    }
    
    // Reset project selection if company changes and not in initial load
    if (isEditing && credential?.company_id !== companyId) {
      setProjectId('')
    }
  }, [companyId, projects, isEditing, credential])
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    setError(null)
    
    try {
      // Validate form
      if (!name || !username || !password) {
        setError('Name, username, and password are required')
        setIsSaving(false)
        return
      }
      
      // Update credential record
      const { error: updateError } = await supabase
        .from('credentials')
        .update({
          name,
          username,
          password,
          url: url || null,
          notes: notes || null,
          credential_type: credentialType || null,
          expiry_date: expiryDate || null,
          company_id: companyId || null,
          project_id: projectId || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', params.id)
      
      if (updateError) throw updateError
      
      // Fetch updated credential
      const { data: updatedCredential, error: fetchError } = await supabase
        .from('credentials')
        .select(`
          *,
          companies:company_id (id, name),
          projects:project_id (id, name)
        `)
        .eq('id', params.id)
        .single()
      
      if (fetchError) {
        console.error('Error fetching updated credential:', fetchError)
      } else {
        setCredential(updatedCredential)
      }
      
      setIsEditing(false)
    } catch (err) {
      console.error('Error updating credential:', err)
      setError('Failed to update credential')
    } finally {
      setIsSaving(false)
    }
  }
  
  // Handle credential deletion
  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this credential? This action cannot be undone.')) {
      return
    }
    
    setIsDeleting(true)
    
    try {
      const { error } = await supabase
        .from('credentials')
        .delete()
        .eq('id', params.id)
      
      if (error) throw error
      
      // Redirect to credentials list
      router.push('/credentials')
    } catch (err) {
      console.error('Error deleting credential:', err)
      setError('Failed to delete credential')
      setIsDeleting(false)
    }
  }
  
  // Copy to clipboard function
  const copyToClipboard = (text: string, field: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        setCopySuccess(`${field} copied!`)
        setTimeout(() => setCopySuccess(''), 2000)
      },
      () => {
        setCopySuccess('Failed to copy')
      }
    )
  }
  
  // Credential types
  const credentialTypes = [
    'Website Login',
    'API Key',
    'Database',
    'Server',
    'Email',
    'SSH Key',
    'FTP',
    'CMS',
    'Social Media',
    'Other'
  ]
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }
  
  if (error && !credential) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
        {error}
        <div className="mt-4">
          <Link href="/credentials" className="text-red-700 underline">
            Return to credentials list
          </Link>
        </div>
      </div>
    )
  }
  
  if (!credential) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md">
        Credential not found
        <div className="mt-4">
          <Link href="/credentials" className="text-yellow-700 underline">
            Return to credentials list
          </Link>
        </div>
      </div>
    )
  }
  
  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/credentials" className="mr-4 p-2 rounded-full hover:bg-gray-100">
            <ArrowLeft className="h-5 w-5 text-gray-500" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {isEditing ? 'Edit Credential' : credential.name}
            </h1>
            <p className="text-gray-600">
              {credential.credential_type || 'Credential'}
            </p>
          </div>
        </div>
        
        <div className="flex space-x-2">
          {!isEditing ? (
            <>
              <button
                onClick={() => setIsEditing(true)}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-sm font-medium text-white flex items-center"
              >
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </button>
              <button
                onClick={handleDelete}
                disabled={isDeleting}
                className="px-3 py-2 bg-red-600 hover:bg-red-700 rounded-md text-sm font-medium text-white flex items-center"
              >
                {isDeleting ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                ) : (
                  <Trash2 className="h-4 w-4 mr-1" />
                )}
                Delete
              </button>
            </>
          ) : (
            <button
              onClick={() => setIsEditing(false)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
          )}
        </div>
      </div>
      
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}
      
      {copySuccess && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
          {copySuccess}
        </div>
      )}
      
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        {isEditing ? (
          <form onSubmit={handleSubmit} className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Credential Name */}
              <div className="col-span-2">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Credential Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., Company Website, AWS Console, etc."
                  required
                />
              </div>
              
              {/* Username */}
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                  Username <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Username or email"
                  required
                />
              </div>
              
              {/* Password */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Password or key"
                    required
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
              
              {/* URL */}
              <div>
                <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-1">
                  URL
                </label>
                <input
                  type="url"
                  id="url"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://example.com"
                />
              </div>
              
              {/* Credential Type */}
              <div>
                <label htmlFor="credentialType" className="block text-sm font-medium text-gray-700 mb-1">
                  Credential Type
                </label>
                <select
                  id="credentialType"
                  value={credentialType}
                  onChange={(e) => setCredentialType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a type</option>
                  {credentialTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Expiry Date */}
              <div>
                <label htmlFor="expiryDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Expiry Date
                </label>
                <input
                  type="date"
                  id="expiryDate"
                  value={expiryDate}
                  onChange={(e) => setExpiryDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              {/* Company */}
              <div>
                <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
                  Company
                </label>
                <select
                  id="company"
                  value={companyId}
                  onChange={(e) => setCompanyId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a company</option>
                  {companies.map((company) => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Project */}
              <div>
                <label htmlFor="project" className="block text-sm font-medium text-gray-700 mb-1">
                  Project
                </label>
                <select
                  id="project"
                  value={projectId}
                  onChange={(e) => setProjectId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!companyId}
                >
                  <option value="">Select a project</option>
                  {filteredProjects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
                {!companyId && (
                  <p className="mt-1 text-xs text-gray-500">Select a company first</p>
                )}
              </div>
              
              {/* Notes */}
              <div className="col-span-2">
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add any additional notes about this credential..."
                ></textarea>
              </div>
            </div>
            
            <div className="mt-8 flex justify-end">
              <button
                type="submit"
                disabled={isSaving}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-sm font-medium text-white flex items-center"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-1" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </form>
        ) : (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="col-span-2 md:col-span-1">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Credential Details</h2>
                
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Username</p>
                    <div className="flex items-center mt-1">
                      <p className="text-gray-900 mr-2">{credential.username}</p>
                      <button
                        onClick={() => copyToClipboard(credential.username, 'Username')}
                        className="text-gray-400 hover:text-gray-600"
                        title="Copy username"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">Password</p>
                    <div className="flex items-center mt-1">
                      <div className="relative flex-grow">
                        <input
                          type={showPassword ? "text" : "password"}
                          value={credential.password}
                          readOnly
                          className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md bg-gray-50"
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="h-5 w-5 text-gray-400" />
                          ) : (
                            <Eye className="h-5 w-5 text-gray-400" />
                          )}
                        </button>
                      </div>
                      <button
                        onClick={() => copyToClipboard(credential.password, 'Password')}
                        className="ml-2 text-gray-400 hover:text-gray-600"
                        title="Copy password"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  
                  {credential.url && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">URL</p>
                      <div className="flex items-center mt-1">
                        <a
                          href={credential.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 flex items-center"
                        >
                          {credential.url}
                          <ExternalLink className="h-3 w-3 ml-1" />
                        </a>
                        <button
                          onClick={() => copyToClipboard(credential.url, 'URL')}
                          className="ml-2 text-gray-400 hover:text-gray-600"
                          title="Copy URL"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {credential.credential_type && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Type</p>
                      <p className="text-gray-900">{credential.credential_type}</p>
                    </div>
                  )}
                  
                  {credential.expiry_date && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Expiry Date</p>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                        <p className="text-gray-900">{formatDate(credential.expiry_date)}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="col-span-2 md:col-span-1">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Related Information</h2>
                
                <div className="space-y-4">
                  {credential.companies && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Company</p>
                      <div className="flex items-center mt-1">
                        <Building className="h-4 w-4 text-gray-400 mr-1" />
                        <Link href={`/companies/${credential.companies.id}`} className="text-blue-600 hover:text-blue-800">
                          {credential.companies.name}
                        </Link>
                      </div>
                    </div>
                  )}
                  
                  {credential.projects && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Project</p>
                      <div className="flex items-center mt-1">
                        <Briefcase className="h-4 w-4 text-gray-400 mr-1" />
                        <Link href={`/projects/${credential.projects.id}`} className="text-blue-600 hover:text-blue-800">
                          {credential.projects.name}
                        </Link>
                      </div>
                    </div>
                  )}
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">Created</p>
                    <p className="text-gray-900">{formatDate(credential.created_at)}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">Last Updated</p>
                    <p className="text-gray-900">{formatDate(credential.updated_at)}</p>
                  </div>
                </div>
              </div>
              
              {credential.notes && (
                <div className="col-span-2">
                  <h2 className="text-lg font-medium text-gray-900 mb-2">Notes</h2>
                  <div className="bg-gray-50 p-4 rounded-md">
                    <p className="text-gray-900 whitespace-pre-wrap">{credential.notes}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
