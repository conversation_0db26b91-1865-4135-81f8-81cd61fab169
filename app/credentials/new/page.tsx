'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { ArrowLeft, Eye, EyeOff } from 'lucide-react'
import Link from 'next/link'
import { createBrowserClient } from '@supabase/ssr'
import { encryptData } from '../../../lib/encryption'

interface Company {
  id: string
  name: string
}

interface Project {
  id: string
  name: string
  company_id: string;
}

export default function NewCredential() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [companies, setCompanies] = useState<Company[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showPassword, setShowPassword] = useState(false)
  const [isPasswordDisabled, setIsPasswordDisabled] = useState(false)

  const [formData, setFormData] = useState({
    name: '',
    username: '',
    password: '',
    credential_type: 'website',
    url: '',
    expiry_date: '',
    company_id: '',
    project_id: '',
  })

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true)

      try {
        // Create a Supabase client
        const supabase = createBrowserClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
        )

        // Fetch companies
        const { data: companiesData, error: companiesError } = await supabase
          .from('companies')
          .select('id, name')
          .order('name')

        if (companiesError) throw companiesError

        // Fetch projects
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select('id, name, company_id')
          .order('name')

        if (projectsError) throw projectsError

        setCompanies(companiesData || [])
        setProjects(projectsData || [])

      } catch (err) {
        console.error('Error fetching data:', err)
        setError('Failed to load companies and projects')
      }

      setIsLoading(false)
    }

    fetchData()
  }, [])

  // Filter projects when company changes
  useEffect(() => {
    if (formData.company_id) {
      const filtered = projects.filter(project => project.company_id === formData.company_id)
      setFilteredProjects(filtered)

      // Reset project selection if current selection is not for this company
      if (formData.project_id) {
        const projectExists = filtered.some(p => p.id === formData.project_id)
        if (!projectExists) {
          setFormData(prev => ({...prev, project_id: ''}))
        }
      }
    } else {
      setFilteredProjects([])
      setFormData(prev => ({...prev, project_id: ''}))
    }
  }, [formData.company_id, projects])

  // Effect to handle password field state based on credential type
  useEffect(() => {
    if (formData.credential_type === 'oauth') {
      setFormData(prev => ({ ...prev, password: '' }))
      setIsPasswordDisabled(true)
    } else {
      setIsPasswordDisabled(false)
    }
  }, [formData.credential_type])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)

    if (!formData.company_id) {
      setError('Please select a company')
      setIsSubmitting(false)
      return
    }

    try {
      // Encrypt password before storing
      const encryptedPassword = await encryptData(formData.password)

      const credentialData = {
        ...formData,
        password: encryptedPassword,
        // Convert empty string to null for project_id
        project_id: formData.project_id || null,
        // Convert empty string to null for expiry_date
        expiry_date: formData.expiry_date || null,
      }

      // Create a Supabase client
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )

console.log('Submitting credentialData:', JSON.stringify(credentialData, null, 2)) // Roo Debug Log
      const { error: insertError } = await supabase
        .from('credentials')
        .insert([credentialData])

      if (insertError) throw insertError
console.error('Supabase insert error:', JSON.stringify(insertError, null, 2)) // Roo Debug Log

      // Navigate to credentials list
      router.push('/credentials')
      router.refresh()
    } catch (err: unknown) {
      console.error('handleSubmit catch block error details:')
      if (err && typeof err === 'object') {
        for (const key in err) {
          // @ts-expect-error If err is a generic object, this allows inspection
          console.error(`${key}:`, err[key]);
        }
      } else {
        console.error(err)
      }
      const errorMessage = err instanceof Error ? err.message : 'Failed to save credential'
      setError(errorMessage)
      setIsSubmitting(false)
    }
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  const credentialTypes = [
    { value: 'website', label: 'Website Login' },
    { value: 'database', label: 'Database' },
    { value: 'api', label: 'API Key' },
    { value: 'server', label: 'Server' },
    { value: 'vpn', label: 'VPN' },
    { value: 'email', label: 'Email' },
    { value: 'oauth', label: 'OAuth / External Auth' },
    { value: 'other', label: 'Other' }
  ]

  return (
    <div className="max-w-5xl">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/credentials"
            className="mr-4 p-2 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft className="h-5 w-5 text-gray-500" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Add New Credential</h1>
            <p className="text-gray-600">Securely store client access credentials</p>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="col-span-2">
              <label htmlFor="company_id" className="block text-sm font-medium text-gray-700 mb-1">
                Company <span className="text-red-500">*</span>
              </label>
              <select
                id="company_id"
                name="company_id"
                required
                value={formData.company_id}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="">Select a company</option>
                {companies.map(company => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="col-span-2">
              <label htmlFor="project_id" className="block text-sm font-medium text-gray-700 mb-1">
                Project
              </label>
              <select
                id="project_id"
                name="project_id"
                value={formData.project_id}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading || !formData.company_id}
              >
                <option value="">Select a project (optional)</option>
                {filteredProjects.map(project => (
                  <option key={project.id} value={project.id}>
                    {project.name}
                  </option>
                ))}
              </select>
              {formData.company_id && filteredProjects.length === 0 && (
                <p className="mt-1 text-sm text-amber-600">No projects found for this company</p>
              )}
            </div>

            <div className="col-span-2">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Credential Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                required
                value={formData.name}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g. Client Website Admin Login"
              />
            </div>

            <div>
              <label htmlFor="credential_type" className="block text-sm font-medium text-gray-700 mb-1">
                Type
              </label>
              <select
                id="credential_type"
                name="credential_type"
                value={formData.credential_type}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {credentialTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-1">
                URL
              </label>
              <input
                type="url"
                id="url"
                name="url"
                value={formData.url}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://example.com/login"
              />
            </div>

            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                Username
              </label>
              <input
                type="text"
                id="username"
                name="username"
                value={formData.username}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Username or email"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10 disabled:bg-gray-100"
                  placeholder={isPasswordDisabled ? "Not applicable for OAuth" : "Secure password"}
                  disabled={isPasswordDisabled}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
                  onClick={togglePasswordVisibility}
                  disabled={isPasswordDisabled}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
              <p className="mt-1 text-xs text-gray-500">
                {isPasswordDisabled
                  ? "Password is not required for OAuth/External Auth."
                  : "Password will be stored securely encrypted"}
              </p>
            </div>

            <div className="col-span-2">
              <label htmlFor="expiry_date" className="block text-sm font-medium text-gray-700 mb-1">
                Expiry Date
              </label>
              <input
                type="date"
                id="expiry_date"
                name="expiry_date"
                value={formData.expiry_date}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <Link
              href="/credentials"
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 mr-2"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-sm font-medium text-white flex items-center"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                'Save Credential'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}