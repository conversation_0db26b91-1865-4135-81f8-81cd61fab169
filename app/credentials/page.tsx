'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Eye, EyeOff, Plus, Search, ExternalLink, ChevronDown, Building } from 'lucide-react'
import { useSupabase } from '../components/SupabaseProvider'
import { formatDate } from '@/lib/utils'
import { getDecryptedCredential } from '../../lib/security'

interface Credential {
  id: string
  name: string
  username: string | null
  password?: string | null
  credential_type: string | null
  url: string | null
  expiry_date: string | null
  company_id: string | null
  project_id: string | null
  companies: { name: string } | null
  projects: { name: string } | null
}

interface CompanyWithCredentials {
  company_id: string | null;
  company_name: string;
  credentials: Credential[];
}

export default function Credentials() {
  const { supabase } = useSupabase()
  const [groupedCredentials, setGroupedCredentials] = useState<CompanyWithCredentials[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [visiblePasswords, setVisiblePasswords] = useState<Record<string, boolean>>({})
  const [decryptedPasswords, setDecryptedPasswords] = useState<Record<string, string>>({})
  const [openAccordionItems, setOpenAccordionItems] = useState<string[]>([])

  useEffect(() => {
    async function fetchAndGroupCredentials() {
      setIsLoading(true)
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (!session) {
          setIsLoading(false)
          return
        }

        // Fetch all credentials first
        const { data: rawCredentials, error: credError } = await supabase
          .from('credentials')
          .select(`
            id,
            name,
            username,
            credential_type,
            url,
            expiry_date,
            company_id,
            project_id
          `)
          .order('name')

        if (credError) {
          console.error('Error fetching credentials:', credError)
          setIsLoading(false)
          return
        }

        if (!rawCredentials || rawCredentials.length === 0) {
          setGroupedCredentials([])
          setIsLoading(false)
          return
        }

        // Get unique company and project IDs
        const companyIds = [...new Set(rawCredentials.map(c => c.company_id).filter(Boolean))] as string[]
        const projectIds = [...new Set(rawCredentials.map(c => c.project_id).filter(Boolean))] as string[]

        let companiesData: { id: string; name: string }[] = []
        if (companyIds.length > 0) {
          const { data, error } = await supabase.from('companies').select('id, name').in('id', companyIds)
          if (error) console.error('Error fetching companies:', error)
          else companiesData = data || []
        }

        let projectsData: { id: string; name: string }[] = []
        if (projectIds.length > 0) {
          const { data, error } = await supabase.from('projects').select('id, name').in('id', projectIds)
          if (error) console.error('Error fetching projects:', error)
          else projectsData = data || []
        }
        
        // Format credentials and attach company/project names
        const formattedCredentials: Credential[] = rawCredentials.map(item => {
          const company = companiesData.find(c => c.id === item.company_id)
          const project = projectsData.find(p => p.id === item.project_id)
          return {
            ...item,
            companies: company ? { name: company.name } : null,
            projects: project ? { name: project.name } : null,
          }
        })

        // Group credentials by company
        const groups: Record<string, CompanyWithCredentials> = {}

        formattedCredentials.forEach(cred => {
          const companyId = cred.company_id || 'unassigned'
          const companyName = cred.companies?.name || 'General Credentials'

          if (!groups[companyId]) {
            groups[companyId] = {
              company_id: cred.company_id,
              company_name: companyName,
              credentials: [],
            }
          }
          groups[companyId].credentials.push(cred)
        })
        
        // Sort company groups by name, putting "General Credentials" last or first
        const sortedGroupedCredentials = Object.values(groups).sort((a, b) => {
          if (a.company_name === 'General Credentials') return 1; // General last
          if (b.company_name === 'General Credentials') return -1;
          return a.company_name.localeCompare(b.company_name);
        });

        setGroupedCredentials(sortedGroupedCredentials)

      } catch (err) {
        console.error('Unexpected error fetching and grouping credentials:', err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchAndGroupCredentials()
  }, [supabase])

  const togglePasswordVisibility = async (id: string) => {
    if (!visiblePasswords[id] && !decryptedPasswords[id]) {
      try {
        const credential = await getDecryptedCredential(id)
        if (credential && credential.password) {
          setDecryptedPasswords(prev => ({ ...prev, [id]: credential.password as string }))
        }
      } catch (error) {
        console.error('Error decrypting password:', error)
      }
    }
    setVisiblePasswords(prev => ({ ...prev, [id]: !prev[id] }))
  }
  
  const toggleAccordionItem = (companyId: string | null) => {
    const idToToggle = companyId || 'unassigned';
    setOpenAccordionItems(prevOpen =>
      prevOpen.includes(idToToggle)
        ? prevOpen.filter(item => item !== idToToggle)
        : [...prevOpen, idToToggle]
    );
  };

  // Filter company groups based on search query (searches company name and credential details within)
  const filteredCompanyGroups = groupedCredentials.filter(group => {
    const groupNameMatch = group.company_name.toLowerCase().includes(searchQuery.toLowerCase());
    if (groupNameMatch) return true;

    // Also search within credentials of this group
    return group.credentials.some(credential =>
      credential.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      credential.username?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      credential.credential_type?.toLowerCase().includes(searchQuery.toLowerCase())
      // Not searching project name here as it's less relevant for top-level company filter
    );
  });

  const isExpiringSoon = (expiryDate: string | null) => {
    if (!expiryDate) return false
    const expiry = new Date(expiryDate)
    const today = new Date()
    const thirtyDaysFromNow = new Date(); thirtyDaysFromNow.setDate(today.getDate() + 30)
    return expiry <= thirtyDaysFromNow && expiry >= today
  }

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Credentials</h1>
          <p className="text-gray-600">Securely store and manage client credentials</p>
        </div>

        <Link
          href="/credentials/new"
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <Plus size={18} className="mr-1" />
          Add Credential
        </Link>
      </div>

      <div className="mb-6">
        <div className="relative w-full md:w-96">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-5 h-5 text-gray-400" />
          </div>
          <input
            type="search"
            className="block w-full py-2 pl-10 pr-3 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search by company or credential..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      ) : filteredCompanyGroups.length === 0 && searchQuery ? (
        <p className="text-gray-600 text-center py-10">No credentials found matching your search.</p>
      ) : filteredCompanyGroups.length === 0 ? (
         <p className="text-gray-600 text-center py-10">No credentials have been added yet.</p>
      ) : (
        <div className="space-y-2">
          {filteredCompanyGroups.map((group) => (
            <div key={group.company_id || 'unassigned'} className="border border-gray-200 rounded-lg overflow-hidden">
              <button
                onClick={() => toggleAccordionItem(group.company_id)}
                className="w-full flex justify-between items-center px-4 py-3 bg-gray-50 hover:bg-gray-100 text-left focus:outline-none"
              >
                <span className="font-medium text-gray-800 flex items-center">
                   <Building size={18} className="mr-2 text-gray-600" /> {group.company_name} ({group.credentials.length})
                </span>
                <ChevronDown
                  size={20}
                  className={`text-gray-500 transition-transform duration-200 ${
                    openAccordionItems.includes(group.company_id || 'unassigned') ? 'transform rotate-180' : ''
                  }`}
                />
              </button>
              {(openAccordionItems.includes(group.company_id || 'unassigned')) && (
                <div className="p-4 bg-white border-t border-gray-200">
                  {group.credentials.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Password</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {group.credentials.map((credential) => (
                            <tr key={credential.id} className={isExpiringSoon(credential.expiry_date) ? 'bg-yellow-50' : ''}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {credential.name}
                                {credential.url && (
                                  <a href={credential.url} target="_blank" rel="noopener noreferrer" className="ml-2 text-blue-500 hover:text-blue-700">
                                    <ExternalLink size={14} className="inline" />
                                  </a>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{credential.credential_type || '-'}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{credential.username || '-'}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div className="flex items-center">
                                  <span>
                                    {visiblePasswords[credential.id]
                                      ? decryptedPasswords[credential.id] || '••••••••'
                                      : '••••••••'}
                                  </span>
                                  <button
                                    onClick={() => togglePasswordVisibility(credential.id)}
                                    className="ml-2 text-gray-400 hover:text-gray-600"
                                    aria-label={visiblePasswords[credential.id] ? 'Hide password' : 'Show password'}
                                  >
                                    {visiblePasswords[credential.id] ? <EyeOff size={16} /> : <Eye size={16} />}
                                  </button>
                                </div>
                              </td>
                              <td className={`px-6 py-4 whitespace-nowrap text-sm ${isExpiringSoon(credential.expiry_date) ? 'text-yellow-700 font-semibold' : 'text-gray-500'}`}>
                                {credential.expiry_date ? formatDate(credential.expiry_date) : '-'}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {credential.projects?.name || '-'}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <Link href={`/credentials/edit/${credential.id}`} className="text-blue-600 hover:text-blue-900 mr-3">
                                  Edit
                                </Link>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm px-6 py-4">No credentials found for this company.</p>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}