'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Plus, Search, Building, Mail, Phone, ExternalLink, Shield, Edit, Trash2 } from 'lucide-react'
import { getInitials } from '@/lib/utils'
import { useSupabase } from '@/app/components/SupabaseProvider'
import ViewToggle from '@/app/components/ViewToggle'

interface Contact {
  id: string
  name: string
  email: string | null
  phone: string | null
  position: string | null
  notes: string | null
  company_id: string | null
}

export default function Contacts() {
  const [contacts, setContacts] = useState<Contact[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'card' | 'table'>('card')
  const { supabase, user } = useSupabase()

  useEffect(() => {
    async function fetchContacts() {
      setIsLoading(true)
      setError(null)
      console.log('Attempting to fetch contacts...')

      try {
        // Try direct Supabase query first (authenticated)
        if (user) {
          console.log('Fetching contacts via Supabase client (authenticated)...')
          const { data, error } = await supabase
            .from('contacts')
            .select('*')
            .order('name')

          if (error) {
            console.error('Error fetching contacts from Supabase:', error)
            throw error
          }

          if (data && data.length > 0) {
            console.log('Successfully fetched contacts from Supabase:', data.length)
            setContacts(data)
            setIsLoading(false)
            return
          } else {
            console.log('No contacts found via Supabase, trying API...')
          }
        }

        // Fallback to admin API endpoint
        console.log('Fetching contacts from admin API...')
        const response = await fetch('/api/admin/contacts')

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`)
        }

        const data = await response.json()
        console.log('Admin API response:', data)

        if (data.contacts) {
          console.log('Fetched contacts data:', data.contacts)
          console.log('Fetched contacts length:', data.contacts.length)
          setContacts(data.contacts)
        } else {
          console.error('No contacts data in API response')
          setContacts([])
        }
      } catch (err) {
        console.error('Unexpected error:', err)
        setError('Failed to load contacts. Please try again later.')
        setContacts([])
      }

      setIsLoading(false)
    }

    fetchContacts()
  }, [supabase, user])

  // Filter contacts based on search query
  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.phone?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.position?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Contacts</h1>
          <p className="text-gray-600">Manage your client contacts</p>
        </div>

        <Link
          href="/contacts/new"
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <Plus size={18} className="mr-1" />
          Add Contact
        </Link>
      </div>

      {/* Search and view toggle */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4 items-center">
        <div className="relative w-full md:w-96">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-5 h-5 text-gray-400" />
          </div>
          <input
            type="search"
            className="block w-full py-2 pl-10 pr-3 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search contacts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="ml-auto">
          <ViewToggle onViewChange={setViewMode} initialView={viewMode} />
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded flex items-center">
          <Shield className="h-5 w-5 mr-2" />
          <span>{error}</span>
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div role="status" className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      ) : filteredContacts.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="bg-gray-100 rounded-full p-3 mb-3">
            <Building className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-1">No contacts found</h3>
          <p className="text-gray-500 max-w-md">
            {searchQuery
              ? 'Try changing your search terms'
              : 'Add your first contact to get started'}
          </p>
          {!searchQuery && (
            <Link
              href="/contacts/new"
              className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
            >
              Add Contact
            </Link>
          )}
        </div>
      ) : viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredContacts.map((contact) => (
            <div key={contact.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-5">
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                        <span className="text-blue-800 font-medium text-lg">{getInitials(contact.name)}</span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">
                        <Link href={`/contacts/${contact.id}`} className="hover:underline">
                          {contact.name}
                        </Link>
                      </h3>
                      <p className="text-sm text-gray-500">{contact.position || 'No position'}</p>
                    </div>
                  </div>
                </div>

                <div className="mt-4 space-y-3">
                  {contact.company_id && (
                    <div className="flex items-center text-sm">
                      <Building className="h-4 w-4 text-gray-400 mr-2" />
                      <Link href={`/companies/${contact.company_id}`} className="text-gray-600 hover:text-blue-600">
                        Company Details
                      </Link>
                    </div>
                  )}

                  {contact.email && (
                    <div className="flex items-center text-sm">
                      <Mail className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={`mailto:${contact.email}`} className="text-gray-600 hover:text-blue-600 truncate">
                        {contact.email}
                      </a>
                    </div>
                  )}

                  {contact.phone && (
                    <div className="flex items-center text-sm">
                      <Phone className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={`tel:${contact.phone}`} className="text-gray-600 hover:text-blue-600">
                        {contact.phone}
                      </a>
                    </div>
                  )}
                </div>

                {contact.notes && (
                  <div className="mt-4">
                    <p className="text-sm text-gray-500 line-clamp-2">{contact.notes}</p>
                  </div>
                )}

                <div className="mt-5 flex justify-between">
                  <Link
                    href={`/contacts/${contact.id}`}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center"
                  >
                    View Details
                    <ExternalLink className="ml-1 h-3 w-3" />
                  </Link>

                  <div className="flex space-x-2">
                    {contact.email && (
                      <a
                        href={`mailto:${contact.email}`}
                        className="p-1.5 bg-blue-50 rounded-full text-blue-600 hover:bg-blue-100"
                        title="Send Email"
                      >
                        <Mail className="h-4 w-4" />
                      </a>
                    )}

                    {contact.phone && (
                      <a
                        href={`tel:${contact.phone}`}
                        className="p-1.5 bg-green-50 rounded-full text-green-600 hover:bg-green-100"
                        title="Call"
                      >
                        <Phone className="h-4 w-4" />
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Position
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Phone
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Company
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredContacts.map((contact) => (
                  <tr key={contact.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <span className="text-blue-800 font-medium">{getInitials(contact.name)}</span>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{contact.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{contact.position || '-'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {contact.email ? (
                        <a href={`mailto:${contact.email}`} className="text-sm text-blue-600 hover:text-blue-900">
                          {contact.email}
                        </a>
                      ) : (
                        <span className="text-sm text-gray-500">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {contact.phone ? (
                        <a href={`tel:${contact.phone}`} className="text-sm text-blue-600 hover:text-blue-900">
                          {contact.phone}
                        </a>
                      ) : (
                        <span className="text-sm text-gray-500">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {contact.company_id ? (
                        <Link href={`/companies/${contact.company_id}`} className="text-sm text-blue-600 hover:text-blue-900">
                          View Company
                        </Link>
                      ) : (
                        <span className="text-sm text-gray-500">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Link href={`/contacts/${contact.id}`} className="text-blue-600 hover:text-blue-900 mr-3">
                        View
                      </Link>
                      <Link href={`/contacts/${contact.id}/edit`} className="text-indigo-600 hover:text-indigo-900">
                        Edit
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}