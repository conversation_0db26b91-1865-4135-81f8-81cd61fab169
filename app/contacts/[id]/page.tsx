'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  ArrowLeft,
  Building,
  Mail,
  Phone,
  Briefcase,
  Calendar,
  Edit,
  Trash2,
  ExternalLink
} from 'lucide-react'
import supabase from '../../../lib/supabase/client'
import { getInitials } from '../../../lib/utils'

interface Contact {
  id: string
  name: string
  email: string | null
  phone: string | null
  position: string | null
  notes: string | null
  company_id: string | null
  created_at: string | null
  updated_at: string | null
  companies?: {
    id: string
    name: string
  } | null
}

interface Project {
  id: string
  name: string
  status: string | null
  start_date: string | null
  expected_end_date: string | null
}

export default function ContactDetail({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [contact, setContact] = useState<Contact | null>(null)
  const [projects, setProjects] = useState<Project[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchContactData() {
      setIsLoading(true)
      setError(null)

      try {
        // Fetch contact details
        const { data: contactData, error: contactError } = await supabase
          .from('contacts')
          .select(`
            id,
            name,
            email,
            phone,
            position,
            notes,
            company_id,
            created_at,
            updated_at,
            companies (id, name)
          `)
          .eq('id', params.id)
          .single()

        if (contactError) throw contactError

        setContact(contactData)

        // Fetch related projects if contact has a company
        if (contactData.company_id) {
          const { data: projectsData, error: projectsError } = await supabase
            .from('projects')
            .select('id, name, status, start_date, expected_end_date')
            .eq('company_id', contactData.company_id)
            .order('created_at', { ascending: false })

          if (projectsError) throw projectsError

          setProjects(projectsData || [])
        }

      } catch (err: any) {
        console.error('Error fetching contact data:', err)
        setError('Failed to load contact data. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    fetchContactData()
  }, [params.id])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (error || !contact) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative max-w-md w-full" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error || 'Failed to load contact data. Please try again.'}</span>
          <div className="mt-4">
            <Link href="/contacts" className="text-red-700 underline">
              Return to contacts list
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div>
      {/* Header with back button and actions */}
      <div className="mb-6">
        <Link
          href="/contacts"
          className="text-gray-500 hover:text-gray-700 inline-flex items-center"
        >
          <ArrowLeft size={16} className="mr-1" />
          Back to Contacts
        </Link>

        <div className="flex justify-between items-center mt-4">
          <div className="flex items-center">
            <div className="h-14 w-14 rounded-full bg-blue-100 flex items-center justify-center mr-4">
              <span className="text-blue-800 font-medium text-xl">{getInitials(contact.name)}</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">{contact.name}</h1>
          </div>

          <div className="flex space-x-2">
            <Link
              href={`/contacts/${contact.id}/edit`}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center"
            >
              <Edit size={16} className="mr-1" />
              Edit
            </Link>
            <button
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md flex items-center"
              onClick={async () => {
                if (window.confirm('Are you sure you want to delete this contact? This action cannot be undone.')) {
                  try {
                    const { error } = await supabase
                      .from('contacts')
                      .delete()
                      .eq('id', contact.id)

                    if (error) throw error

                    // Navigate back to contacts list
                    router.push('/contacts')
                    router.refresh()
                  } catch (err) {
                    console.error('Error deleting contact:', err)
                    alert('Failed to delete contact. Please try again.')
                  }
                }
              }}
            >
              <Trash2 size={16} className="mr-1" />
              Delete
            </button>
          </div>
        </div>

        {contact.position && (
          <div className="mt-1">
            <span className="text-gray-600">{contact.position}</span>
          </div>
        )}
      </div>

      {/* Contact details */}
      <div className="bg-white shadow rounded-lg mb-6">
        <div className="px-6 py-5 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Contact Details</h2>
        </div>

        <div className="px-6 py-5">
          <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
            {contact.companies && (
              <div>
                <dt className="text-sm font-medium text-gray-500 flex items-center">
                  <Building className="w-4 h-4 mr-1" />
                  Company
                </dt>
                <dd className="mt-1 text-sm text-gray-900">
                  <Link
                    href={`/companies/${contact.companies.id}`}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    {contact.companies.name}
                  </Link>
                </dd>
              </div>
            )}

            {contact.email && (
              <div>
                <dt className="text-sm font-medium text-gray-500 flex items-center">
                  <Mail className="w-4 h-4 mr-1" />
                  Email
                </dt>
                <dd className="mt-1 text-sm text-gray-900">
                  <a
                    href={`mailto:${contact.email}`}
                    className="text-blue-600 hover:text-blue-800 flex items-center"
                  >
                    {contact.email}
                    <ExternalLink className="w-3 h-3 ml-1" />
                  </a>
                </dd>
              </div>
            )}

            {contact.phone && (
              <div>
                <dt className="text-sm font-medium text-gray-500 flex items-center">
                  <Phone className="w-4 h-4 mr-1" />
                  Phone
                </dt>
                <dd className="mt-1 text-sm text-gray-900">
                  <a
                    href={`tel:${contact.phone}`}
                    className="text-blue-600 hover:text-blue-800 flex items-center"
                  >
                    {contact.phone}
                    <ExternalLink className="w-3 h-3 ml-1" />
                  </a>
                </dd>
              </div>
            )}

            <div>
              <dt className="text-sm font-medium text-gray-500">Created</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {contact.created_at ? new Date(contact.created_at).toLocaleDateString() : 'Unknown'}
              </dd>
            </div>
          </dl>

          {contact.notes && (
            <div className="mt-6">
              <h3 className="text-sm font-medium text-gray-500">Notes</h3>
              <div className="mt-2 p-3 bg-gray-50 rounded-md text-sm text-gray-900">
                {contact.notes}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Related projects section */}
      {contact.companies && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-5 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900 flex items-center">
              <Briefcase className="w-5 h-5 mr-2 text-gray-500" />
              Company Projects
            </h2>
            <Link
              href={`/projects/new?company=${contact.company_id}`}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              + Add Project
            </Link>
          </div>

          <div className="px-6 py-5">
            {projects.length > 0 ? (
              <ul className="divide-y divide-gray-200">
                {projects.map(project => (
                  <li key={project.id} className="py-4">
                    <Link href={`/projects/${project.id}`} className="block hover:bg-gray-50">
                      <div className="flex justify-between">
                        <p className="text-sm font-medium text-gray-900">{project.name}</p>
                        {project.status && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {project.status}
                          </span>
                        )}
                      </div>
                      <div className="mt-1 text-sm text-gray-500">
                        {project.start_date && (
                          <span>Started: {new Date(project.start_date).toLocaleDateString()}</span>
                        )}
                        {project.expected_end_date && (
                          <span className="ml-4">Due: {new Date(project.expected_end_date).toLocaleDateString()}</span>
                        )}
                      </div>
                    </Link>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500 py-4">No projects found for this contact's company.</p>
            )}
          </div>
        </div>
      )}

      {/* Quick actions */}
      <div className="mt-6 flex space-x-4">
        {contact.email && (
          <a
            href={`mailto:${contact.email}`}
            className="flex-1 bg-blue-50 hover:bg-blue-100 text-blue-700 py-3 px-4 rounded-md flex items-center justify-center"
          >
            <Mail className="w-5 h-5 mr-2" />
            Send Email
          </a>
        )}

        {contact.phone && (
          <a
            href={`tel:${contact.phone}`}
            className="flex-1 bg-green-50 hover:bg-green-100 text-green-700 py-3 px-4 rounded-md flex items-center justify-center"
          >
            <Phone className="w-5 h-5 mr-2" />
            Call
          </a>
        )}

        <Link
          href={`/calendar/new?contact=${contact.id}`}
          className="flex-1 bg-purple-50 hover:bg-purple-100 text-purple-700 py-3 px-4 rounded-md flex items-center justify-center"
        >
          <Calendar className="w-5 h-5 mr-2" />
          Schedule Meeting
        </Link>
      </div>
    </div>
  )
}
