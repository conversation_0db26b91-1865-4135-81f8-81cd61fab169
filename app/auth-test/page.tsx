'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import supabase from '@/lib/supabase/client'

export default function AuthTestPage() {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function checkAuth() {
      try {
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          throw sessionError
        }

        setUser(session?.user || null)
      } catch (err: any) {
        console.error('Error checking auth:', err)
        setError(err.message || 'Failed to check authentication status')
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut()
      setUser(null)
      window.location.href = '/sign-in'
    } catch (err: any) {
      console.error('Error signing out:', err)
      setError(err.message || 'Failed to sign out')
    }
  }

  if (loading) {
    return (
      <div className="p-8 max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-4">Authentication Test</h1>
        <div className="bg-white p-6 rounded-lg shadow">
          <p>Loading authentication status...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Authentication Test</h1>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow mb-4">
        <h2 className="text-xl font-semibold mb-2">Authentication Status</h2>
        {user ? (
          <div>
            <p className="text-green-600 font-medium">✅ Authenticated</p>
            <div className="mt-4 p-4 bg-gray-50 rounded overflow-auto">
              <pre className="text-sm">{JSON.stringify(user, null, 2)}</pre>
            </div>
          </div>
        ) : (
          <p className="text-red-600 font-medium">❌ Not authenticated</p>
        )}
      </div>

      <div className="bg-white p-6 rounded-lg shadow mb-4">
        <h2 className="text-xl font-semibold mb-2">Actions</h2>
        <div className="flex flex-wrap gap-4">
          {user ? (
            <button
              onClick={handleSignOut}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Sign Out
            </button>
          ) : (
            <Link
              href="/sign-in"
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Sign In
            </Link>
          )}

          <Link
            href="/dashboard"
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Go to Dashboard
          </Link>

          <Link
            href="/"
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            Go to Home
          </Link>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-2">Debug Information</h2>
        <p className="mb-2">
          This page is not protected by middleware and can be accessed whether you&apos;re authenticated or not.
          It&apos;s useful for debugging authentication issues.
        </p>
        <p>
          If you&apos;re authenticated but can&apos;t access protected routes, there might be an issue with how
          the session is being stored or retrieved.
        </p>
      </div>
    </div>
  )
}
