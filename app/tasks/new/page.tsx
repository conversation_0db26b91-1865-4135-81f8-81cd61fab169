'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { useSupabase } from '../../components/SupabaseProvider'

interface Project {
  id: string
  name: string
  company_id: string | null
  company_name?: string | null
}

interface Contact {
  id: string
  name: string
}

export default function NewTask() {
  const router = useRouter()
  const { supabase } = useSupabase()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [projects, setProjects] = useState<Project[]>([])
  const [contacts, setContacts] = useState<Contact[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    status: 'pending',
    priority: 'medium',
    due_date: '',
    project_id: '',
    assigned_to: '',
    estimated_hours: '',
    task_type: 'general',
  })

  // Status and priority options
  const statusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'blocked', label: 'Blocked' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' },
  ]

  const priorityOptions = [
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
    { value: 'urgent', label: 'Urgent' },
  ]

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true)

      try {
        // Fetch projects with company names
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select(`
            id,
            name,
            company_id,
            companies:company_id (name)
          `)
          .order('name')

        if (projectsError) throw projectsError

        // Fetch contacts
        const { data: contactsData, error: contactsError } = await supabase
          .from('contacts')
          .select('id, name')
          .order('name')

        if (contactsError) throw contactsError

        // Process project data to include company name
        const processedProjects = projectsData ? projectsData.map((project: any) => ({
          id: project.id,
          name: project.name,
          company_id: project.company_id,
          company_name: project.companies ? project.companies.name : null
        })) : []

        setProjects(processedProjects)
        setContacts(contactsData || [])

      } catch (err) {
        console.error('Error fetching data:', err)
        setError('Failed to load projects and contacts data')
      }

      setIsLoading(false)
    }

    fetchData()
  }, [supabase])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)

    if (!formData.name) {
      setError('Task name is required')
      setIsSubmitting(false)
      return
    }

    try {
      // Process form data
      const taskData = {
        name: formData.name,
        description: formData.description || null,
        status: formData.status || 'pending',
        priority: formData.priority || 'medium',
        due_date: formData.due_date || null,
        project_id: formData.project_id || null,
        assigned_to: formData.assigned_to || null,
        estimated_hours: formData.estimated_hours ? parseFloat(formData.estimated_hours) : null,
        task_type: formData.task_type || 'general',
      }

      console.log('Submitting task data:', taskData)

      // Get the current user to verify authentication
      const { data: { user } } = await supabase.auth.getUser()
      console.log('Current user:', user?.id)

      // Check if the user has the necessary permissions
      const { data: userRoles, error: rolesError } = await supabase
        .from('user_roles')
        .select('role')
        .eq('user_id', user?.id)
        .single()

      if (rolesError) {
        console.error('Error checking user roles:', rolesError)
      } else {
        console.log('User role:', userRoles?.role)
      }

      const { data, error: insertError } = await supabase
        .from('tasks')
        .insert([taskData])
        .select()

      if (insertError) {
        console.error('Error inserting task:', insertError)
        throw insertError
      }

      console.log('Task created successfully:', data)

      // Navigate to tasks list
      router.push('/tasks')
      router.refresh()
    } catch (err: unknown) {
      console.error('Error in task creation:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to create task'
      setError(errorMessage)
      setIsSubmitting(false)
    }
  }

  return (
    <div>
      <div className="mb-6">
        <Link
          href="/tasks"
          className="text-gray-500 hover:text-gray-700 inline-flex items-center"
        >
          <ArrowLeft size={16} className="mr-1" />
          Back to Tasks
        </Link>
        <h1 className="text-2xl font-bold text-gray-900 mt-4">Create New Task</h1>
        <p className="text-gray-600">Add a task to track work items</p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="col-span-2">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Task Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                required
                value={formData.name}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter task name"
              />
            </div>

            <div className="col-span-2">
              <label htmlFor="project_id" className="block text-sm font-medium text-gray-700 mb-1">
                Related Project
              </label>
              <select
                id="project_id"
                name="project_id"
                value={formData.project_id}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="">Select a project (optional)</option>
                {projects.map(project => (
                  <option key={project.id} value={project.id}>
                    {project.name} {project.company_name ? `(${project.company_name})` : ''}
                  </option>
                ))}
              </select>
            </div>

            <div className="col-span-2">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={4}
                value={formData.description}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Describe the task details, requirements, etc."
              ></textarea>
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-1">
                Priority
              </label>
              <select
                id="priority"
                name="priority"
                value={formData.priority}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {priorityOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="assigned_to" className="block text-sm font-medium text-gray-700 mb-1">
                Assigned To
              </label>
              <select
                id="assigned_to"
                name="assigned_to"
                value={formData.assigned_to}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="">Unassigned</option>
                {contacts.map(contact => (
                  <option key={contact.id} value={contact.id}>
                    {contact.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="due_date" className="block text-sm font-medium text-gray-700 mb-1">
                Due Date
              </label>
              <input
                type="date"
                id="due_date"
                name="due_date"
                value={formData.due_date}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="estimated_hours" className="block text-sm font-medium text-gray-700 mb-1">
                Estimated Hours
              </label>
              <input
                type="number"
                id="estimated_hours"
                name="estimated_hours"
                step="0.5"
                min="0"
                value={formData.estimated_hours}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g. 4.5"
              />
            </div>
          </div>

          <div className="mt-8 flex justify-end space-x-4">
            <Link
              href="/tasks"
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
            >
              {isSubmitting ? 'Creating...' : 'Create Task'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}