'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd'
import {
  Plus,
  Search,
  Calendar,
  Clock,
  AlertCircle,
  User,
  Briefcase,
  Building,
  ArrowLeft
} from 'lucide-react'
import { useSupabase } from '../../components/SupabaseProvider'
import { formatDate } from '../../../lib/utils'

interface Task {
  id: string
  name: string
  description: string | null
  status: string
  priority: string
  due_date: string | null
  assigned_to: string | null
  project_id: string | null
  company_id: string | null
  created_at: string
  updated_at: string
  projects?: {
    id: string
    name: string
    companies?: {
      id: string
      name: string
    } | null
  } | null
  companies?: {
    id: string
    name: string
  } | null
  users?: {
    id: string
    email: string
    full_name: string | null
  } | null
}

interface Column {
  id: string
  title: string
  tasks: Task[]
}

export default function TaskBoard() {
  const { supabase } = useSupabase()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [priorityFilter, setPriorityFilter] = useState<string>('all')
  const [projectFilter, setProjectFilter] = useState<string>('all')
  const [companyFilter, setCompanyFilter] = useState<string>('all')
  const [assigneeFilter, setAssigneeFilter] = useState<string>('all')
  const [projects, ] = useState<{ id: string; name: string }[]>([])
  const [companies, setCompanies] = useState<{ id: string; name: string }[]>([])
  const [tasks, setTasks] = useState<Task[]>([])
  const [columns, setColumns] = useState<Column[]>([
    { id: 'todo', title: 'To Do', tasks: [] },
    { id: 'in_progress', title: 'In Progress', tasks: [] },
    { id: 'review', title: 'Review', tasks: [] },
    { id: 'completed', title: 'Completed', tasks: [] }
  ])

  // Fetch tasks and related data
  useEffect(() => {
    async function fetchData() {
      setIsLoading(true)
      setError(null)

      try {
        // Fetch tasks with related data
        const { data: tasksData, error: tasksError } = await supabase
          .from('tasks')
          .select('*') // Temporarily simplified
          // .select(`
          //   *,
          //   projects:project_id (
          //     id,
          //     name,
          //     companies:company_id (
          //       id,
          //       name
          //     )
          //   ),
          //   companies:company_id (
          //     id,
          //     name
          //   ),
          //   users:assigned_to (
          //     id,
          //     email,
          //     full_name
          //   )
          // `)
          .order('created_at', { ascending: false })

        if (tasksError) throw tasksError

        // Fetch projects for filtering
        // const { data: projectsData, error: projectsError } = await supabase
        //   .from('projects')
        //   .select('id, name')
        //   .order('name')

        // if (projectsError) throw projectsError

        // Fetch companies for filtering
        const { data: companiesData, error: companiesError } = await supabase
          .from('companies')
          .select('id, name')
          .order('name')

        if (companiesError) throw companiesError

        // Fetch users for filtering
        // const { data: usersData, error: usersError } = await supabase
        //   .from('users') // This was causing the error
        //   .select('id, email, full_name')
        //   .order('email')

        // if (usersError) throw usersError

        setTasks(tasksData || [])
        // setProjects(projectsData || []) // Comment out for now
        setCompanies(companiesData || [])
        // setUsers(usersData || []) // Comment out for now
      } catch (err) {
        console.error('Error fetching data:', err)
        setError('Failed to load tasks')
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [supabase])

  // Organize tasks into columns whenever tasks or filters change
  useEffect(() => {
    // Apply filters
    const filteredTasks = tasks.filter(task => {
      // Search filter
      const matchesSearch =
        searchQuery === '' ||
        task.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.projects?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.companies?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.users?.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.users?.full_name?.toLowerCase().includes(searchQuery.toLowerCase());

      // Priority filter
      const matchesPriority =
        priorityFilter === 'all' ||
        task.priority.toLowerCase() === priorityFilter.toLowerCase();

      // Project filter
      const matchesProject =
        projectFilter === 'all' ||
        task.project_id === projectFilter;

      // Company filter
      const matchesCompany =
        companyFilter === 'all' ||
        task.company_id === companyFilter;

      // Assignee filter
      const matchesAssignee =
        assigneeFilter === 'all' ||
        task.assigned_to === assigneeFilter;

      return matchesSearch && matchesPriority && matchesProject && matchesCompany && matchesAssignee;
    });

    // Map status values to column IDs
    const statusToColumnMap: Record<string, string> = {
      'todo': 'todo',
      'in_progress': 'in_progress',
      'review': 'review',
      'completed': 'completed',
      'pending': 'todo',
      'in progress': 'in_progress',
      'done': 'completed',
      'cancelled': 'completed'
    };

    // Organize tasks into columns
    const newColumns = columns.map(column => ({
      ...column,
      tasks: filteredTasks.filter(task => {
        const mappedStatus = statusToColumnMap[task.status.toLowerCase()] || 'todo';
        return mappedStatus === column.id;
      })
    }));

    setColumns(newColumns);
  }, [tasks, searchQuery, priorityFilter, projectFilter, companyFilter, assigneeFilter]);

  // Handle drag and drop
  const handleDragEnd = async (result: any) => {
    const { destination, source, draggableId } = result;

    // If dropped outside a droppable area
    if (!destination) return;

    // If dropped in the same position
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) return;

    // Find the task that was dragged
    const task = tasks.find(t => t.id === draggableId);
    if (!task) return;

    // Map column IDs to status values
    const columnToStatusMap: Record<string, string> = {
      'todo': 'Todo',
      'in_progress': 'In Progress',
      'review': 'Review',
      'completed': 'Completed'
    };

    // Get the new status based on the destination column
    const newStatus = columnToStatusMap[destination.droppableId] || 'Todo';

    // Update the task status in the database
    try {
      const { error } = await supabase
        .from('tasks')
        .update({ status: newStatus, updated_at: new Date().toISOString() })
        .eq('id', task.id);

      if (error) throw error;

      // Update the local state
      const updatedTasks = tasks.map(t =>
        t.id === task.id ? { ...t, status: newStatus } : t
      );

      setTasks(updatedTasks);
    } catch (err) {
      console.error('Error updating task status:', err);
      // Revert the UI if the update fails
      // This is handled automatically since we're using the tasks state
    }
  };

  // Get priority badge color
  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get due date status
  const getDueDateStatus = (dueDate: string | null) => {
    if (!dueDate) return null;

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const due = new Date(dueDate);
    due.setHours(0, 0, 0, 0);

    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return { color: 'text-red-600', icon: <AlertCircle className="h-4 w-4 mr-1" /> };
    } else if (diffDays === 0) {
      return { color: 'text-orange-600', icon: <Clock className="h-4 w-4 mr-1" /> };
    } else if (diffDays <= 2) {
      return { color: 'text-yellow-600', icon: <Calendar className="h-4 w-4 mr-1" /> };
    } else {
      return { color: 'text-gray-600', icon: <Calendar className="h-4 w-4 mr-1" /> };
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="mb-6 flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/tasks" className="mr-4 p-2 rounded-full hover:bg-gray-100">
            <ArrowLeft className="h-5 w-5 text-gray-500" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Task Board</h1>
            <p className="text-gray-600">Manage tasks with drag-and-drop</p>
          </div>
        </div>

        <Link
          href="/tasks/new"
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <Plus size={18} className="mr-1" />
          Add Task
        </Link>
      </div>

      {/* Search and filters */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <div className="lg:col-span-2">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="w-5 h-5 text-gray-400" />
            </div>
            <input
              type="search"
              className="block w-full py-2 pl-10 pr-3 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search tasks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div>
          <select
            className="block w-full py-2 px-3 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
            aria-label="Priority filter"
          >
            <option value="all">All Priorities</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>

        <div>
          <select
            className="block w-full py-2 px-3 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={projectFilter}
            onChange={(e) => setProjectFilter(e.target.value)}
            aria-label="Project filter"
          >
            <option value="all">All Projects</option>
            {projects.map((project) => (
              <option key={project.id} value={project.id}>
                {project.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <select
            className="block w-full py-2 px-3 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={companyFilter}
            onChange={(e) => setCompanyFilter(e.target.value)}
            aria-label="Company filter"
          >
            <option value="all">All Companies</option>
            {companies.map((company) => (
              <option key={company.id} value={company.id}>
                {company.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <select
            className="block w-full py-2 px-3 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={assigneeFilter}
            onChange={(e) => setAssigneeFilter(e.target.value)}
            aria-label="Assignee filter"
          >
            <option value="all">All Assignees</option>
            {/* {users.map((user) => (
              <option key={user.id} value={user.id}>
                {user.full_name || user.email}
              </option>
            ))} */}
          </select>
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div role="status" className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500" />
        </div>
      ) : (
        <div className="flex-1 overflow-x-auto">
          <DragDropContext onDragEnd={handleDragEnd}>
            <div className="flex h-full space-x-4 pb-4">
              {columns.map((column) => (
                <div key={column.id} className="flex-1 min-w-[300px]">
                  <div className="bg-gray-100 rounded-t-lg p-3">
                    <h3 className="font-medium text-gray-700 flex items-center justify-between">
                      <span>{column.title}</span>
                      <span className="bg-gray-200 text-gray-700 text-xs font-medium px-2 py-1 rounded-full">
                        {column.tasks.length}
                      </span>
                    </h3>
                  </div>

                  <Droppable droppableId={column.id}>
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className={`bg-gray-50 rounded-b-lg p-2 h-[calc(100vh-280px)] overflow-y-auto ${
                          snapshot.isDraggingOver ? 'bg-blue-50' : ''
                        }`}
                      >
                        {column.tasks.length > 0 ? (
                          column.tasks.map((task, index) => (
                            <Draggable key={task.id} draggableId={task.id} index={index}>
                              {(provided, snapshot) => (
                                <div
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                  className={`bg-white rounded-lg shadow-sm p-4 mb-3 ${
                                    snapshot.isDragging ? 'shadow-md' : ''
                                  }`}
                                >
                                  <div className="flex justify-between items-start mb-2">
                                    <Link href={`/tasks/${task.id}`} className="text-blue-600 hover:text-blue-800 font-medium">
                                      {task.name}
                                    </Link>
                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                                      {task.priority}
                                    </span>
                                  </div>

                                  {task.description && (
                                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                                      {task.description}
                                    </p>
                                  )}

                                  <div className="flex flex-col space-y-2 text-xs">
                                    {task.due_date && (
                                      <div className={`flex items-center ${getDueDateStatus(task.due_date)?.color || 'text-gray-600'}`}>
                                        {getDueDateStatus(task.due_date)?.icon}
                                        <span>Due: {formatDate(task.due_date)}</span>
                                      </div>
                                    )}

                                    {task.users && (
                                      <div className="flex items-center text-gray-600">
                                        <User className="h-4 w-4 mr-1" />
                                        <span>{task.users.full_name || task.users.email}</span>
                                      </div>
                                    )}

                                    {task.projects && (
                                      <div className="flex items-center text-gray-600">
                                        <Briefcase className="h-4 w-4 mr-1" />
                                        <span>{task.projects.name}</span>
                                      </div>
                                    )}

                                    {task.companies && (
                                      <div className="flex items-center text-gray-600">
                                        <Building className="h-4 w-4 mr-1" />
                                        <span>{task.companies.name}</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}
                            </Draggable>
                          ))
                        ) : (
                          <div className="flex flex-col items-center justify-center h-full text-gray-400 text-sm">
                            <p>No tasks</p>
                          </div>
                        )}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </div>
              ))}
            </div>
          </DragDropContext>
        </div>
      )}
    </div>
  )
}
