'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  ArrowLeft,
  CheckCircle2,
  Clock,
  AlertCircle,
  Calendar,
  Users,
  Building,
  Edit,
  Trash2,
  Save
} from 'lucide-react'
import { useSupabase } from '../../components/SupabaseProvider'
import { formatDate } from '../../../lib/utils'

interface Task {
  id: string
  name: string
  description: string | null
  status: string | null
  priority: string | null
  due_date: string | null
  task_type: string | null
  assigned_to: string | null
  project_id: string | null
  estimated_hours: number | null
  created_at: string | null
  updated_at: string | null
  projects?: {
    id: string
    name: string
    company_id: string | null
    companies?: {
      id: string
      name: string
    } | null
  } | null
}

interface Project {
  id: string
  name: string
  company_id: string | null
}

interface Contact {
  id: string
  email: string
  role: string
}

export default function TaskDetail({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { supabase } = useSupabase()
  const [task, setTask] = useState<Task | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [projects, setProjects] = useState<Project[]>([])
  const [contacts, setContacts] = useState<Contact[]>([])
  const [assignedUserEmail, setAssignedUserEmail] = useState<string | null>(null)

  // Form state
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [status, setStatus] = useState('pending')
  const [priority, setPriority] = useState('medium')
  const [dueDate, setDueDate] = useState('')
  const [taskType, setTaskType] = useState('')
  const [projectId, setProjectId] = useState('')
  const [assignedTo, setAssignedTo] = useState('')
  const [estimatedHours, setEstimatedHours] = useState('')

  // Fetch task data
  useEffect(() => {
    async function fetchTask() {
      setIsLoading(true)
      setError(null)

      try {
        const { data, error } = await supabase
          .from('tasks')
          .select(`
            *,
            projects:project_id (
              id,
              name,
              company_id,
              companies:company_id (id, name)
            )
          `)
          .eq('id', params.id)
          .single()

        if (error) throw error

        setTask(data)

        // Initialize form state
        setName(data.name)
        setDescription(data.description || '')
        setStatus(data.status || 'pending')
        setPriority(data.priority || 'medium')
        setDueDate(data.due_date || '')
        setTaskType(data.task_type || '')
        setProjectId(data.project_id || '')
        setAssignedTo(data.assigned_to || '')
        setEstimatedHours(data.estimated_hours ? data.estimated_hours.toString() : '')

        // Fetch assigned user details if there's an assigned user
        if (data.assigned_to) {
          fetchAssignedUser(data.assigned_to)
        }

      } catch (err) {
        console.error('Error fetching task:', err)
        setError('Failed to load task details')
      } finally {
        setIsLoading(false)
      }
    }

    // Fetch projects and users
    async function fetchRelatedData() {
      try {
        // Fetch projects
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select('id, name, company_id')
          .order('name')

        if (projectsError) {
          console.error('Error fetching projects:', projectsError)
        } else {
          setProjects(projectsData || [])
        }

        // Fetch users
        const response = await fetch('/api/auth/list-users')
        const userData = await response.json()

        if (!response.ok) {
          console.error('Error fetching users:', userData.error)
        } else {
          setContacts(userData.users || [])
        }
      } catch (err) {
        console.error('Unexpected error:', err)
      }
    }

    // Function to fetch assigned user details
    async function fetchAssignedUser(userId: string) {
      try {
        const response = await fetch(`/api/auth/get-user?userId=${userId}`)
        const data = await response.json()

        if (!response.ok) {
          console.error('Error fetching assigned user:', data.error)
          return
        }

        if (data.user) {
          setAssignedUserEmail(data.user.email)
        }
      } catch (err) {
        console.error('Unexpected error fetching assigned user:', err)
      }
    }

    fetchTask()
    fetchRelatedData()
  }, [supabase, params.id])

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    setError(null)

    try {
      // Validate form
      if (!name) {
        setError('Task name is required')
        setIsSaving(false)
        return
      }

      // Update task record
      const { error: updateError } = await supabase
        .from('tasks')
        .update({
          name,
          description: description || null,
          status,
          priority,
          due_date: dueDate || null,
          task_type: taskType || null,
          project_id: projectId || null,
          assigned_to: assignedTo || null,
          estimated_hours: estimatedHours ? parseFloat(estimatedHours) : null,
          updated_at: new Date().toISOString()
        })
        .eq('id', params.id)

      if (updateError) throw updateError

      // Fetch updated task
      const { data: updatedTask, error: fetchError } = await supabase
        .from('tasks')
        .select(`
          *,
          projects:project_id (
            id,
            name,
            company_id,
            companies:company_id (id, name)
          )
        `)
        .eq('id', params.id)
        .single()

      if (fetchError) {
        console.error('Error fetching updated task:', fetchError)
      } else {
        setTask(updatedTask)

        // Fetch assigned user details if there's an assigned user
        if (updatedTask.assigned_to) {
          fetchAssignedUser(updatedTask.assigned_to)
        } else {
          setAssignedUserEmail(null)
        }
      }

      setIsEditing(false)
    } catch (err) {
      console.error('Error updating task:', err)
      setError('Failed to update task')
    } finally {
      setIsSaving(false)
    }
  }

  // Handle task deletion
  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this task? This action cannot be undone.')) {
      return
    }

    setIsDeleting(true)

    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', params.id)

      if (error) throw error

      // Redirect to tasks list or project detail
      if (task?.project_id) {
        router.push(`/projects/${task.project_id}`)
      } else {
        router.push('/tasks')
      }
    } catch (err) {
      console.error('Error deleting task:', err)
      setError('Failed to delete task')
      setIsDeleting(false)
    }
  }

  // Get status color
  const getStatusColor = (status: string | null) => {
    if (!status) return 'bg-gray-100 text-gray-800'

    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'blocked':
        return 'bg-red-100 text-red-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Get priority color
  const getPriorityColor = (priority: string | null) => {
    if (!priority) return 'bg-gray-100 text-gray-800'

    switch (priority.toLowerCase()) {
      case 'low':
        return 'bg-blue-100 text-blue-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'urgent':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Task types
  const taskTypes = [
    'Development',
    'Design',
    'Research',
    'Meeting',
    'Documentation',
    'Testing',
    'Deployment',
    'Maintenance',
    'Support',
    'Other'
  ]

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (error && !task) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
        {error}
        <div className="mt-4">
          <Link href="/tasks" className="text-red-700 underline">
            Return to tasks list
          </Link>
        </div>
      </div>
    )
  }

  if (!task) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md">
        Task not found
        <div className="mt-4">
          <Link href="/tasks" className="text-yellow-700 underline">
            Return to tasks list
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href={task.project_id ? `/projects/${task.project_id}` : '/tasks'}
            className="mr-4 p-2 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft className="h-5 w-5 text-gray-500" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {isEditing ? 'Edit Task' : task.name}
            </h1>
            {!isEditing && task.projects && (
              <p className="text-gray-600">
                Project: <Link href={`/projects/${task.projects.id}`} className="text-blue-600 hover:text-blue-800">
                  {task.projects.name}
                </Link>
              </p>
            )}
          </div>
        </div>

        <div className="flex space-x-2">
          {!isEditing ? (
            <>
              <button
                onClick={() => setIsEditing(true)}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-sm font-medium text-white flex items-center"
              >
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </button>
              <button
                onClick={handleDelete}
                disabled={isDeleting}
                className="px-3 py-2 bg-red-600 hover:bg-red-700 rounded-md text-sm font-medium text-white flex items-center"
              >
                {isDeleting ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                ) : (
                  <Trash2 className="h-4 w-4 mr-1" />
                )}
                Delete
              </button>
            </>
          ) : (
            <button
              onClick={() => setIsEditing(false)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
          )}
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        {isEditing ? (
          <form onSubmit={handleSubmit} className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Task Name */}
              <div className="col-span-2">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Task Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter task name"
                  required
                />
              </div>

              {/* Description */}
              <div className="col-span-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter task description"
                ></textarea>
              </div>

              {/* Status */}
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  id="status"
                  value={status}
                  onChange={(e) => setStatus(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="pending">Pending</option>
                  <option value="in_progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="blocked">Blocked</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              {/* Priority */}
              <div>
                <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-1">
                  Priority
                </label>
                <select
                  id="priority"
                  value={priority}
                  onChange={(e) => setPriority(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>

              {/* Due Date */}
              <div>
                <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Due Date
                </label>
                <input
                  type="date"
                  id="dueDate"
                  value={dueDate}
                  onChange={(e) => setDueDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Task Type */}
              <div>
                <label htmlFor="taskType" className="block text-sm font-medium text-gray-700 mb-1">
                  Task Type
                </label>
                <select
                  id="taskType"
                  value={taskType}
                  onChange={(e) => setTaskType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a type</option>
                  {taskTypes.map((type) => (
                    <option key={type} value={type.toLowerCase()}>
                      {type}
                    </option>
                  ))}
                </select>
              </div>

              {/* Project */}
              <div>
                <label htmlFor="project" className="block text-sm font-medium text-gray-700 mb-1">
                  Project
                </label>
                <select
                  id="project"
                  value={projectId}
                  onChange={(e) => setProjectId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">No Project</option>
                  {projects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Assigned To */}
              <div>
                <label htmlFor="assignedTo" className="block text-sm font-medium text-gray-700 mb-1">
                  Assigned To
                </label>
                <select
                  id="assignedTo"
                  value={assignedTo}
                  onChange={(e) => setAssignedTo(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Unassigned</option>
                  {contacts.map((contact) => (
                    <option key={contact.id} value={contact.id}>
                      {contact.email} ({contact.role})
                    </option>
                  ))}
                </select>
              </div>

              {/* Estimated Hours */}
              <div>
                <label htmlFor="estimatedHours" className="block text-sm font-medium text-gray-700 mb-1">
                  Estimated Hours
                </label>
                <input
                  type="number"
                  id="estimatedHours"
                  value={estimatedHours}
                  onChange={(e) => setEstimatedHours(e.target.value)}
                  step="0.5"
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g. 4.5"
                />
              </div>
            </div>

            <div className="mt-8 flex justify-end">
              <button
                type="submit"
                disabled={isSaving}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-sm font-medium text-white flex items-center"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-1" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </form>
        ) : (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="col-span-2 md:col-span-1">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Task Details</h2>

                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Status</p>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                      {task.status ? task.status.charAt(0).toUpperCase() + task.status.slice(1) : 'Not Set'}
                    </span>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Priority</p>
                    {task.priority ? (
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                        {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                      </span>
                    ) : (
                      <span className="text-gray-500">Not set</span>
                    )}
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Due Date</p>
                    <p className="text-gray-900">{task.due_date ? formatDate(task.due_date) : 'No due date'}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Task Type</p>
                    <p className="text-gray-900">{task.task_type ? task.task_type.charAt(0).toUpperCase() + task.task_type.slice(1) : 'Not specified'}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Estimated Hours</p>
                    <p className="text-gray-900">{task.estimated_hours !== null ? `${task.estimated_hours} hours` : 'Not estimated'}</p>
                  </div>
                </div>
              </div>

              <div className="col-span-2 md:col-span-1">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h2>

                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Project</p>
                    {task.projects ? (
                      <div>
                        <Link href={`/projects/${task.projects.id}`} className="text-blue-600 hover:text-blue-800">
                          {task.projects.name}
                        </Link>
                        {task.projects.companies && (
                          <p className="text-sm text-gray-500">
                            Company: <Link href={`/companies/${task.projects.companies.id}`} className="text-gray-600 hover:text-gray-800">
                              {task.projects.companies.name}
                            </Link>
                          </p>
                        )}
                      </div>
                    ) : (
                      <p className="text-gray-500">Not assigned to a project</p>
                    )}
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Assigned To</p>
                    {task.assigned_to ? (
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                          <Users className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">
                            {assignedUserEmail || `User ID: ${task.assigned_to.substring(0, 8)}...`}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <p className="text-gray-500">Unassigned</p>
                    )}
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Created</p>
                    <p className="text-gray-900">{task.created_at ? formatDate(task.created_at) : 'Unknown'}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Last Updated</p>
                    <p className="text-gray-900">{task.updated_at ? formatDate(task.updated_at) : 'Unknown'}</p>
                  </div>
                </div>
              </div>

              {task.description && (
                <div className="col-span-2">
                  <h2 className="text-lg font-medium text-gray-900 mb-2">Description</h2>
                  <div className="bg-gray-50 p-4 rounded-md">
                    <p className="text-gray-900 whitespace-pre-wrap">{task.description}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
