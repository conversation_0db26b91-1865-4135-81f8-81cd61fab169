'use client'

import Sidebar from '../components/Sidebar'
import Header from '../components/Header' // Assuming this is the correct header to pair with the sidebar

export default function CalendarLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar />
      <div className="ml-64"> {/* This margin creates space for the sidebar */}
        <Header />
        <main className="p-6 mt-16 max-w-7xl mx-auto"> {/* Adjusted classes for consistency */}
          {children}
        </main>
      </div>
    </div>
  )
} 