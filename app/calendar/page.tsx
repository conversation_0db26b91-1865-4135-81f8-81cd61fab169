'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight, Plus, Info, AlertTriangle, CheckCircle, Clock, Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { useSupabase } from '@/app/components/SupabaseProvider'

interface CalendarEvent {
  id: string;
  title: string;
  date: string;
  type: string;
  details?: string;
  status?: string;
  project_id?: string;
  time?: string;
}

interface Task {
  id: string;
  name: string;
  due_date: string;
  description?: string | null;
  status?: string | null;
  task_type?: string | null;
  project_id?: string | null;
}

export default function CalendarPage() {
  const { supabase } = useSupabase()
  const [currentDate, setCurrentDate] = useState(new Date())
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filterCompany, ] = useState<string | null>(null)

  useEffect(() => {
    const fetchTasks = async () => {
      setIsLoading(true)
      setError(null)

      if (!supabase) {
        setError('Supabase client is not available.')
        setIsLoading(false)
        return
      }

      try {
        const { data: tasksData, error: tasksError } = await supabase
          .from('tasks')
          .select('id, name, due_date, description, status, task_type, project_id')
          .order('due_date', { ascending: true })

        if (tasksError) {
          throw tasksError
        }

        if (tasksData) {
          const mappedEvents: CalendarEvent[] = tasksData.map((task: Task) => ({
            id: task.id,
            title: task.name,
            date: task.due_date,
            type: task.task_type || task.status || 'Task',
            details: task.description || undefined,
            status: task.status || undefined,
            project_id: task.project_id || undefined,
          }))
          setEvents(mappedEvents)
        }
      } catch (err: unknown) {
        console.error('Error fetching tasks:', err)
        if (err instanceof Error) {
            setError(err.message || 'Failed to fetch tasks.')
        } else {
            setError('An unknown error occurred while fetching tasks.')
        }
      }
      setIsLoading(false)
    }

    fetchTasks()
  }, [supabase, currentDate])

  const handlePrevMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1))
  }

  const handleNextMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1))
  }

  const filteredEvents = filterCompany
    ? events.filter(event => event.project_id === filterCompany)
    : events

  const groupedEvents: { [key: string]: CalendarEvent[] } = filteredEvents.reduce((acc, event) => {
    if (!event.date) return acc
    const eventDateString = new Date(event.date).toISOString().split('T')[0]
    if (!acc[eventDateString]) {
      acc[eventDateString] = []
    }
    acc[eventDateString].push(event)
    return acc
  }, {} as { [key: string]: CalendarEvent[] })

  const getIconForType = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'meeting': return <CalendarIcon className="w-4 h-4 mr-2 text-blue-500" />
      case 'deadline': return <AlertTriangle className="w-4 h-4 mr-2 text-red-500" />
      case 'completed':
      case 'done': return <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
      case 'pending':
      case 'in progress': return <Clock className="w-4 h-4 mr-2 text-yellow-500" />
      default: return <Info className="w-4 h-4 mr-2 text-gray-500" />
    }
  }
  
  const getBadgeVariant = (type: string): "default" | "destructive" | "outline" | "secondary" => {
    switch (type?.toLowerCase()) {
      case 'meeting': return 'default'
      case 'deadline': return 'destructive'
      case 'completed':
      case 'done': return 'default'
      case 'pending': return 'secondary'
      case 'in progress': return 'outline'
      default: return 'outline'
    }
  }

  return (
    <div className="flex flex-col h-full p-4 md:p-6 bg-gray-50">
      <header className="mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" onClick={handlePrevMonth}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h2 className="text-lg font-medium">
              {currentDate ? new Date(currentDate).toLocaleString('default', { month: 'long', year: 'numeric' }) : 'Loading...'}
            </h2>
            <Button variant="outline" size="icon" onClick={handleNextMonth}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" className="flex items-center gap-2" onClick={() => alert('Filter functionality to be implemented')}>
              <Filter className="h-4 w-4" />
              {filterCompany ? 'Filtered' : 'All Projects'}
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Link href="/tasks/new">
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                New Task
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {isLoading && (
        <div className="flex items-center justify-center flex-grow">
          <p className="text-gray-500">Loading calendar events...</p>
        </div>
      )}
      {error && (
        <div className="flex items-center justify-center flex-grow">
          <p className="text-red-500">Error: {error}</p>
        </div>
      )}

      {!isLoading && !error && Object.keys(groupedEvents).length === 0 && (
        <div className="flex items-center justify-center flex-grow">
          <p className="text-gray-500">No tasks found for this period.</p>
        </div>
      )}

      {!isLoading && !error && Object.keys(groupedEvents).length > 0 && (
        <div className="space-y-6 overflow-y-auto flex-grow">
          {Object.entries(groupedEvents).sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime()).map(([date, dayEvents]) => (
            <div key={date}>
              <h3 className="text-lg font-medium text-gray-800 mb-2">
                {new Date(date).toLocaleDateString('default', { weekday: 'long', month: 'long', day: 'numeric' })}
              </h3>
              <div className="space-y-3">
                {dayEvents.map(event => (
                  <Card key={event.id} className="shadow-sm hover:shadow-md transition-shadow bg-white">
                    <CardHeader className="pb-2 pt-3 px-4">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base font-semibold text-gray-700">{event.title}</CardTitle>
                        <Badge variant={getBadgeVariant(event.type)} className="capitalize">
                          {event.type}
                        </Badge>
                      </div>
                    </CardHeader>
                    {event.details && (
                      <CardContent className="text-xs text-gray-600 pt-0 pb-2 px-4">
                        <p className="line-clamp-2">{event.details}</p>
                      </CardContent>
                    )}
                    <CardFooter className="text-xs text-gray-500 flex items-center justify-between pt-1 pb-3 px-4">
                      <div className="flex items-center">
                        {getIconForType(event.status || event.type)}
                        <span className="capitalize">{event.status || 'No status'}</span>
                      </div>
                      {event.project_id && (
                        <Link href={`/projects/${event.project_id}`} className="hover:underline text-blue-600">
                          View Project
                        </Link>
                      )}
                    </CardFooter>
                  </Card>
                ))}
              </div>
              <Separator className="mt-4" />
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
