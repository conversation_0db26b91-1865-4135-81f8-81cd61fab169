'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Building, Briefcase, Key, Receipt, Clock, ArrowRight } from 'lucide-react'

// Simple currency formatter
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
}

interface DashboardData {
  summary: {
    total_companies: number;
    active_projects: number;
    pending_tasks: number;
    expiring_credentials: number;
    unpaid_invoices: number;
    total_outstanding: number;
  };
  recentProjects: {
    id: string;
    name: string;
    status: string;
    companies: {
      id: string;
      name: string;
    };
  }[];
  recentInvoices: {
    id: string;
    invoice_number: string;
    amount: number;
    status: string;
    companies: {
      id: string;
      name: string;
    };
  }[];
}

export default function Dashboard() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    summary: {
      total_companies: 0,
      active_projects: 0,
      pending_tasks: 0,
      expiring_credentials: 0,
      unpaid_invoices: 0,
      total_outstanding: 0
    },
    recentProjects: [],
    recentInvoices: []
  })

  useEffect(() => {
    async function fetchDashboardData() {
      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch('/api/admin/dashboard')

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`)
        }

        const data = await response.json()
        setDashboardData(data)
      } catch (err) {
        console.error('Error fetching dashboard data:', err)
        setError('Failed to load dashboard data. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  const stats = [
    {
      title: 'Companies',
      value: dashboardData.summary?.total_companies || 0,
      icon: Building,
      color: 'bg-blue-500',
      link: '/companies'
    },
    {
      title: 'Active Projects',
      value: dashboardData.summary?.active_projects || 0,
      icon: Briefcase,
      color: 'bg-emerald-500',
      link: '/projects'
    },
    {
      title: 'Pending Tasks',
      value: dashboardData.summary?.pending_tasks || 0,
      icon: Clock,
      color: 'bg-amber-500',
      link: '/projects?filter=tasks'
    },
    {
      title: 'Expiring Credentials',
      value: dashboardData.summary?.expiring_credentials || 0,
      icon: Key,
      color: 'bg-purple-500',
      link: '/credentials?filter=expiring'
    },
    {
      title: 'Unpaid Invoices',
      value: dashboardData.summary?.unpaid_invoices || 0,
      icon: Receipt,
      color: 'bg-red-500',
      link: '/invoices?filter=unpaid'
    },
    {
      title: 'Outstanding Amount',
      value: formatCurrency(dashboardData.summary?.total_outstanding || 0),
      icon: Receipt,
      color: 'bg-indigo-500',
      link: '/invoices'
    },
  ]

  return (
    <div>
      <div className="mb-8">
        <div className="flex items-center gap-2">
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <div className="bg-primary/10 text-primary px-2 py-0.5 text-xs rounded-md font-medium">v2.0.4</div>
        </div>
        <p className="text-muted-foreground mt-2">Welcome to your Developer CRM dashboard. Here&apos;s an overview of your business.</p>
      </div>

      {/* Dashboard content */}
      <>
          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {stats.map((stat, index) => (
              <Link
                href={stat.link}
                key={index}
                className="card p-6 group hover:scale-[1.02] transition-all duration-300 ease-out-back border border-border/40 dark:bg-card/80 backdrop-blur-sm"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`p-3 rounded-lg ${stat.color} group-hover:scale-110 transition-transform shadow-sm`}>
                      <stat.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                      <p className="text-2xl font-semibold text-foreground">{stat.value}</p>
                    </div>
                  </div>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="p-2 rounded-full bg-background/80 dark:bg-background/30 backdrop-blur-sm">
                      <ArrowRight className="h-4 w-4 text-primary" />
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {/* Recent Projects & Invoices */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Projects */}
            <div className="card overflow-hidden border border-border/40 dark:bg-card/80 backdrop-blur-sm">
              <div className="p-6 border-b border-border/60">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <Briefcase className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-medium">Recent Projects</h3>
                  </div>
                  <Link
                    href="/projects"
                    className="text-sm text-primary hover:text-primary/80 flex items-center gap-1 px-2 py-1 rounded-md hover:bg-primary/10 transition-colors"
                  >
                    View all <ArrowRight className="h-4 w-4" />
                  </Link>
                </div>
              </div>
              <div className="p-4">
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  </div>
                ) : error ? (
                  <div className="text-center py-8 px-4">
                    <p className="text-destructive">Error loading projects</p>
                    <p className="text-sm text-muted-foreground mt-1">Please try refreshing the page</p>
                  </div>
                ) : dashboardData.recentProjects.length > 0 ? (
                  <ul className="divide-y divide-border/60">
                    {dashboardData.recentProjects.map((project) => (
                      <li key={project.id} className="py-2">
                        <Link
                          href={`/projects/${project.id}`}
                          className="flex justify-between hover:bg-muted/30 dark:hover:bg-muted/10 p-3 rounded-lg transition-all duration-200 hover:shadow-sm -mx-2"
                        >
                          <div>
                            <p className="text-sm font-medium">{project.name}</p>
                            <p className="text-sm text-muted-foreground mt-0.5">{project.companies?.name}</p>
                          </div>
                          <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                            project.status === 'active' ? 'bg-success/10 text-success border border-success/20' :
                            project.status === 'completed' ? 'bg-primary/10 text-primary border border-primary/20' :
                            'bg-muted/80 text-muted-foreground dark:bg-muted/20'
                          }`}>
                            {project.status}
                          </span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="text-center py-8 px-4">
                    <p className="text-muted-foreground">No recent projects</p>
                    <Link href="/projects/new" className="text-sm text-primary hover:underline mt-2 inline-block">
                      Create a new project
                    </Link>
                  </div>
                )}
              </div>
            </div>

            {/* Recent Invoices */}
            <div className="card overflow-hidden border border-border/40 dark:bg-card/80 backdrop-blur-sm">
              <div className="p-6 border-b border-border/60">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <Receipt className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-medium">Recent Invoices</h3>
                  </div>
                  <Link
                    href="/invoices"
                    className="text-sm text-primary hover:text-primary/80 flex items-center gap-1 px-2 py-1 rounded-md hover:bg-primary/10 transition-colors"
                  >
                    View all <ArrowRight className="h-4 w-4" />
                  </Link>
                </div>
              </div>
              <div className="p-4">
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  </div>
                ) : error ? (
                  <div className="text-center py-8 px-4">
                    <p className="text-destructive">Error loading invoices</p>
                    <p className="text-sm text-muted-foreground mt-1">Please try refreshing the page</p>
                  </div>
                ) : dashboardData.recentInvoices.length > 0 ? (
                  <ul className="divide-y divide-border/60">
                    {dashboardData.recentInvoices.map((invoice) => (
                      <li key={invoice.id} className="py-2">
                        <Link
                          href={`/invoices/${invoice.id}`}
                          className="flex justify-between hover:bg-muted/30 dark:hover:bg-muted/10 p-3 rounded-lg transition-all duration-200 hover:shadow-sm -mx-2"
                        >
                          <div>
                            <p className="text-sm font-medium">#{invoice.invoice_number}</p>
                            <p className="text-sm text-muted-foreground mt-0.5">{invoice.companies?.name}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">{formatCurrency(invoice.amount)}</p>
                            <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium mt-1 ${
                              invoice.status === 'paid' ? 'bg-success/10 text-success border border-success/20' :
                              invoice.status === 'overdue' ? 'bg-destructive/10 text-destructive border border-destructive/20' :
                              'bg-warning/10 text-warning-600 border border-warning/20'
                            }`}>
                              {invoice.status}
                            </span>
                          </div>
                        </Link>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="text-center py-8 px-4">
                    <p className="text-muted-foreground">No recent invoices</p>
                    <Link href="/invoices/new" className="text-sm text-primary hover:underline mt-2 inline-block">
                      Create a new invoice
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
    </div>
  )
}