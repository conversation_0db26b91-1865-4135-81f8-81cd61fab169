@tailwind base;
@tailwind components;
@tailwind utilities;

/*
@tailwind base;
@tailwind components;
@tailwind utilities;
*/

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 224 71% 4%;

    --card: 0 0% 100%;
    --card-foreground: 224 71% 4%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71% 4%;

    --primary: 222 83% 55%;
    --primary-foreground: 0 0% 100%;

    --secondary: 252 87% 67%;
    --secondary-foreground: 0 0% 100%;

    --muted: 220 14% 96%;
    --muted-foreground: 220 8% 46%;

    --accent: 210 40% 96%;
    --accent-foreground: 224 71% 4%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 224 71% 4%;

    --radius: 0.75rem;

    --chart-1: 224 82% 56%;
    --chart-2: 252 87% 67%;
    --chart-3: 142 76% 36%;
    --chart-4: 38 92% 50%;
    --chart-5: 320 90% 56%;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 210 20% 98%;

    --card: 224 71% 4%;
    --card-foreground: 210 20% 98%;

    --popover: 224 71% 4%;
    --popover-foreground: 210 20% 98%;

    --primary: 224 82% 65%;
    --primary-foreground: 0 0% 100%;

    --secondary: 252 87% 67%;
    --secondary-foreground: 0 0% 100%;

    --muted: 215 27% 16%;
    --muted-foreground: 217 14% 64%;

    --accent: 215 27% 16%;
    --accent-foreground: 210 20% 98%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 210 20% 98%;

    --success: 142 70% 45%;
    --success-foreground: 0 0% 100%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;

    --border: 215 27% 16%;
    --input: 215 27% 16%;
    --ring: 216 12% 84%;

    --chart-1: 224 82% 65%;
    --chart-2: 252 87% 76%;
    --chart-3: 142 70% 45%;
    --chart-4: 38 92% 60%;
    --chart-5: 320 90% 65%;
  }
}

/* ORIGINAL CONTENT UNCOMMENTED BELOW */

@layer base {
  html {
    scroll-behavior: smooth;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-medium tracking-tight;
  }

  h1 {
    @apply text-3xl sm:text-4xl font-bold;
  }

  h2 {
    @apply text-2xl sm:text-3xl font-semibold;
  }

  h3 {
    @apply text-xl sm:text-2xl font-medium;
  }

  /* Grid pattern background */
  .bg-grid-pattern {
    background-image:
      linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
  }
}

@layer components {
  .card {
    @apply bg-card text-card-foreground rounded-xl border border-border shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm;
  }

  /* Add a subtle gradient hover effect to cards */
  .card:hover {
    @apply bg-card/90 dark:bg-card/80;
    transform: translateY(-2px);
  }

  /* Dark mode specific card styles */
  .dark .card {
    @apply border-border/30 shadow-md;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .card-header {
    @apply p-6 flex flex-col space-y-1.5;
  }

  .card-title {
    @apply text-lg font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply p-6 pt-0 flex items-center;
  }

  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-primary {
    @apply bg-primary/10 text-primary-foreground border border-primary/20;
  }

  .badge-secondary {
    @apply bg-secondary text-secondary-foreground;
  }

  .badge-success {
    @apply bg-success/10 text-success border border-success/20;
  }

  .badge-destructive {
    @apply bg-destructive/10 text-destructive border border-destructive/20;
  }

  .badge-warning {
    @apply bg-warning/10 text-warning border border-warning/20;
  }

  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
  }

  .btn-outline {
    @apply border border-input hover:bg-accent hover:text-accent-foreground;
  }
}

@layer utilities {
  .animate-in {
    animation-duration: 300ms;
    animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
    will-change: transform, opacity;
  }

  .fade-in-0 {
    opacity: 0;
  }

  .fade-in-100 {
    opacity: 1;
  }

  .slide-in-from-bottom-8 {
    transform: translateY(8px);
  }

  .slide-in-from-top-8 {
    transform: translateY(-8px);
  }

  .slide-in-to-origin {
    transform: translateY(0);
  }
}

/* All other content from globals.css is temporarily commented out for this test

@layer base {
  html {
    scroll-behavior: smooth;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-medium tracking-tight;
  }

  h1 {
    @apply text-3xl sm:text-4xl font-bold;
  }

  h2 {
    @apply text-2xl sm:text-3xl font-semibold;
  }

  h3 {
    @apply text-xl sm:text-2xl font-medium;
  }
}

@layer components {
  .card {
    @apply bg-card text-card-foreground rounded-xl border border-border shadow-sm hover:shadow-md transition-all duration-200;
  }

  .card-header {
    @apply p-6 flex flex-col space-y-1.5;
  }

  .card-title {
    @apply text-lg font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply p-6 pt-0 flex items-center;
  }

  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-primary {
    @apply bg-primary/10 text-primary-foreground border border-primary/20;
  }

  .badge-secondary {
    @apply bg-secondary text-secondary-foreground;
  }

  .badge-success {
    @apply bg-success/10 text-success border border-success/20;
  }

  .badge-destructive {
    @apply bg-destructive/10 text-destructive border border-destructive/20;
  }

  .badge-warning {
    @apply bg-warning/10 text-warning border border-warning/20;
  }

  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
  }

  .btn-outline {
    @apply border border-input hover:bg-accent hover:text-accent-foreground;
  }
}

@layer utilities {
  .animate-in {
    animation-duration: 300ms;
    animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
    will-change: transform, opacity;
  }

  .fade-in-0 {
    opacity: 0;
  }

  .fade-in-100 {
    opacity: 1;
  }

  .slide-in-from-bottom-8 {
    transform: translateY(8px);
  }

  .slide-in-from-top-8 {
    transform: translateY(-8px);
  }

  .slide-in-to-origin {
    transform: translateY(0);
  }
}

*/