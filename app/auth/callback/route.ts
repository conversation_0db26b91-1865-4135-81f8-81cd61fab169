import { createServerClient } from '@supabase/ssr'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const next = requestUrl.searchParams.get('next') || '/dashboard'

  console.log('Auth callback received with code:', code ? 'present' : 'missing')

  if (!code) {
    console.error('No code provided in auth callback')
    // If no code is provided, redirect to the sign-in page
    return NextResponse.redirect(new URL('/sign-in?error=missing_code', request.url))
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Missing Supabase environment variables')
    return NextResponse.redirect(new URL('/sign-in?error=configuration_error', request.url))
  }

  try {
    // Add cache busting timestamp and no redirect counter to prevent middleware loops
    const redirectUrl = new URL(`${next}?cb=${Date.now()}`, request.url);
    
    // Create a response to set cookies on
    const response = NextResponse.redirect(redirectUrl);

    // Create a Supabase client for the auth exchange
    const supabase = createServerClient(
      supabaseUrl,
      supabaseAnonKey,
      {
        cookies: {
          get(name) {
            const cookie = request.cookies.get(name);
            console.log(`Reading cookie ${name}: ${cookie ? 'present' : 'missing'}`);
            return cookie?.value;
          },
          set(name, value, options) {
            // This is used for setting cookies in the response
            console.log(`Setting cookie ${name} with options:`, options);
            
            // Set default options for better cookie security and reliability
            const cookieOptions = {
              ...options,
              // Ensure cookies are accessible for JavaScript
              httpOnly: name.includes('access_token') ? true : false,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'lax' as const,
              path: '/',
              // Prevent cookie from expiring prematurely
              maxAge: name.includes('refresh_token') ? 60 * 60 * 24 * 30 : undefined, // 30 days for refresh token
            };
            
            response.cookies.set({
              name,
              value,
              ...cookieOptions,
            });
          },
          remove(name, options) {
            console.log(`Removing cookie ${name}`);
            response.cookies.set({
              name,
              value: '',
              path: '/',
              ...options,
              expires: new Date(0),
            });
          },
        },
      }
    );

    // Exchange the code for a session
    console.log('Exchanging code for session...');
    const { error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);

    if (exchangeError) {
      console.error('Error exchanging code for session:', exchangeError.message);
      return NextResponse.redirect(new URL(`/sign-in?error=${encodeURIComponent('Authentication failed: ' + exchangeError.message)}`, request.url));
    }

    // Verify that we actually got a session
    console.log('Verifying session was created');
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error('Error getting session after exchange:', sessionError.message);
      return NextResponse.redirect(new URL(`/sign-in?error=${encodeURIComponent('Session verification failed')}`, request.url));
    }

    if (!session) {
      console.error('No session after exchanging code');
      return NextResponse.redirect(new URL(`/sign-in?error=${encodeURIComponent('No session established')}`, request.url));
    }

    console.log('Authentication successful, redirecting to dashboard');
    console.log('Session expires at:', new Date(session.expires_at! * 1000).toISOString());
    console.log('Access token present:', !!session.access_token);
    console.log('Refresh token present:', !!session.refresh_token);
    
    // Add debug cookies to check in the browser
    response.cookies.set({
      name: 'auth-debug-timestamp',
      value: new Date().toISOString(),
      maxAge: 60 * 5, // 5 minutes
      path: '/',
    });

    // List all cookies being set in the response
    const cookieHeaders = response.headers.getSetCookie();
    console.log('Response cookies:', cookieHeaders.map(c => c.split(';')[0]));

    // Return the response with cookies set
    return response;
  } catch (error) {
    console.error('Unexpected error during authentication:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.redirect(new URL(`/sign-in?error=${encodeURIComponent('Authentication error: ' + errorMessage)}`, request.url));
  }
}
