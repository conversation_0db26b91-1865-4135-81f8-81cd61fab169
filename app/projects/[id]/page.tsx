'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  ArrowLeft,
  Building,
  Calendar,
  Clock,
  CheckCircle2,
  AlertCircle,
  Edit,
  Trash2,
  Plus,
  Users,
  DollarSign
} from 'lucide-react'
import { useSupabase } from '../../components/SupabaseProvider'
import { formatDate, getStatusBadgeClass } from '../../../lib/utils'

interface Project {
  id: string
  name: string
  description: string | null
  start_date: string | null
  expected_end_date: string | null
  status: string | null
  value: number | null
  company_id: string | null
  created_at: string | null
  updated_at: string | null
  companies?: {
    id: string
    name: string
  } | null
}

interface Task {
  id: string
  name: string
  description: string | null
  status: string | null
  priority: string | null
  due_date: string | null
  estimated_hours: number | null
  assigned_to: string | null
}

export default function ProjectDetail({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { supabase } = useSupabase()
  const [project, setProject] = useState<Project | null>(null)
  const [tasks, setTasks] = useState<Task[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchProjectData() {
      setIsLoading(true)
      setError(null)

      try {
        // Fetch project details, explicitly typing the result as Project
        // The select query includes 'value' field which represents the project budget.
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select(`
            id,
            name,
            description,
            start_date,
            expected_end_date,
            status,
            value,
            company_id,
            created_at,
            updated_at,
            companies (id, name)
          `)
          .eq('id', params.id)
          .single<Project>() // Explicitly type the expected return data structure

        if (projectError) throw projectError

        if (projectData) {
          // projectData should now be correctly typed as Project | null
          // and contain the 'value' field which represents the project budget.
          setProject({
            ...projectData,
            // Ensure companies is handled correctly as it might be an array from join
            companies: Array.isArray(projectData.companies) ? (projectData.companies[0] || null) : projectData.companies,
          });
        }

        // Fetch related tasks
        const { data: tasksData, error: tasksError } = await supabase
          .from('tasks')
          .select(`
            id,
            name,
            description,
            status,
            priority,
            due_date,
            estimated_hours,
            assigned_to
          `)
          .eq('project_id', params.id)
          .order('due_date', { ascending: true })

        if (tasksError) throw tasksError

        if (tasksData) {
          setTasks(tasksData || []);
        }

      } catch (err: unknown) {
        console.error('Error fetching project data:', err)
        setError('Failed to load project data. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    fetchProjectData()
  }, [params.id, supabase])

  // Calculate project progress based on tasks
  const calculateProgress = () => {
    if (!tasks.length) return 0

    const completedTasks = tasks.filter(task => task.status === 'completed').length
    return Math.round((completedTasks / tasks.length) * 100)
  }

  // Get task counts by status
  const getTaskCounts = () => {
    const completed = tasks.filter(task => task.status === 'completed').length
    const inProgress = tasks.filter(task => task.status === 'in_progress').length
    const pending = tasks.filter(task => task.status === 'pending').length
    const blocked = tasks.filter(task => task.status === 'blocked').length

    return { completed, inProgress, pending, blocked, total: tasks.length }
  }

  // Get status color for tasks
  const getTaskStatusColor = (status: string | null) => {
    if (!status) return 'bg-gray-100 text-gray-800'

    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'blocked':
        return 'bg-red-100 text-red-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Get priority color
  const getPriorityColor = (priority: string | null) => {
    if (!priority) return 'bg-gray-100 text-gray-800'

    switch (priority.toLowerCase()) {
      case 'low':
        return 'bg-blue-100 text-blue-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'urgent':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (error || !project) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative max-w-md w-full" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error || 'Failed to load project data. Please try again.'}</span>
          <div className="mt-4">
            <Link href="/projects" className="text-red-700 underline">
              Return to projects list
            </Link>
          </div>
        </div>
      </div>
    )
  }

  const taskCounts = getTaskCounts()
  const progress = calculateProgress()

  return (
    <div>
      {/* Header with back button and actions */}
      <div className="mb-6">
        <Link
          href="/projects"
          className="text-gray-500 hover:text-gray-700 inline-flex items-center"
        >
          <ArrowLeft size={16} className="mr-1" />
          Back to Projects
        </Link>

        <div className="flex justify-between items-center mt-4">
          <h1 className="text-2xl font-bold text-gray-900">{project.name}</h1>

          <div className="flex space-x-2">
            <Link
              href={`/projects/${project.id}/edit`}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center"
            >
              <Edit size={16} className="mr-1" />
              Edit
            </Link>
            <button
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md flex items-center"
              onClick={async () => {
                if (window.confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
                  try {
                    const { error } = await supabase
                      .from('projects')
                      .delete()
                      .eq('id', project.id)

                    if (error) throw error

                    // Navigate back to projects list
                    router.push('/projects')
                    router.refresh()
                  } catch (err) {
                    console.error('Error deleting project:', err)
                    alert('Failed to delete project. Please try again.')
                  }
                }
              }}
            >
              <Trash2 size={16} className="mr-1" />
              Delete
            </button>
          </div>
        </div>

        {project.status && (
          <div className="mt-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(project.status)}`}>
              {project.status}
            </span>
          </div>
        )}
      </div>

      {/* Project details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-5 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Project Details</h2>
          </div>

          <div className="px-6 py-5">
            {project.description && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Description</h3>
                <p className="text-sm text-gray-900">{project.description}</p>
              </div>
            )}

            <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
              {project.companies && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 flex items-center">
                    <Building className="w-4 h-4 mr-1" />
                    Client Company
                  </dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    <Link
                      href={`/companies/${project.companies.id}`}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      {project.companies.name}
                    </Link>
                  </dd>
                </div>
              )}

              {project.start_date && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    Start Date
                  </dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {formatDate(project.start_date)}
                  </dd>
                </div>
              )}

              {project.expected_end_date && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    End Date
                  </dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {formatDate(project.expected_end_date)}
                  </dd>
                </div>
              )}

              {project.value !== null && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 flex items-center">
                    <DollarSign className="w-4 h-4 mr-1" />
                    Budget
                  </dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    ${project.value.toFixed(2)}
                  </dd>
                </div>
              )}

              <div>
                <dt className="text-sm font-medium text-gray-500">Created</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {project.created_at ? formatDate(project.created_at) : 'Unknown'}
                </dd>
              </div>
            </dl>
          </div>
        </div>

        {/* Project progress card */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-5 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Progress</h2>
          </div>

          <div className="px-6 py-5">
            <div className="mb-4">
              <div className="flex justify-between items-center mb-1">
                <span className="text-sm font-medium text-gray-700">{progress}% Complete</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-blue-600 h-2.5 rounded-full"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-6">
              <div className="bg-green-50 p-3 rounded-lg">
                <div className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                  <span className="text-sm font-medium text-gray-700">Completed</span>
                </div>
                <p className="text-2xl font-bold text-gray-900 mt-1">{taskCounts.completed}</p>
              </div>

              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-blue-500 mr-2" />
                  <span className="text-sm font-medium text-gray-700">In Progress</span>
                </div>
                <p className="text-2xl font-bold text-gray-900 mt-1">{taskCounts.inProgress}</p>
              </div>

              <div className="bg-yellow-50 p-3 rounded-lg">
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-yellow-500 mr-2" />
                  <span className="text-sm font-medium text-gray-700">Pending</span>
                </div>
                <p className="text-2xl font-bold text-gray-900 mt-1">{taskCounts.pending}</p>
              </div>

              <div className="bg-red-50 p-3 rounded-lg">
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                  <span className="text-sm font-medium text-gray-700">Blocked</span>
                </div>
                <p className="text-2xl font-bold text-gray-900 mt-1">{taskCounts.blocked}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tasks section */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-5 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-lg font-medium text-gray-900 flex items-center">
            <CheckCircle2 className="w-5 h-5 mr-2 text-gray-500" />
            Tasks
          </h2>
          <Link
            href={`/tasks/new?project=${project.id}`}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            <Plus size={16} className="inline mr-1" />
            Add Task
          </Link>
        </div>

        <div className="px-6 py-5">
          {tasks.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Task
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Priority
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Due Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Assigned To
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {tasks.map(task => (
                    <tr key={task.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{task.name}</div>
                        {task.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">{task.description}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getTaskStatusColor(task.status)}`}>
                          {task.status || 'Not Set'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {task.priority && (
                          <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                            {task.priority}
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {task.due_date ? formatDate(task.due_date) : '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {task.assigned_to ? (
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                              <Users className="h-4 w-4 text-blue-600" />
                            </div>
                            <div className="ml-3">
                              <div className="text-sm font-medium text-gray-900">
                                {task.assigned_to}
                              </div>
                            </div>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-500">Unassigned</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="mx-auto h-12 w-12 text-gray-400 rounded-full bg-gray-100 flex items-center justify-center">
                <CheckCircle2 className="h-6 w-6" />
              </div>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No tasks</h3>
              <p className="mt-1 text-sm text-gray-500">Get started by creating a new task.</p>
              <div className="mt-6">
                <Link
                  href={`/tasks/new?project=${project.id}`}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                  New Task
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
