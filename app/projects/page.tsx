'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Plus, Search, Briefcase, Clock, CheckCircle2, AlertCircle, Filter, Shield } from 'lucide-react'
import { formatDate, getStatusBadgeClass } from '@/lib/utils'
import { useSupabase } from '@/app/components/SupabaseProvider'
import ViewToggle from '@/app/components/ViewToggle'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'

// Type definitions based on actual Supabase response
interface Company {
  name: string
}

// Interface for raw Supabase response
interface SupabaseProject {
  id: string
  name: string
  description: string | null
  start_date: string | null
  expected_end_date: string | null
  status: string | null
  company_id: string | null
  companies: Company | Company[] | null
  created_at?: string
}

// Define the processed project with our frontend additions
interface Project {
  id: string
  name: string
  description: string | null
  start_date: string | null
  end_date: string | null
  status: string | null
  company_id: string | null
  companies: Company | null
  _count?: {
    tasks: number
    pending_tasks: number
  }
}

export default function Projects() {
  const [projects, setProjects] = useState<Project[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'card' | 'table'>('card')
  const { supabase } = useSupabase()

  useEffect(() => {
    async function fetchProjects() {
      setIsLoading(true)
      setError(null)

      try {
        console.log('Fetching projects data...');

        // Use the Supabase client from context
        const { data, error } = await supabase
          .from('projects')
          .select(`
            id,
            name,
            description,
            start_date,
            expected_end_date,
            status,
            company_id,
            companies (name)
          `)
          .order('created_at', { ascending: false })

        if (error) {
          console.error('Error fetching projects:', error)
          setError('Failed to load projects. Please try again later.')
          setProjects([])
        } else if (data) {
          console.log(`Successfully fetched ${data.length} projects`);
          console.log('Sample project data:', data[0]);

          // Create dummy task counts for demonstration purposes
          const projectsWithCounts = data.map((project: SupabaseProject) => {
            const taskCount = Math.floor(Math.random() * 10)
            const pendingTaskCount = Math.floor(Math.random() * Math.min(5, taskCount + 1))

            // Normalize the companies data to ensure it's in the expected format
            let normalizedCompanies: Company | null = null;

            if (project.companies) {
              // Handle various possible data structures from Supabase
              if (Array.isArray(project.companies)) {
                if (project.companies.length > 0) {
                  // If it's an array with items, take the first company
                  normalizedCompanies = {
                    name: project.companies[0].name || 'Unknown'
                  };
                }
              } else {
                // If it's a single object
                normalizedCompanies = {
                  name: (project.companies as Company).name || 'Unknown'
                };
              }
            }

            const projectWithCount: Project = {
              id: project.id,
              name: project.name,
              description: project.description,
              start_date: project.start_date,
              end_date: project.expected_end_date, // Map expected_end_date to end_date
              status: project.status,
              company_id: project.company_id,
              companies: normalizedCompanies,
              _count: {
                tasks: taskCount,
                pending_tasks: pendingTaskCount
              }
            }

            return projectWithCount
          })

          setProjects(projectsWithCounts)
        }
      } catch (err) {
        console.error('Unexpected error:', err)
        setError('An unexpected error occurred. Please try again.')
        setProjects([])
      }

      setIsLoading(false)
    }

    fetchProjects()
  }, [supabase])

  // Filter projects based on search query and status filter
  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.companies?.name?.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesStatus = !statusFilter || project.status === statusFilter

    return matchesSearch && matchesStatus
  })

  // Calculate project progress and status
  const getProjectStatus = (project: Project) => {
    if (!project.status) return '-'
    return project.status.charAt(0).toUpperCase() + project.status.slice(1)
  }

  // Get project progress - this would be a real calculation in production
  const getProjectProgress = (project: Project) => {
    if (project.status === 'completed') return 100

    if (project._count && project._count.tasks > 0) {
      const completedTasks = project._count.tasks - project._count.pending_tasks
      return Math.round((completedTasks / project._count.tasks) * 100)
    }

    return 0
  }

  // Available status filters
  const statusOptions = [
    { value: null, label: 'All Statuses' },
    { value: 'active', label: 'Active' },
    { value: 'completed', label: 'Completed' },
    { value: 'on hold', label: 'On Hold' },
    { value: 'planning', label: 'Planning' }
  ]

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Projects</h1>
          <p className="text-gray-600">Manage and track your client projects</p>
        </div>

        <Link
          href="/projects/new"
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <Plus size={18} className="mr-1" />
          New Project
        </Link>
      </div>

      {/* Search and filters */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative w-full sm:w-96">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-5 h-5 text-gray-400" />
          </div>
          <input
            type="search"
            className="block w-full py-2 pl-10 pr-3 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search projects..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="relative w-full sm:w-64">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Filter className="w-5 h-5 text-gray-400" />
          </div>
          <select
            className="block w-full py-2 pl-10 pr-3 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={statusFilter || ''}
            onChange={(e) => setStatusFilter(e.target.value || null)}
          >
            {statusOptions.map((option) => (
              <option key={option.label} value={option.value || ''}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div className="ml-auto">
          <ViewToggle onViewChange={setViewMode} initialView={viewMode} />
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded flex items-center">
          <Shield className="h-5 w-5 mr-2" />
          <span>{error}</span>
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      ) : filteredProjects.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="bg-gray-100 rounded-full p-3 mb-3">
            <Briefcase className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-1">No projects found</h3>
          <p className="text-gray-500 max-w-md">
            {searchQuery || statusFilter
              ? 'Try changing your search or filter criteria'
              : 'Add your first project to get started'}
          </p>
          {!searchQuery && !statusFilter && (
            <Link
              href="/projects/new"
              className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
            >
              Add Project
            </Link>
          )}
        </div>
      ) : viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => {
            const progress = getProjectProgress(project)

            return (
              <Link
                key={project.id}
                href={`/projects/${project.id}`}
                className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden"
              >
                <div className="p-5">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center space-x-3">
                      <div className="bg-blue-100 p-2 rounded-lg">
                        <Briefcase className="h-5 w-5 text-blue-600" />
                      </div>
                      <h3 className="font-medium text-gray-900">{project.name}</h3>
                    </div>
                    <Badge
                      variant="outline"
                      className={cn(
                        'text-xs px-2 py-0.5 rounded-full font-medium',
                        getStatusBadgeClass(project.status)
                      )}
                    >
                      {getProjectStatus(project)}
                    </Badge>
                  </div>

                  <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                    {project.description || 'No description provided'}
                  </p>

                  <div className="mt-4">
                    <p className="text-sm text-gray-600 mb-1">Client: {project.companies?.name || 'No client'}</p>
                    <div className="flex items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className="bg-blue-600 h-2.5 rounded-full"
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-500 ml-2">{progress}%</span>
                    </div>
                  </div>

                  <div className="mt-4 flex justify-between items-center text-sm">
                    <div className="flex items-center text-gray-500">
                      <Clock className="h-4 w-4 mr-1" />
                      {project.end_date ? formatDate(project.end_date) : 'No deadline'}
                    </div>

                    <div className="flex space-x-3">
                      <div className="flex items-center text-gray-500">
                        <CheckCircle2 className="h-4 w-4 mr-1 text-green-500" />
                        {project._count ? project._count.tasks - project._count.pending_tasks : 0}
                      </div>
                      <div className="flex items-center text-gray-500">
                        <AlertCircle className="h-4 w-4 mr-1 text-amber-500" />
                        {project._count?.pending_tasks || 0}
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            )
          })}
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Project Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Client
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Deadline
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Progress
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tasks
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredProjects.map((project) => {
                  const progress = getProjectProgress(project)

                  return (
                    <tr key={project.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <Briefcase className="h-5 w-5 text-blue-600" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{project.name}</div>
                            <div className="text-sm text-gray-500 truncate max-w-xs">
                              {project.description || 'No description'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{project.companies?.name || '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge
                          variant="outline"
                          className={cn(
                            'text-xs px-2 py-0.5 rounded-full font-medium',
                            getStatusBadgeClass(project.status)
                          )}
                        >
                          {getProjectStatus(project)}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {project.end_date ? formatDate(project.end_date) : 'No deadline'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-full max-w-xs bg-gray-200 rounded-full h-2.5">
                            <div
                              className="bg-blue-600 h-2.5 rounded-full"
                              style={{ width: `${progress}%` }}
                            ></div>
                          </div>
                          <span className="text-xs text-gray-500 ml-2">{progress}%</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center space-x-2">
                          <span className="flex items-center">
                            <CheckCircle2 className="h-4 w-4 mr-1 text-green-500" />
                            {project._count ? project._count.tasks - project._count.pending_tasks : 0}
                          </span>
                          <span className="flex items-center">
                            <AlertCircle className="h-4 w-4 mr-1 text-amber-500" />
                            {project._count?.pending_tasks || 0}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Link href={`/projects/${project.id}`} className="text-blue-600 hover:text-blue-900">
                          View Details
                        </Link>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}