import React from 'react';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  return (
    <div className="flex h-screen bg-gray-100">
      {/* Placeholder for Admin Sidebar */}
      <aside className="w-64 bg-gray-800 text-white p-4 hidden md:block">
        <div className="mb-8">
          <h2 className="text-2xl font-semibold">Admin Panel</h2>
        </div>
        <nav>
          <ul>
            <li className="mb-2">
              <a href="/admin/messages" className="block p-2 rounded hover:bg-gray-700">Messages</a>
            </li>
            {/* Add other admin navigation links here */}
          </ul>
        </nav>
      </aside>

      {/* Main Content Area */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Placeholder for Admin Header/Navbar */}
        <header className="bg-white shadow p-4">
          <h1 className="text-xl font-semibold">Admin Dashboard</h1>
        </header>
        
        <div className="flex-1 p-6 overflow-auto">
          {children}
        </div>
      </main>
    </div>
  );
} 