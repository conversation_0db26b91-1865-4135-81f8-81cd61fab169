'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation'; // Or from 'next/router' if using Pages Router
// import { Button } from '@/components/ui/button';
// import { Textarea } from '@/components/ui/textarea';
// import { Input } from '@/components/ui/input';
// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { formatDate } from '../../../lib/utils'; // Adjusted path assuming it's same as client-view

// Define interfaces for the data you expect
interface AdminMessage {
  id: string;
  subject?: string | null; // Match API where subject can be null
  body: string;
  is_from_client: boolean;
  created_at: string;
  client_user_id?: string | null;
  company_id: string;
  admin_user_id?: string | null;
  parent_message_id?: string | null;
  updated_at?: string | null; // Added from API response
  read_at?: string | null;    // Added from API response
  company_name?: string | null;
  client_user_name?: string | null;
  admin_user_name?: string | null;
}

const AdminMessagesPage = () => {
  const router = useRouter();
  const [messages, setMessages] = useState<AdminMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // TODO: Add state for selected message/thread, reply text, etc.
  // const [selectedThreadId, setSelectedThreadId] = useState<string | null>(null);
  // const [replyText, setReplyText] = useState('');
  // const [isReplying, setIsReplying] = useState(false);

  const fetchMessages = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/admin/messages');
      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          setError('Unauthorized or Forbidden. Redirecting to sign-in...'); // Set error before redirect
          router.push('/sign-in'); // Corrected redirect path
          console.error('Admin authentication failed. Status:', response.status);
          return; // Stop further execution in this function if auth fails
        }
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch messages');
      }
      const data: AdminMessage[] = await response.json();
      setMessages(data);
      
      // Placeholder data removed

    } catch (err: unknown) {
      console.error('Error fetching messages:', err);
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unexpected error occurred.');
      }
    } finally {
      setLoading(false);
    }
  }, [router]);

  useEffect(() => {
    // TODO: Implement admin authentication check here before fetching
    // For now, directly fetching messages.
    fetchMessages();
  }, [fetchMessages]);

  // TODO: Implement handleReplySubmit function

  if (loading) {
    return <div className="p-6">Loading messages...</div>;
  }

  if (error) {
    return <div className="p-6 text-red-500">Error: {error}</div>;
  }

  return (
    <div className="container mx-auto p-4 md:p-6">
      <h1 className="text-3xl font-bold mb-6">Client Messages</h1>
      
      {messages.length === 0 ? (
        <p>No messages found.</p>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Company</TableHead>
              <TableHead>Client/Sender</TableHead>
              <TableHead>Subject</TableHead>
              <TableHead>Snippet</TableHead>
              <TableHead>Type</TableHead>
              <TableHead className="text-right">Date</TableHead>
              {/* <TableHead>Actions</TableHead> */}
            </TableRow>
          </TableHeader>
          <TableBody>
            {messages.map((msg) => (
              <TableRow key={msg.id} className="hover:bg-muted/50 cursor-pointer"
                // onClick={() => setSelectedThreadId(msg.parent_message_id || msg.id)} // TODO: Implement thread view
              >
                <TableCell className="font-medium">{msg.company_name || 'N/A'}</TableCell>
                <TableCell>{msg.is_from_client ? (msg.client_user_name || 'Client') : (msg.admin_user_name || 'Admin')}</TableCell>
                <TableCell>{msg.subject || '(No Subject)'}</TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {msg.body.substring(0, 50)}{msg.body.length > 50 ? '...' : ''}
                </TableCell>
                <TableCell>
                  <Badge variant={msg.is_from_client ? 'default' : 'secondary'}>
                    {msg.is_from_client ? 'Client Message' : 'Admin Reply'}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">{formatDate(msg.created_at)}</TableCell>
                {/* <TableCell> <Button variant="outline" size="sm">View</Button> </TableCell> */}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
};

export default AdminMessagesPage; 