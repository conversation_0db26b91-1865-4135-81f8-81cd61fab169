'use client'

import { useState } from 'react'
import { Bell, Search, Menu, X } from 'lucide-react'
import UserProfile from './UserProfile'
import { cn } from '../../lib/utils'
import { ThemeToggle } from '../../components/ui/theme-toggle'

interface HeaderProps {
  toggleSidebar?: () => void
  isSidebarOpen?: boolean
}

export default function Header({ toggleSidebar, isSidebarOpen }: HeaderProps) {
  const [notificationsOpen, setNotificationsOpen] = useState(false)

  return (
    <header className="bg-card border-b border-border h-16 flex items-center justify-between px-4 md:px-6 fixed top-0 right-0 left-0 md:left-64 z-10 transition-all duration-300">
      <div className="flex items-center gap-4">
        {/* Mobile sidebar toggle */}
        <button
          className="md:hidden p-1.5 rounded-md hover:bg-muted text-muted-foreground focus:outline-none"
          onClick={toggleSidebar}
          aria-label={isSidebarOpen ? 'Close sidebar' : 'Open sidebar'}
        >
          {isSidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
        </button>

      {/* Search */}
        <div className="relative w-full max-w-md">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-4 h-4 text-muted-foreground" />
        </div>
        <input
          type="search"
            className="block w-full py-2 pl-10 pr-3 bg-muted/40 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 focus:border-primary transition-all text-sm placeholder-muted-foreground"
          placeholder="Search..."
        />
        </div>
      </div>

      {/* Right side actions */}
      <div className="flex items-center gap-4">
        {/* Theme Toggle */}
        <ThemeToggle />

        {/* Notifications */}
        <div className="relative">
          <button
            className={cn(
              "p-1.5 rounded-full relative transition-colors",
              notificationsOpen ? "bg-secondary" : "hover:bg-secondary/60"
            )}
            onClick={() => setNotificationsOpen(!notificationsOpen)}
            aria-label="Notifications"
          >
            <Bell className="w-5 h-5 text-muted-foreground" />
            <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-destructive ring-2 ring-card animate-pulse-subtle" />
        </button>

          {notificationsOpen && (
            <div className="absolute right-0 mt-2 w-80 bg-card rounded-xl shadow-lg ring-1 ring-black ring-opacity-5 overflow-hidden animate-scale-in">
              <div className="p-4 border-b border-border">
                <h3 className="text-sm font-semibold">Notifications</h3>
              </div>
              <div className="max-h-96 overflow-y-auto">
                <div className="py-2 px-4 border-b border-border hover:bg-muted/50 transition-colors">
                  <p className="text-sm font-medium">New user registered</p>
                  <p className="text-xs text-muted-foreground mt-1">Someone just created a new account</p>
                  <p className="text-xs text-muted-foreground mt-2">2 hours ago</p>
                </div>
                <div className="py-2 px-4 border-b border-border hover:bg-muted/50 transition-colors">
                  <p className="text-sm font-medium">New order received</p>
                  <p className="text-xs text-muted-foreground mt-1">You received a new order from Client X</p>
                  <p className="text-xs text-muted-foreground mt-2">Yesterday</p>
                </div>
                <div className="py-2 px-4 hover:bg-muted/50 transition-colors">
                  <p className="text-sm font-medium">System update</p>
                  <p className="text-xs text-muted-foreground mt-1">The system has been updated to version 2.0</p>
                  <p className="text-xs text-muted-foreground mt-2">3 days ago</p>
                </div>
              </div>
              <div className="p-2 border-t border-border">
                <a href="/notifications" className="block text-xs text-center py-2 text-primary hover:underline">
                  View all notifications
                </a>
              </div>
            </div>
          )}
        </div>

        {/* User profile */}
        <UserProfile />
      </div>
    </header>
  )
}