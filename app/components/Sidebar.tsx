'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  Building,
  Briefcase,
  Users,
  Key,
  Receipt,
  Calendar,
  Settings,
  Globe,
  LayoutDashboard,
  ClipboardList,
  Layers,
  X,
  DollarSign,
  MessageSquare
} from 'lucide-react'
import { cn } from '../../lib/utils'

// Function to determine if link is active
const isLinkActive = (pathname: string, href: string) => {
  if (href === '/dashboard' && pathname === '/dashboard') {
    return true
  }
  return pathname.startsWith(href) && href !== '/dashboard'
}

interface SidebarProps {
  isMobile?: boolean
  isOpen?: boolean
  onClose?: () => void
}

export default function Sidebar({ isMobile = false, isOpen = true, onClose }: SidebarProps) {
  const pathname = usePathname()

  const navigationGroups = [
    {
      name: 'Core',
      links: [
        { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
        { name: 'Companies', href: '/companies', icon: Building },
        { name: 'Projects', href: '/projects', icon: Briefcase },
        { name: 'Contacts', href: '/contacts', icon: Users },
      ]
    },
    {
      name: 'Business',
      links: [
        { name: 'Credentials', href: '/credentials', icon: Key },
        { name: 'Expenses', href: '/expenses', icon: DollarSign },
        { name: 'Invoices', href: '/invoices', icon: Receipt },
        { name: 'Client Portals', href: '/client-portals', icon: Globe },
      ]
    },
    {
      name: 'Management',
      links: [
        { name: 'Calendar', href: '/calendar', icon: Calendar },
        { name: 'Tasks', href: '/tasks', icon: ClipboardList },
        { name: 'Resources', href: '/resources', icon: Layers },
        { name: 'Messages', href: '/admin/messages', icon: MessageSquare },
        { name: 'Settings', href: '/settings', icon: Settings },
      ]
    }
  ]

  // If sidebar is closed on mobile, don't render anything
  if (isMobile && !isOpen) return null;

  const sidebarClasses = cn(
    "bg-card border-r border-border h-screen overflow-y-auto transition-all duration-300 z-30",
    isMobile
      ? "fixed inset-0 w-72 shadow-lg animate-in slide-in-from-left"
      : "w-64 fixed left-0 top-0 md:block"
  );

  return (
    <>
      {/* Overlay for mobile */}
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-20 animate-in fade-in"
          onClick={onClose}
        />
      )}

      <aside className={sidebarClasses}>
        {/* Mobile close button */}
        {isMobile && (
          <button
            className="absolute top-4 right-4 p-1 rounded-md hover:bg-muted text-muted-foreground"
            onClick={onClose}
            aria-label="Close sidebar"
          >
            <X className="h-5 w-5" />
          </button>
        )}

        {/* Logo */}
        <div className="p-6">
          <Link href="/" className="flex items-center gap-2 group">
            <div className="bg-primary text-white p-2 rounded-lg shadow-sm flex items-center justify-center w-10 h-10 transition-all duration-300 group-hover:shadow-md">
              <span className="font-bold text-xl">D</span>
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-bold text-foreground transition-colors">
                DevCRM
              </span>
              <span className="text-xs text-muted-foreground">
                v2.0.4
              </span>
            </div>
          </Link>
        </div>

        {/* Navigation */}
        <nav className="px-3 py-2">
          {navigationGroups.map((group) => (
            <div key={group.name} className="mb-6">
              <div className="px-3 mb-2 text-xs uppercase text-muted-foreground font-medium tracking-wider">
                {group.name}
              </div>
              <ul className="space-y-1">
                {group.links.map((link) => {
                  const Icon = link.icon
                  const active = isLinkActive(pathname, link.href)

                  return (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className={cn(
                          "flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors group relative",
                          active
                            ? "bg-primary/10 text-primary"
                            : "text-muted-foreground hover:bg-muted hover:text-foreground"
                        )}
                      >
                        <Icon size={18} className={cn(
                          "transition-colors",
                          active ? "text-primary" : "text-muted-foreground group-hover:text-foreground"
                        )} />
                        <span>{link.name}</span>

                        {active && (
                          <div className="absolute right-2 w-1.5 h-1.5 rounded-full bg-primary"></div>
                        )}
                      </Link>
                    </li>
                  )
                })}
              </ul>
            </div>
          ))}
        </nav>

        {/* App version */}
        <div className="absolute bottom-0 left-0 right-0 p-4 text-center border-t border-border">
          <p className="text-xs text-muted-foreground">
            DevCRM v2.0.4
          </p>
        </div>
      </aside>
    </>
  )
}