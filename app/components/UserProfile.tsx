'use client'

import { useState, useEffect, useRef } from 'react'
import { useSupabase } from './SupabaseProvider'
import { LogOut, User, Settings, ChevronDown } from 'lucide-react'
import Link from 'next/link'
import { cn } from '../../lib/utils'

export default function UserProfile() {
  const { user, signOut, loading } = useSupabase()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const toggleDropdown = () => {
    setIsOpen(!isOpen)
  }

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  if (loading) {
    return (
      <div className="h-9 w-9 rounded-full bg-muted animate-pulse"></div>
    )
  }

  if (!user) {
    return (
      <Link
        href="/sign-in"
        className="flex items-center text-sm font-medium text-foreground/70 hover:text-primary transition-colors"
      >
        <User className="h-5 w-5 mr-1.5" />
        Sign in
      </Link>
    )
  }

  // Get first character of email for avatar and username
  const avatarText = user.email?.charAt(0).toUpperCase() || 'U'
  const username = user.email?.split('@')[0] || 'User'
  const displayName = username.charAt(0).toUpperCase() + username.slice(1)

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        className="flex items-center space-x-2 focus:outline-none user-menu-trigger group"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <div className="h-9 w-9 rounded-full bg-primary text-white flex items-center justify-center shadow-sm transition-all duration-200 group-hover:shadow-md ring-2 ring-background">
          {avatarText}
        </div>
        <div className="hidden md:flex flex-col items-start">
          <span className="text-sm font-medium text-foreground">{displayName}</span>
          <span className="text-xs text-muted-foreground">Admin</span>
        </div>
        <ChevronDown className={cn(
          "h-4 w-4 text-muted-foreground transition-transform duration-200 hidden md:block",
          isOpen && "transform rotate-180"
        )} />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-56 bg-card rounded-xl shadow-lg ring-1 ring-black ring-opacity-5 overflow-hidden z-10 animate-scale-in">
          <div className="px-4 py-3 border-b border-border">
            <div className="font-medium text-sm">Signed in as</div>
            <div className="text-xs text-muted-foreground mt-0.5 truncate">{user.email}</div>
          </div>

          <div className="py-1">
            <Link
              href="/profile"
              className="flex items-center px-4 py-2 text-sm text-foreground/80 hover:bg-secondary transition-colors"
              onClick={() => setIsOpen(false)}
            >
              <User className="h-4 w-4 mr-2 text-foreground/70" />
              Your Profile
            </Link>

            <Link
              href="/settings"
              className="flex items-center px-4 py-2 text-sm text-foreground/80 hover:bg-secondary transition-colors"
              onClick={() => setIsOpen(false)}
            >
              <Settings className="h-4 w-4 mr-2 text-foreground/70" />
              Settings
            </Link>
          </div>

          <div className="py-1 border-t border-border">
            <button
              onClick={handleSignOut}
              className="flex items-center w-full text-left px-4 py-2 text-sm text-foreground/80 hover:bg-secondary transition-colors"
            >
              <LogOut className="h-4 w-4 mr-2 text-foreground/70" />
              Sign out
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
