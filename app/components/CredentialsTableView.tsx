'use client'

import React, { useState } from 'react'
import { Eye, EyeOff, ExternalLink, Copy, Check } from 'lucide-react'
import { Credential } from '../../lib/supabase/types'
import { Button } from "@/components/ui/button"
import { cn } from '../../lib/utils'

interface CredentialsTableViewProps {
  credentials: Credential[]
}

export default function CredentialsTableView({ credentials }: CredentialsTableViewProps) {
  const [visiblePasswords, setVisiblePasswords] = useState<Record<string, boolean>>({})
  const [copiedStates, setCopiedStates] = useState<Record<string, boolean>>({})
  
  // Toggle password visibility for a specific credential
  const togglePasswordVisibility = (id: string) => {
    setVisiblePasswords(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }
  
  // Copy text to clipboard
  const copyToClipboard = (text: string, id: string) => {
    if (!text) return
    
    navigator.clipboard.writeText(text).then(() => {
      // Set copied state for this specific credential
      setCopiedStates(prev => ({ ...prev, [id]: true }))
      
      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopiedStates(prev => ({ ...prev, [id]: false }))
      }, 2000)
    })
  }
  
  if (!credentials || credentials.length === 0) {
    return (
      <div className="text-center py-6 bg-slate-50 rounded-lg border">
        <p className="text-muted-foreground">No credentials available.</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="bg-slate-100 dark:bg-slate-800">
            <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider border-b">Name</th>
            <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider border-b">Type</th>
            <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider border-b">Username</th>
            <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider border-b">Password</th>
            <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider border-b">URL</th>
            <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider border-b">Actions</th>
          </tr>
        </thead>
        <tbody>
          {credentials.map((cred) => (
            <tr key={cred.id} className="border-b hover:bg-slate-50 dark:hover:bg-slate-800/50">
              <td className="px-4 py-3 text-sm">{cred.name || 'N/A'}</td>
              <td className="px-4 py-3 text-sm">{cred.credential_type || 'N/A'}</td>
              <td className="px-4 py-3 text-sm">
                <div className="flex items-center space-x-2">
                  <span className="truncate max-w-[120px]">{cred.username || 'N/A'}</span>
                  {cred.username && (
                    <button 
                      onClick={() => copyToClipboard(cred.username || '', `username-${cred.id}`)}
                      className="text-muted-foreground hover:text-foreground"
                      title="Copy username"
                    >
                      {copiedStates[`username-${cred.id}`] ? (
                        <Check className="h-4 w-4 text-green-500" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </button>
                  )}
                </div>
              </td>
              <td className="px-4 py-3 text-sm">
                {cred.password_decrypted ? (
                  <div className="flex items-center space-x-2">
                    <span className={cn("font-mono max-w-[120px]", visiblePasswords[cred.id] ? "truncate" : "text-slate-400")}>
                      {visiblePasswords[cred.id] ? cred.password_decrypted : '••••••••'}
                    </span>
                    <button 
                      onClick={() => togglePasswordVisibility(cred.id)}
                      className="text-muted-foreground hover:text-foreground"
                      title={visiblePasswords[cred.id] ? "Hide password" : "Show password"}
                    >
                      {visiblePasswords[cred.id] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                    <button 
                      onClick={() => copyToClipboard(cred.password_decrypted || '', `password-${cred.id}`)}
                      className="text-muted-foreground hover:text-foreground"
                      title="Copy password"
                    >
                      {copiedStates[`password-${cred.id}`] ? (
                        <Check className="h-4 w-4 text-green-500" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                ) : (
                  <span className="text-muted-foreground italic">Not available</span>
                )}
              </td>
              <td className="px-4 py-3 text-sm">
                {cred.url ? (
                  <div className="flex items-center space-x-2">
                    <a 
                      href={cred.url.startsWith('http://') || cred.url.startsWith('https://') ? cred.url : `https://${cred.url}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline truncate max-w-[120px]"
                    >
                      {cred.url}
                    </a>
                    <ExternalLink className="h-4 w-4 text-muted-foreground" />
                  </div>
                ) : (
                  <span>N/A</span>
                )}
              </td>
              <td className="px-4 py-3 text-sm">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="h-7 px-2"
                  onClick={() => {
                    // Open in new tab if URL exists
                    if (cred.url) {
                      const url = cred.url.startsWith('http://') || cred.url.startsWith('https://') 
                        ? cred.url 
                        : `https://${cred.url}`;
                      window.open(url, '_blank');
                    }
                  }}
                  disabled={!cred.url}
                >
                  Visit
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
