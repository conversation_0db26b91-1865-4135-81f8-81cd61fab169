'use client'

import React from 'react'
import { Credential } from '../../lib/supabase/types'
import SimpleModal from './SimpleModal'

interface SimpleCredentialModalProps {
  isOpen: boolean
  onClose: () => void
  credential: Credential
}

export default function SimpleCredentialModal({
  isOpen,
  onClose,
  credential
}: SimpleCredentialModalProps) {
  console.log('SimpleCredentialModal rendering with:', { isOpen, credential })

  return (
    <SimpleModal
      isOpen={isOpen}
      onClose={onClose}
      title={credential?.name || 'Credential Details'}
    >
      <div className="space-y-3">
        <div>
          <p className="text-xs font-medium text-gray-500 mb-0.5">TYPE</p>
          <p className="text-sm">{credential?.credential_type || 'N/A'}</p>
        </div>

        {credential?.username && (
          <div>
            <p className="text-xs font-medium text-gray-500 mb-0.5">USERNAME</p>
            <p className="text-sm">{credential.username}</p>
          </div>
        )}

        {credential?.password_decrypted ? (
          <div>
            <p className="text-xs font-medium text-gray-500 mb-0.5">PASSWORD</p>
            <p className="text-sm font-mono bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded break-all">
              {credential.password_decrypted}
            </p>
          </div>
        ) : (
          <div>
            <p className="text-xs font-medium text-gray-500 mb-0.5">PASSWORD</p>
            <p className="text-sm text-gray-500 italic">Password not available</p>
          </div>
        )}

        {credential?.url && (
          <div>
            <p className="text-xs font-medium text-gray-500 mb-0.5">URL</p>
            <a
              href={credential.url.startsWith('http://') || credential.url.startsWith('https://')
                ? credential.url
                : `https://${credential.url}`
              }
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-blue-600 hover:underline block truncate"
            >
              {credential.url}
            </a>
          </div>
        )}

        {credential?.notes && (
          <div>
            <p className="text-xs font-medium text-gray-500 mb-0.5">NOTES</p>
            <p className="text-sm whitespace-pre-wrap bg-gray-50 dark:bg-gray-800 p-1.5 rounded border border-gray-200 dark:border-gray-700">
              {credential.notes}
            </p>
          </div>
        )}
      </div>
    </SimpleModal>
  )
}
