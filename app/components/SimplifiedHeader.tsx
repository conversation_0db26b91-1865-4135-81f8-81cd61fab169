'use client'

import { useState, useEffect, useRef } from 'react'
import { Bell, Search, Menu, X } from 'lucide-react'
import Link from 'next/link'
import { cn } from '../../lib/utils'
import UserProfile from './UserProfile'

interface SimplifiedHeaderProps {
  toggleSidebar?: () => void
  isSidebarOpen?: boolean
}

export default function SimplifiedHeader({ toggleSidebar, isSidebarOpen }: SimplifiedHeaderProps) {
  const [notificationsOpen, setNotificationsOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const notificationsRef = useRef<HTMLDivElement>(null)

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Handle notification dropdown click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setNotificationsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <header 
      className={cn(
        "bg-card border-b border-border h-16 flex items-center justify-between px-6 fixed top-0 right-0 left-0 md:left-64 z-20 transition-all duration-300",
        scrolled && "shadow-sm backdrop-blur-sm bg-card/95"
      )}
    >
      <div className="flex items-center gap-4">
        {/* Mobile sidebar toggle */}
        {toggleSidebar && (
          <button 
            className="inline-flex md:hidden p-1.5 rounded-md hover:bg-muted text-muted-foreground focus:outline-none"
            onClick={toggleSidebar}
            aria-label={isSidebarOpen ? 'Close sidebar' : 'Open sidebar'}
          >
            {isSidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </button>
        )}
        
        {/* Search */}
        <div className="relative w-full max-w-md">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-4 h-4 text-muted-foreground" />
          </div>
          <input
            type="search"
            className="block w-full py-2 pl-10 pr-3 bg-muted/40 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 focus:border-transparent transition-all text-sm placeholder-muted-foreground"
            placeholder="Search..."
          />
        </div>
      </div>

      {/* Right side actions */}
      <div className="flex items-center gap-4">
        {/* Notifications */}
        <div className="relative" ref={notificationsRef}>
          <button 
            className={cn(
              "p-1.5 rounded-full relative transition-colors",
              notificationsOpen ? "bg-secondary" : "hover:bg-secondary/60"
            )}
            onClick={() => setNotificationsOpen(!notificationsOpen)}
            aria-label="Notifications"
          >
            <Bell className="w-5 h-5 text-muted-foreground" />
            <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-destructive ring-2 ring-card animate-pulse-subtle" />
          </button>
          
          {notificationsOpen && (
            <div className="absolute right-0 mt-2 w-80 bg-card rounded-xl shadow-lg ring-1 ring-black ring-opacity-5 overflow-hidden animate-scale-in">
              <div className="p-4 border-b border-border">
                <h3 className="text-sm font-semibold">Notifications</h3>
              </div>
              <div className="max-h-96 overflow-y-auto">
                <div className="py-2 px-4 border-b border-border hover:bg-muted/50 transition-colors">
                  <div className="flex items-start gap-3">
                    <div className="bg-primary/10 p-2 rounded-full">
                      <Bell className="h-4 w-4 text-primary" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">New client registered</p>
                      <p className="text-xs text-muted-foreground mt-1">A new client account was created</p>
                      <p className="text-xs text-muted-foreground mt-2">2 hours ago</p>
                    </div>
                  </div>
                </div>
                <div className="py-2 px-4 hover:bg-muted/50 transition-colors">
                  <div className="flex items-start gap-3">
                    <div className="bg-success/10 p-2 rounded-full">
                      <Bell className="h-4 w-4 text-success" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">System update</p>
                      <p className="text-xs text-muted-foreground mt-1">The system has been updated successfully</p>
                      <p className="text-xs text-muted-foreground mt-2">Yesterday</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="p-2 border-t border-border">
                <Link href="/notifications" className="block text-xs text-center py-2 text-primary hover:underline">
                  View all notifications
                </Link>
              </div>
            </div>
          )}
        </div>

        {/* User profile */}
        <UserProfile />
      </div>
    </header>
  )
}
