'use client'

import { Grid, List } from 'lucide-react'
import { useState, useEffect } from 'react'

interface ViewToggleProps {
  onViewChange: (view: 'card' | 'table') => void
  initialView?: 'card' | 'table'
}

export default function ViewToggle({ onViewChange, initialView = 'card' }: ViewToggleProps) {
  const [view, setView] = useState<'card' | 'table'>(initialView)

  useEffect(() => {
    // Load saved preference from localStorage if available
    const savedView = localStorage.getItem('viewPreference') as 'card' | 'table' | null
    if (savedView) {
      setView(savedView)
      onViewChange(savedView)
    }
  }, [onViewChange])

  const handleViewChange = (newView: 'card' | 'table') => {
    setView(newView)
    // Save preference to localStorage
    localStorage.setItem('viewPreference', newView)
    onViewChange(newView)
  }

  return (
    <div className="flex items-center space-x-2 bg-white rounded-lg border border-gray-200 p-1">
      <button
        onClick={() => handleViewChange('card')}
        className={`p-1.5 rounded ${
          view === 'card' 
            ? 'bg-blue-100 text-blue-600' 
            : 'text-gray-500 hover:bg-gray-100'
        }`}
        title="Card View"
      >
        <Grid size={18} />
      </button>
      <button
        onClick={() => handleViewChange('table')}
        className={`p-1.5 rounded ${
          view === 'table' 
            ? 'bg-blue-100 text-blue-600' 
            : 'text-gray-500 hover:bg-gray-100'
        }`}
        title="Table View"
      >
        <List size={18} />
      </button>
    </div>
  )
}
