'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { createBrowserClient } from '@supabase/ssr'
import type { Session, User, SupabaseClient } from '@supabase/supabase-js'

// Create context
type SupabaseContextType = {
  supabase: SupabaseClient
  user: User | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ user: User | null; session: Session | null }>
  signUp: (email: string, password: string) => Promise<{ user: User | null; session: Session | null }>
  signOut: () => Promise<void>
}

const SupabaseContext = createContext<SupabaseContextType | undefined>(undefined)

// Provider component
export function SupabaseProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  // Create a Supabase client
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  // Check for session on mount
  useEffect(() => {
    let mounted = true;

    const getSession = async () => {
      try {
        console.log('Checking for existing session...');
        const { data: { session } } = await supabase.auth.getSession()

        if (mounted) {
          if (session?.user) {
            console.log('Session found for user:', session.user.email);
            setUser(session.user);
          } else {
            console.log('No active session found');
            setUser(null);
          }
          setLoading(false)
        }

        // Set up auth state listener
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          (event, session) => {
            console.log('Auth state changed:', event);
            if (mounted) {
              setUser(session?.user || null);
              router.refresh();
            }
          }
        )

        return () => {
          mounted = false;
          subscription.unsubscribe();
        }
      } catch (error) {
        console.error('Error checking session:', error);
        if (mounted) {
          setUser(null);
          setLoading(false);
        }
      }
    }

    getSession();

    return () => {
      mounted = false;
    }
  }, [router, supabase])

  // Auth methods
  const signIn = async (email: string, password: string) => {
    console.log('Attempting sign in for:', email);
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      console.error('Sign in error:', error.message);
      throw error;
    }

    // Update the user state immediately
    console.log('Sign in successful:', data.user?.email);
    console.log('Session established:', !!data.session);

    // Log session details to help with debugging
    if (data.session) {
      console.log('Session expires at:', new Date(data.session.expires_at! * 1000).toISOString());
      console.log('Access token present:', !!data.session.access_token);
      console.log('Refresh token present:', !!data.session.refresh_token);
    }

    setUser(data.user);

    return { user: data.user, session: data.session };
  }

  const signUp = async (email: string, password: string) => {
    console.log('Attempting sign up for:', email);
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    })

    if (error) {
      console.error('Sign up error:', error.message);
      throw error;
    }

    console.log('Sign up successful:', data.user?.email);
    return { user: data.user, session: data.session };
  }

  const signOut = async () => {
    console.log('Signing out user:', user?.email);
    const { error } = await supabase.auth.signOut()
    if (error) {
      console.error('Sign out error:', error.message);
      throw error;
    }

    // Clear user state
    setUser(null);

    // Use direct navigation for more reliable redirect
    window.location.href = '/sign-in';
  }

  const value = {
    supabase,
    user,
    loading,
    signIn,
    signUp,
    signOut,
  }

  return (
    <SupabaseContext.Provider value={value}>
      {children}
    </SupabaseContext.Provider>
  )
}

// Hook to use the Supabase context
export function useSupabase() {
  const context = useContext(SupabaseContext)
  if (context === undefined) {
    throw new Error('useSupabase must be used within a SupabaseProvider')
  }
  return context
}
