'use client'

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Eye, EyeOff, LogIn, AlertCircle, UserPlus, AlertTriangle } from 'lucide-react';
import { createBrowserClient } from '@supabase/ssr';
import { useRouter } from 'next/navigation';

export default function SignInPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSetupLink, setShowSetupLink] = useState(false);
  const [isCheckingSetup, setIsCheckingSetup] = useState(true);
  const [sessionChecked, setSessionChecked] = useState(false);
  const [showManualLink, setShowManualLink] = useState(false);
  const router = useRouter();

  // Create a Supabase client for this component
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  // Check for URL error parameters
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const errorParam = urlParams.get('error');
      if (errorParam) {
        setError(decodeURIComponent(errorParam));
      }
      
      // Check redirect count 
      const redirectCount = parseInt(urlParams.get('rc') || '0');
      if (redirectCount > 0) {
        console.log(`Detected previous ${redirectCount} redirects`);
        setShowManualLink(true);
      }
      
      // Clean URL
      if (urlParams.has('rc') || urlParams.has('error') || urlParams.has('cb')) {
        const url = new URL(window.location.href);
        url.search = '';
        window.history.replaceState({}, '', url.toString());
      }
    }
  }, []);

  // Check for existing session on mount
  useEffect(() => {
    const checkSession = async () => {
      try {
        console.log('Checking for existing session...');
        const { data: { session } } = await supabase.auth.getSession();

        if (session) {
          console.log('Existing session found for', session.user?.email);
          console.log('Session expires at:', new Date(session.expires_at! * 1000).toISOString());
          console.log('Redirecting to dashboard');
          
          // Don't immediately redirect, set a flag
          setTimeout(() => {
            router.push('/dashboard');
          }, 1000);
        } else {
          console.log('No active session found');
        }
        
        setSessionChecked(true);
      } catch (err) {
        console.error('Error checking session:', err);
        setSessionChecked(true);
      }
    };

    checkSession();
  }, [router, supabase]);

  // Check if this is a first-time setup (no users exist)
  useEffect(() => {
    async function checkFirstTimeSetup() {
      if (!sessionChecked) return;
      
      try {
        console.log('Checking if this is first-time setup...');
        // Check if any users exist in user_roles
        const { count, error } = await supabase
          .from('user_roles')
          .select('*', { count: 'exact', head: true });

        if (error) {
          console.error('Error checking user count:', error);
          setIsCheckingSetup(false);
          return;
        }

        // If no users exist in user_roles, show the setup link
        setShowSetupLink(count === 0);
        console.log('Setup check complete, count:', count);
      } catch (err) {
        console.error('Unexpected error checking setup status:', err);
      } finally {
        setIsCheckingSetup(false);
      }
    }

    checkFirstTimeSetup();
  }, [supabase, sessionChecked]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      console.log('Attempting sign in for:', email);
      
      // Sign in with Supabase Auth
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      // Log successful login
      console.log('[SECURITY] Successful login:', { email });
      console.log('[SECURITY] Session established:', !!data.session);

      if (data.session) {
        console.log('Session expires at:', new Date(data.session.expires_at! * 1000).toISOString());
        console.log('Access token present:', !!data.session.access_token);
        console.log('Refresh token present:', !!data.session.refresh_token);

        // Log all cookies to help with debugging
        if (typeof document !== 'undefined') {
          console.log('Cookies after login:', document.cookie);
        }
      }

      // Refresh the router to update the auth state
      router.refresh();

      // Add small delay before redirect to allow cookies to be set
      setTimeout(() => {
        router.push('/dashboard');
      }, 300);
    } catch (err) {
      // Log failed login attempt
      console.error('[SECURITY] Login error:', { email, error: err instanceof Error ? err.message : String(err) });

      // Show generic error message to prevent username enumeration
      setError('Invalid email or password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="w-full max-w-md">
        <div className="mb-8 text-center">
          <h1 className="text-2xl font-bold text-blue-900">DevCRM</h1>
          <p className="mt-2 text-gray-600">Sign in to your account</p>
        </div>

        <div className="overflow-hidden rounded-lg bg-white shadow p-8">
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative flex items-start">
              <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
              <span>{error}</span>
            </div>
          )}

          {showManualLink && (
            <div className="mb-6 bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded relative flex items-start">
              <AlertTriangle className="h-5 w-5 mr-2 mt-0.5" />
              <span>
                You may already be signed in. Try going directly to the{' '}
                <a href="/dashboard" className="underline font-medium">dashboard</a> or clear your cookies and try again.
              </span>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <div className="relative mt-1">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-500"
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5" />
                    ) : (
                      <Eye className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <button
                type="submit"
                disabled={isLoading}
                className="flex w-full justify-center items-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <span className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent mr-2"></span>
                ) : (
                  <LogIn className="h-4 w-4 mr-2" />
                )}
                Sign in
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="bg-white px-2 text-gray-500">Or</span>
              </div>
            </div>

            <div className="mt-6 space-y-3">
              {showSetupLink && !isCheckingSetup && (
                <Link
                  href="/setup"
                  className="flex w-full justify-center items-center rounded-md border border-blue-300 bg-blue-50 py-2 px-4 text-sm font-medium text-blue-600 shadow-sm hover:bg-blue-100"
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  First-Time Setup
                </Link>
              )}

              <Link
                href="/"
                className="flex w-full justify-center rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-500 shadow-sm hover:bg-gray-50"
              >
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
