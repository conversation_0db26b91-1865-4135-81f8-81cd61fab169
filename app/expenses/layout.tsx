'use client'

import { useState, useEffect } from 'react'
import Sidebar from '../components/Sidebar'
import Header from '../components/Header'

// A simple hook to detect mobile viewport (you might want a more robust solution)
const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    const listener = () => setMatches(media.matches);
    window.addEventListener('resize', listener);
    return () => window.removeEventListener('resize', listener);
  }, [matches, query]);

  return matches;
};

export default function ExpensesLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const isMobile = useMediaQuery('(max-width: 767px)'); // Tailwind's md breakpoint is 768px

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Close sidebar on route changes on mobile
  useEffect(() => {
    if (isMobile && isSidebarOpen) {
      setIsSidebarOpen(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [children]); // Assuming `children` changes on route navigation

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar
        isMobile={isMobile}
        isOpen={isMobile ? isSidebarOpen : true} // Sidebar always open on desktop
        onClose={() => setIsSidebarOpen(false)}
      />

      <div className={`flex-1 flex flex-col ${isMobile && isSidebarOpen ? '' : 'md:ml-64'}`}>
        <Header
          toggleSidebar={toggleSidebar}
          isSidebarOpen={isMobile ? isSidebarOpen : false} // Pass isSidebarOpen, false for desktop as sidebar is part of layout
        />

        <main className="p-6 mt-16 max-w-7xl mx-auto">
          {children}
        </main>
      </div>
    </div>
  )
} 