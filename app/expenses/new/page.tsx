'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Save, Receipt } from 'lucide-react'
import Link from 'next/link'
import { useSupabase } from '../../components/SupabaseProvider'

interface Company {
  id: string
  name: string
}

interface Project {
  id: string
  name: string
}

export default function NewExpense() {
  const router = useRouter()
  const { supabase } = useSupabase()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [companies, setCompanies] = useState<Company[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([])
  
  // Form state
  const [name, setName] = useState('')
  const [amount, setAmount] = useState('')
  const [date, setDate] = useState(new Date().toISOString().split('T')[0])
  const [category, setCategory] = useState('')
  const [paymentMethod, setPaymentMethod] = useState('')
  const [isReimbursable, setIsReimbursable] = useState(false)
  const [reimbursementStatus, setReimbursementStatus] = useState('pending')
  const [companyId, setCompanyId] = useState('')
  const [projectId, setProjectId] = useState('')
  const [notes, setNotes] = useState('')
  const [receiptFile, setReceiptFile] = useState<File | null>(null)
  const [receiptUrl, setReceiptUrl] = useState('')
  const [uploadProgress, setUploadProgress] = useState(0)
  
  // Fetch companies and projects
  useEffect(() => {
    async function fetchData() {
      try {
        // Fetch companies
        const { data: companiesData, error: companiesError } = await supabase
          .from('companies')
          .select('id, name')
          .order('name')
        
        if (companiesError) {
          console.error('Error fetching companies:', companiesError)
        } else if (companiesData) {
          setCompanies(companiesData)
        }
        
        // Fetch projects
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select('id, name, company_id')
          .order('name')
        
        if (projectsError) {
          console.error('Error fetching projects:', projectsError)
        } else if (projectsData) {
          setProjects(projectsData)
        }
      } catch (err) {
        console.error('Unexpected error:', err)
      }
    }
    
    fetchData()
  }, [supabase])
  
  // Filter projects when company changes
  useEffect(() => {
    if (companyId) {
      setFilteredProjects(projects.filter(project => project.company_id === companyId))
    } else {
      setFilteredProjects(projects)
    }
    
    // Reset project selection if company changes
    setProjectId('')
  }, [companyId, projects])
  
  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setReceiptFile(e.target.files[0])
    }
  }
  
  // Upload receipt to storage
  const uploadReceipt = async (): Promise<string | null> => {
    if (!receiptFile) return null
    
    try {
      const fileExt = receiptFile.name.split('.').pop()
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`
      const filePath = `receipts/${fileName}`
      
      const { error: uploadError, data } = await supabase.storage
        .from('expenses')
        .upload(filePath, receiptFile, {
          cacheControl: '3600',
          upsert: false,
          onUploadProgress: (progress) => {
            setUploadProgress(Math.round((progress.loaded / progress.total) * 100))
          }
        })
      
      if (uploadError) {
        console.error('Error uploading receipt:', uploadError)
        return null
      }
      
      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('expenses')
        .getPublicUrl(filePath)
      
      return publicUrl
    } catch (err) {
      console.error('Unexpected error uploading receipt:', err)
      return null
    }
  }
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    
    try {
      // Validate form
      if (!name || !amount || !date) {
        setError('Please fill in all required fields')
        setIsLoading(false)
        return
      }
      
      // Upload receipt if provided
      let uploadedReceiptUrl = null
      if (receiptFile) {
        uploadedReceiptUrl = await uploadReceipt()
      }
      
      // Create expense record
      const { data, error: insertError } = await supabase
        .from('expenses')
        .insert({
          name,
          amount: parseFloat(amount),
          date,
          category,
          payment_method: paymentMethod,
          is_reimbursable: isReimbursable,
          reimbursement_status: isReimbursable ? reimbursementStatus : null,
          company_id: companyId || null,
          project_id: projectId || null,
          notes,
          receipt_url: uploadedReceiptUrl,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
      
      if (insertError) {
        console.error('Error creating expense:', insertError)
        setError('Failed to create expense. Please try again.')
        setIsLoading(false)
        return
      }
      
      // Redirect to expenses list
      router.push('/expenses')
    } catch (err) {
      console.error('Unexpected error:', err)
      setError('An unexpected error occurred. Please try again.')
      setIsLoading(false)
    }
  }
  
  // Predefined categories
  const expenseCategories = [
    'Office Supplies',
    'Travel',
    'Meals',
    'Software',
    'Hardware',
    'Marketing',
    'Utilities',
    'Rent',
    'Consulting',
    'Legal',
    'Insurance',
    'Taxes',
    'Other'
  ]
  
  // Payment methods
  const paymentMethods = [
    'Credit Card',
    'Debit Card',
    'Cash',
    'Bank Transfer',
    'PayPal',
    'Check',
    'Other'
  ]
  
  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/expenses" className="mr-4 p-2 rounded-full hover:bg-gray-100">
            <ArrowLeft className="h-5 w-5 text-gray-500" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">New Expense</h1>
            <p className="text-gray-600">Add a new expense record</p>
          </div>
        </div>
      </div>
      
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}
      
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Expense Name */}
            <div className="col-span-2">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Expense Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., Office Supplies, Client Lunch, etc."
                required
              />
            </div>
            
            {/* Amount */}
            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                Amount <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500">$</span>
                </div>
                <input
                  type="number"
                  id="amount"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full pl-7 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  required
                />
              </div>
            </div>
            
            {/* Date */}
            <div>
              <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
                Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            {/* Category */}
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                id="category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a category</option>
                {expenseCategories.map((cat) => (
                  <option key={cat} value={cat}>
                    {cat}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Payment Method */}
            <div>
              <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 mb-1">
                Payment Method
              </label>
              <select
                id="paymentMethod"
                value={paymentMethod}
                onChange={(e) => setPaymentMethod(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select payment method</option>
                {paymentMethods.map((method) => (
                  <option key={method} value={method}>
                    {method}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Reimbursable */}
            <div>
              <div className="flex items-center h-full">
                <input
                  type="checkbox"
                  id="isReimbursable"
                  checked={isReimbursable}
                  onChange={(e) => setIsReimbursable(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="isReimbursable" className="ml-2 block text-sm text-gray-700">
                  Reimbursable Expense
                </label>
              </div>
            </div>
            
            {/* Reimbursement Status (only shown if reimbursable) */}
            {isReimbursable && (
              <div>
                <label htmlFor="reimbursementStatus" className="block text-sm font-medium text-gray-700 mb-1">
                  Reimbursement Status
                </label>
                <select
                  id="reimbursementStatus"
                  value={reimbursementStatus}
                  onChange={(e) => setReimbursementStatus(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="reimbursed">Reimbursed</option>
                  <option value="denied">Denied</option>
                </select>
              </div>
            )}
            
            {/* Company */}
            <div>
              <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
                Company
              </label>
              <select
                id="company"
                value={companyId}
                onChange={(e) => setCompanyId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a company</option>
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Project (filtered by company) */}
            <div>
              <label htmlFor="project" className="block text-sm font-medium text-gray-700 mb-1">
                Project
              </label>
              <select
                id="project"
                value={projectId}
                onChange={(e) => setProjectId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={!companyId}
              >
                <option value="">Select a project</option>
                {filteredProjects.map((project) => (
                  <option key={project.id} value={project.id}>
                    {project.name}
                  </option>
                ))}
              </select>
              {!companyId && (
                <p className="mt-1 text-xs text-gray-500">Select a company first</p>
              )}
            </div>
            
            {/* Notes */}
            <div className="col-span-2">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Add any additional details about this expense..."
              ></textarea>
            </div>
            
            {/* Receipt Upload */}
            <div className="col-span-2">
              <label htmlFor="receipt" className="block text-sm font-medium text-gray-700 mb-1">
                Receipt
              </label>
              <div className="mt-1 flex items-center">
                <label className="block w-full">
                  <div className="flex items-center justify-center px-6 py-4 border-2 border-gray-300 border-dashed rounded-md hover:bg-gray-50 cursor-pointer">
                    <Receipt className="h-6 w-6 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-500">
                      {receiptFile ? receiptFile.name : 'Upload a receipt (optional)'}
                    </span>
                  </div>
                  <input
                    id="receipt"
                    type="file"
                    className="sr-only"
                    accept="image/*,.pdf"
                    onChange={handleFileChange}
                  />
                </label>
              </div>
              {uploadProgress > 0 && uploadProgress < 100 && (
                <div className="mt-2">
                  <div className="bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-blue-600 h-2.5 rounded-full"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Uploading: {uploadProgress}%</p>
                </div>
              )}
            </div>
          </div>
          
          <div className="mt-8 flex justify-end">
            <Link
              href="/expenses"
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 mr-2"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-sm font-medium text-white flex items-center"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-1" />
                  Save Expense
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
