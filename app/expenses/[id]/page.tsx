'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { ArrowLeft, Edit, Trash2, Receipt, ExternalLink, Save } from 'lucide-react'
import Link from 'next/link'
import { useSupabase } from '../../components/SupabaseProvider'
import { formatCurrency, formatDate } from '../../../lib/utils'

interface Expense {
  id: string
  name: string
  amount: number
  category: string | null
  date: string | null
  payment_method: string | null
  is_reimbursable: boolean | null
  reimbursement_status: string | null
  company_id: string | null
  project_id: string | null
  notes: string | null
  receipt_url: string | null
  created_at: string
  updated_at: string
  companies?: { name: string } | null
  projects?: { name: string } | null
}

interface Company {
  id: string
  name: string
}

interface Project {
  id: string
  name: string
  company_id: string
}

export default function ExpenseDetail({ params }: { params: { id: string } }) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { supabase } = useSupabase()
  const [expense, setExpense] = useState<Expense | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(searchParams.get('edit') === 'true')
  const [error, setError] = useState<string | null>(null)
  const [companies, setCompanies] = useState<Company[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([])
  const [isDeleting, setIsDeleting] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  // Form state
  const [name, setName] = useState('')
  const [amount, setAmount] = useState('')
  const [date, setDate] = useState('')
  const [category, setCategory] = useState('')
  const [paymentMethod, setPaymentMethod] = useState('')
  const [isReimbursable, setIsReimbursable] = useState(false)
  const [reimbursementStatus, setReimbursementStatus] = useState('pending')
  const [companyId, setCompanyId] = useState('')
  const [projectId, setProjectId] = useState('')
  const [notes, setNotes] = useState('')

  // Fetch expense data
  useEffect(() => {
    async function fetchExpense() {
      setIsLoading(true)

      try {
        // Fetch expense with company and project information
        const { data, error } = await supabase
          .from('expenses')
          .select(`
            *,
            companies:company_id(id, name),
            projects:project_id(id, name)
          `)
          .eq('id', params.id)
          .single()

        if (error) {
          console.error('Error fetching expense:', error)
          setError('Failed to load expense details')
        } else if (data) {
          setExpense(data)

          // Initialize form state
          setName(data.name)
          setAmount(data.amount.toString())
          setDate(data.date || '')
          setCategory(data.category || '')
          setPaymentMethod(data.payment_method || '')
          setIsReimbursable(data.is_reimbursable || false)
          setReimbursementStatus(data.reimbursement_status || 'pending')
          setCompanyId(data.company_id || '')
          setProjectId(data.project_id || '')
          setNotes(data.notes || '')
        }
      } catch (err) {
        console.error('Unexpected error:', err)
        setError('An unexpected error occurred')
      }

      setIsLoading(false)
    }

    // Fetch companies and projects
    async function fetchRelatedData() {
      try {
        // Fetch companies
        const { data: companiesData, error: companiesError } = await supabase
          .from('companies')
          .select('id, name')
          .order('name')

        if (companiesError) {
          console.error('Error fetching companies:', companiesError)
        } else if (companiesData) {
          setCompanies(companiesData)
        }

        // Fetch projects
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select('id, name, company_id')
          .order('name')

        if (projectsError) {
          console.error('Error fetching projects:', projectsError)
        } else if (projectsData) {
          setProjects(projectsData)
        }
      } catch (err) {
        console.error('Unexpected error:', err)
      }
    }

    fetchExpense()
    fetchRelatedData()
  }, [supabase, params.id])

  // Filter projects when company changes
  useEffect(() => {
    if (companyId) {
      setFilteredProjects(projects.filter(project => project.company_id === companyId))
    } else {
      setFilteredProjects(projects)
    }

    // Reset project selection if company changes and not in initial load
    if (isEditing && expense?.company_id !== companyId) {
      setProjectId('')
    }
  }, [companyId, projects, isEditing, expense])

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    setError(null)

    try {
      // Validate form
      if (!name || !amount || !date) {
        setError('Please fill in all required fields')
        setIsSaving(false)
        return
      }

      // Update expense record
      const { error: updateError } = await supabase
        .from('expenses')
        .update({
          name,
          amount: parseFloat(amount),
          date,
          category,
          payment_method: paymentMethod,
          is_reimbursable: isReimbursable,
          reimbursement_status: isReimbursable ? reimbursementStatus : null,
          company_id: companyId || null,
          project_id: projectId || null,
          notes,
          updated_at: new Date().toISOString()
        })
        .eq('id', params.id)

      if (updateError) {
        console.error('Error updating expense:', updateError)
        setError('Failed to update expense. Please try again.')
        setIsSaving(false)
        return
      }

      // Fetch updated expense
      const { data: updatedExpense, error: fetchError } = await supabase
        .from('expenses')
        .select(`
          *,
          companies:company_id(id, name),
          projects:project_id(id, name)
        `)
        .eq('id', params.id)
        .single()

      if (fetchError) {
        console.error('Error fetching updated expense:', fetchError)
      } else if (updatedExpense) {
        setExpense(updatedExpense)
      }

      // If we came from the expenses list with edit=true, go back to the list
      if (searchParams.get('edit') === 'true') {
        router.push('/expenses')
      } else {
        setIsEditing(false)
      }
      setIsSaving(false)
    } catch (err) {
      console.error('Unexpected error:', err)
      setError('An unexpected error occurred. Please try again.')
      setIsSaving(false)
    }
  }

  // Handle expense deletion
  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this expense? This action cannot be undone.')) {
      return
    }

    setIsDeleting(true)

    try {
      const { error } = await supabase
        .from('expenses')
        .delete()
        .eq('id', params.id)

      if (error) {
        console.error('Error deleting expense:', error)
        setError('Failed to delete expense. Please try again.')
        setIsDeleting(false)
        return
      }

      // Redirect to expenses list
      router.push('/expenses')
    } catch (err) {
      console.error('Unexpected error:', err)
      setError('An unexpected error occurred. Please try again.')
      setIsDeleting(false)
    }
  }

  // Predefined categories
  const expenseCategories = [
    'Office Supplies',
    'Travel',
    'Meals',
    'Software',
    'Hardware',
    'Marketing',
    'Utilities',
    'Rent',
    'Consulting',
    'Legal',
    'Insurance',
    'Taxes',
    'Other'
  ]

  // Payment methods
  const paymentMethods = [
    'Credit Card',
    'Debit Card',
    'Cash',
    'Bank Transfer',
    'PayPal',
    'Check',
    'Other'
  ]

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (error && !expense) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
        {error}
      </div>
    )
  }

  if (!expense) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md">
        Expense not found
      </div>
    )
  }

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/expenses" className="mr-4 p-2 rounded-full hover:bg-gray-100">
            <ArrowLeft className="h-5 w-5 text-gray-500" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {isEditing ? 'Edit Expense' : expense.name}
            </h1>
            <p className="text-gray-600">
              {isEditing ? 'Update expense details' : formatDate(expense.date || '')}
            </p>
          </div>
        </div>

        <div className="flex space-x-2">
          {!isEditing ? (
            <>
              <button
                onClick={() => setIsEditing(true)}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-sm font-medium text-white flex items-center"
              >
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </button>
              <button
                onClick={handleDelete}
                disabled={isDeleting}
                className="px-3 py-2 bg-red-600 hover:bg-red-700 rounded-md text-sm font-medium text-white flex items-center"
              >
                {isDeleting ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                ) : (
                  <Trash2 className="h-4 w-4 mr-1" />
                )}
                Delete
              </button>
            </>
          ) : (
            <button
              onClick={() => {
                if (searchParams.get('edit') === 'true') {
                  router.push('/expenses')
                } else {
                  setIsEditing(false)
                }
              }}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
          )}
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        {isEditing ? (
          <form onSubmit={handleSubmit} className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Expense Name */}
              <div className="col-span-2">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Expense Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., Office Supplies, Client Lunch, etc."
                  required
                />
              </div>

              {/* Amount */}
              <div>
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                  Amount <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500">$</span>
                  </div>
                  <input
                    type="number"
                    id="amount"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    className="w-full pl-7 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                    required
                  />
                </div>
              </div>

              {/* Date */}
              <div>
                <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
                  Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  id="date"
                  value={date}
                  onChange={(e) => setDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              {/* Category */}
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  id="category"
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a category</option>
                  {expenseCategories.map((cat) => (
                    <option key={cat} value={cat}>
                      {cat}
                    </option>
                  ))}
                </select>
              </div>

              {/* Payment Method */}
              <div>
                <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Method
                </label>
                <select
                  id="paymentMethod"
                  value={paymentMethod}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select payment method</option>
                  {paymentMethods.map((method) => (
                    <option key={method} value={method}>
                      {method}
                    </option>
                  ))}
                </select>
              </div>

              {/* Reimbursable */}
              <div>
                <div className="flex items-center h-full">
                  <input
                    type="checkbox"
                    id="isReimbursable"
                    checked={isReimbursable}
                    onChange={(e) => setIsReimbursable(e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isReimbursable" className="ml-2 block text-sm text-gray-700">
                    Reimbursable Expense
                  </label>
                </div>
              </div>

              {/* Reimbursement Status (only shown if reimbursable) */}
              {isReimbursable && (
                <div>
                  <label htmlFor="reimbursementStatus" className="block text-sm font-medium text-gray-700 mb-1">
                    Reimbursement Status
                  </label>
                  <select
                    id="reimbursementStatus"
                    value={reimbursementStatus}
                    onChange={(e) => setReimbursementStatus(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="pending">Pending</option>
                    <option value="approved">Approved</option>
                    <option value="reimbursed">Reimbursed</option>
                    <option value="denied">Denied</option>
                  </select>
                </div>
              )}

              {/* Company */}
              <div>
                <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
                  Company
                </label>
                <select
                  id="company"
                  value={companyId}
                  onChange={(e) => setCompanyId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a company</option>
                  {companies.map((company) => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Project (filtered by company) */}
              <div>
                <label htmlFor="project" className="block text-sm font-medium text-gray-700 mb-1">
                  Project
                </label>
                <select
                  id="project"
                  value={projectId}
                  onChange={(e) => setProjectId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!companyId}
                >
                  <option value="">Select a project</option>
                  {filteredProjects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
                {!companyId && (
                  <p className="mt-1 text-xs text-gray-500">Select a company first</p>
                )}
              </div>

              {/* Notes */}
              <div className="col-span-2">
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add any additional details about this expense..."
                ></textarea>
              </div>
            </div>

            <div className="mt-8 flex justify-end">
              <button
                type="submit"
                disabled={isSaving}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-sm font-medium text-white flex items-center"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-1" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </form>
        ) : (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="col-span-2 md:col-span-1">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Expense Details</h2>

                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Amount</p>
                    <p className="text-lg font-semibold text-gray-900">{formatCurrency(expense.amount)}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Date</p>
                    <p className="text-gray-900">{expense.date ? formatDate(expense.date) : '-'}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Category</p>
                    <p className="text-gray-900">{expense.category || '-'}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Payment Method</p>
                    <p className="text-gray-900">{expense.payment_method || '-'}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Reimbursable</p>
                    <p className="text-gray-900">{expense.is_reimbursable ? 'Yes' : 'No'}</p>
                  </div>

                  {expense.is_reimbursable && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Reimbursement Status</p>
                      <div className="mt-1">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          expense.reimbursement_status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          expense.reimbursement_status === 'approved' ? 'bg-blue-100 text-blue-800' :
                          expense.reimbursement_status === 'reimbursed' ? 'bg-green-100 text-green-800' :
                          expense.reimbursement_status === 'denied' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {expense.reimbursement_status ?
                            expense.reimbursement_status.charAt(0).toUpperCase() + expense.reimbursement_status.slice(1) :
                            'Unknown'}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="col-span-2 md:col-span-1">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h2>

                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Company</p>
                    <p className="text-gray-900">{expense.companies?.name || '-'}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Project</p>
                    <p className="text-gray-900">{expense.projects?.name || '-'}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Notes</p>
                    <p className="text-gray-900 whitespace-pre-wrap">{expense.notes || '-'}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Receipt</p>
                    {expense.receipt_url ? (
                      <a
                        href={expense.receipt_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 flex items-center mt-1"
                      >
                        <Receipt className="h-4 w-4 mr-1" />
                        View Receipt
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </a>
                    ) : (
                      <p className="text-gray-500">No receipt attached</p>
                    )}
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Created</p>
                    <p className="text-gray-900">{formatDate(expense.created_at)}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Last Updated</p>
                    <p className="text-gray-900">{formatDate(expense.updated_at)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
