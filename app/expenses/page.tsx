'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Plus, Search, Filter, DollarSign, FileDown, Eye, Receipt, Edit } from 'lucide-react'
import { useSupabase } from '../components/SupabaseProvider'
import { formatCurrency, formatDate } from '../../lib/utils'
import { downloadCSV, formatDateForExport } from '../../lib/exportUtils'

interface Expense {
  id: string
  name: string
  amount: number
  category: string | null
  date: string | null
  payment_method: string | null
  is_reimbursable: boolean | null
  reimbursement_status: string | null
  company_id: string | null
  project_id: string | null
  receipt_url: string | null
  companies?: { name: string } | null
  projects?: { name: string } | null
}

export default function Expenses() {
  const { supabase } = useSupabase()
  const [expenses, setExpenses] = useState<Expense[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null)

  useEffect(() => {
    async function fetchExpenses() {
      setIsLoading(true)

      try {
        console.log('Fetching expenses...')

        // First check if the user is authenticated
        const { data: { session } } = await supabase.auth.getSession()
        console.log('Current session:', session ? 'Authenticated' : 'Not authenticated')

        if (!session) {
          console.error('No active session found')
          setIsLoading(false)
          return
        }

        // Fetch expenses with company and project information
        const { data, error } = await supabase
          .from('expenses')
          .select(`
            id,
            name,
            amount,
            category,
            date,
            payment_method,
            is_reimbursable,
            reimbursement_status,
            company_id,
            project_id,
            receipt_url
          `)
          .order('date', { ascending: false })

        if (error) {
          console.error('Error fetching expenses:', error)
          setExpenses([])
          setIsLoading(false)
          return
        }

        console.log('Expenses fetched successfully:', data?.length || 0)

        // If we have expenses, fetch the company and project names
        interface Company {
          id: string;
          name: string;
        }

        interface Project {
          id: string;
          name: string;
        }

        let companiesData: Company[] | null = null
        let projectsData: Project[] | null = null

        if (data && data.length > 0) {
          // Get unique company IDs
          const companyIds = [...new Set(data.filter(item => item.company_id).map(item => item.company_id))]

          if (companyIds.length > 0) {
            const { data: companies, error: companyError } = await supabase
              .from('companies')
              .select('id, name')
              .in('id', companyIds)

            if (companyError) {
              console.error('Error fetching companies:', companyError)
            } else {
              console.log('Companies fetched:', companies.length)
              companiesData = companies
            }
          }

          // Get unique project IDs
          const projectIds = [...new Set(data.filter(item => item.project_id).map(item => item.project_id))]

          if (projectIds.length > 0) {
            const { data: projects, error: projectError } = await supabase
              .from('projects')
              .select('id, name')
              .in('id', projectIds)

            if (projectError) {
              console.error('Error fetching projects:', projectError)
            } else {
              console.log('Projects fetched:', projects.length)
              projectsData = projects
            }
          }

          // Map the data to include company and project names
          const formattedData = data.map(item => {
            // Find company name if available
            const company = companiesData?.find(c => c.id === item.company_id)
            // Find project name if available
            const project = projectsData?.find(p => p.id === item.project_id)

            return {
              ...item,
              companies: company ? { name: company.name } : null,
              projects: project ? { name: project.name } : null
            }
          })

          setExpenses(formattedData)
        } else {
          setExpenses([])
        }
      } catch (err) {
        console.error('Unexpected error:', err)
        setExpenses([])
      }

      setIsLoading(false)
    }

    fetchExpenses()
  }, [supabase])

  // Filter expenses based on search query and category filter
  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch =
      expense.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (expense.category?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false) ||
      expense.companies?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      expense.projects?.name?.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCategory = !categoryFilter || expense.category === categoryFilter

    return matchesSearch && matchesCategory
  })

  // Get unique categories for the filter
  const categories = Array.from(new Set(expenses.map(expense => expense.category).filter(Boolean) as string[]))

  // Calculate total amount for selected expenses
  const totalAmount = filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0)

  // Handle CSV export
  const handleExportCSV = () => {
    // Define custom headers for the CSV
    const headers = [
      { key: 'name' as keyof Expense, label: 'Expense Name' },
      { key: 'amount' as keyof Expense, label: 'Amount' },
      { key: 'date' as keyof Expense, label: 'Date' },
      { key: 'category' as keyof Expense, label: 'Category' },
      { key: 'payment_method' as keyof Expense, label: 'Payment Method' },
      { key: 'is_reimbursable' as keyof Expense, label: 'Reimbursable' },
      { key: 'reimbursement_status' as keyof Expense, label: 'Reimbursement Status' },
      { key: 'notes' as keyof Expense, label: 'Notes' }
    ]

    // Format data for export
    const formattedData = filteredExpenses.map(expense => ({
      ...expense,
      date: formatDateForExport(expense.date),
      amount: expense.amount.toFixed(2),
      is_reimbursable: expense.is_reimbursable ? 'Yes' : 'No'
    }))

    // Generate filename with current date
    const date = new Date().toISOString().split('T')[0]
    const filename = `expenses_export_${date}.csv`

    // Download the CSV
    downloadCSV(formattedData, filename, headers)
  }

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Expenses</h1>
          <p className="text-gray-600">Track and manage project and client expenses</p>
        </div>

        <Link
          href="/expenses/new"
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <Plus size={18} className="mr-1" />
          New Expense
        </Link>
      </div>

      {/* Search and filters */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative w-full sm:w-96">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-5 h-5 text-gray-400" />
          </div>
          <input
            type="search"
            className="block w-full py-2 pl-10 pr-3 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search expenses..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="relative w-full sm:w-64">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Filter className="w-5 h-5 text-gray-400" />
          </div>
          <select
            className="block w-full py-2 pl-10 pr-3 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={categoryFilter || ''}
            onChange={(e) => setCategoryFilter(e.target.value || null)}
          >
            <option value="">All Categories</option>
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Summary stats */}
      {filteredExpenses.length > 0 && (
        <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
          <div className="flex flex-wrap items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Amount ({filteredExpenses.length} expenses)</p>
              <p className="text-xl font-bold text-gray-900">{formatCurrency(totalAmount)}</p>
            </div>

            <button
              onClick={() => handleExportCSV()}
              className="flex items-center text-blue-600 hover:text-blue-800"
            >
              <FileDown className="h-4 w-4 mr-1" />
              <span className="text-sm font-medium">Export</span>
            </button>
          </div>
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow w-full">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Expense
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Client/Project
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Payment Method
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredExpenses.length > 0 ? (
                  filteredExpenses.map((expense) => (
                    <tr key={expense.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                            <DollarSign className="h-5 w-5 text-green-600" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{expense.name}</div>
                            <div className="text-sm text-gray-500">
                              {expense.is_reimbursable ? 'Reimbursable' : 'Non-reimbursable'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{expense.companies?.name || '-'}</div>
                        <div className="text-sm text-gray-500">{expense.projects?.name || '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{expense.date ? formatDate(expense.date) : '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{expense.category || '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{formatCurrency(expense.amount)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{expense.payment_method || '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex space-x-3">
                          <Link href={`/expenses/${expense.id}`} className="text-indigo-600 hover:text-indigo-900" title="View">
                            <Eye size={16} />
                          </Link>
                          <Link href={`/expenses/${expense.id}?edit=true`} className="text-green-600 hover:text-green-900" title="Edit">
                            <Edit size={16} />
                          </Link>
                          {expense.receipt_url ? (
                            <a
                              href={expense.receipt_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-900"
                              title="View Receipt"
                            >
                              <Receipt size={16} />
                            </a>
                          ) : (
                            <span className="text-gray-400 cursor-not-allowed" title="No Receipt Available">
                              <Receipt size={16} />
                            </span>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                      {searchQuery || categoryFilter ? 'No expenses match your search' : 'No expenses found'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}