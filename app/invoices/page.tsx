'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Plus, Search, Filter, Receipt, FileDown, Eye } from 'lucide-react'
import supabase from '../../lib/supabase/client'
import { formatCurrency, formatDate, getStatusBadgeClass } from '@/lib/utils'

interface Invoice {
  id: string
  invoice_number: string
  amount: number
  status: string | null
  due_date: string | null
  issue_date: string | null
  company_id: string | null
  project_id: string | null
  companies?: { name: string } | null
  projects?: { name: string } | null
}

export default function Invoices() {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string | null>(null)
  
  useEffect(() => {
    async function fetchInvoices() {
      setIsLoading(true)
      
      try {
        const { data, error } = await supabase
          .from('invoices')
          .select(`
            id, 
            invoice_number, 
            amount,
            status,
            due_date,
            issue_date,
            company_id,
            project_id,
            companies (name),
            projects (name)
          `)
          .order('due_date', { ascending: false })
        
        if (error) {
          console.error('Error fetching invoices:', error)
          setInvoices([])
        } else if (data) {
          setInvoices(data)
        }
      } catch (err) {
        console.error('Unexpected error:', err)
        setInvoices([])
      }
      
      setIsLoading(false)
    }
    
    fetchInvoices()
  }, [])
  
  // Filter invoices based on search query and status filter
  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = 
      invoice.invoice_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invoice.companies?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invoice.projects?.name?.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = !statusFilter || invoice.status === statusFilter
    
    return matchesSearch && matchesStatus
  })
  
  // Calculate if invoice is overdue
  const isOverdue = (dueDate: string | null, status: string | null) => {
    if (!dueDate || status === 'paid') return false
    
    const today = new Date()
    const due = new Date(dueDate)
    
    return due < today
  }
  
  // Available status filters
  const statusOptions = [
    { value: null, label: 'All Statuses' },
    { value: 'draft', label: 'Draft' },
    { value: 'sent', label: 'Sent' },
    { value: 'paid', label: 'Paid' },
    { value: 'overdue', label: 'Overdue' }
  ]
  
  // Calculate total amount for selected invoices
  const totalAmount = filteredInvoices.reduce((sum, invoice) => sum + invoice.amount, 0)
  
  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Invoices</h1>
          <p className="text-gray-600">Manage your client invoices and payments</p>
        </div>
        
        <Link
          href="/invoices/new"
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <Plus size={18} className="mr-1" />
          New Invoice
        </Link>
      </div>
      
      {/* Search and filters */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative w-full sm:w-96">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-5 h-5 text-gray-400" />
          </div>
          <input 
            type="search" 
            className="block w-full py-2 pl-10 pr-3 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search invoices..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="relative w-full sm:w-64">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Filter className="w-5 h-5 text-gray-400" />
          </div>
          <select
            className="block w-full py-2 pl-10 pr-3 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={statusFilter || ''}
            onChange={(e) => setStatusFilter(e.target.value || null)}
          >
            {statusOptions.map((option) => (
              <option key={option.label} value={option.value || ''}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>
      
      {/* Summary stats */}
      {filteredInvoices.length > 0 && (
        <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
          <div className="flex flex-wrap items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Amount ({filteredInvoices.length} invoices)</p>
              <p className="text-xl font-bold text-gray-900">{formatCurrency(totalAmount)}</p>
            </div>
            
            <button className="flex items-center text-blue-600 hover:text-blue-800">
              <FileDown className="h-4 w-4 mr-1" />
              <span className="text-sm font-medium">Export</span>
            </button>
          </div>
        </div>
      )}
      
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Invoice
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Client
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Issue Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Due Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredInvoices.length > 0 ? (
                  filteredInvoices.map((invoice) => (
                    <tr key={invoice.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                            <Receipt className="h-5 w-5 text-indigo-600" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">#{invoice.invoice_number}</div>
                            <div className="text-sm text-gray-500">{invoice.projects?.name || 'No project'}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{invoice.companies?.name || '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{invoice.issue_date ? formatDate(invoice.issue_date) : '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm ${isOverdue(invoice.due_date, invoice.status) ? 'text-red-600 font-medium' : 'text-gray-900'}`}>
                          {invoice.due_date ? formatDate(invoice.due_date) : '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{formatCurrency(invoice.amount)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(invoice.status)}`}>
                          {invoice.status || 'Draft'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex space-x-3">
                          <Link href={`/invoices/${invoice.id}`} className="text-indigo-600 hover:text-indigo-900">
                            <Eye size={16} />
                          </Link>
                          <button className="text-blue-600 hover:text-blue-900">
                            <FileDown size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                      {searchQuery || statusFilter ? 'No invoices match your search' : 'No invoices found'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
} 