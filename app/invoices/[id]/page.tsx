'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  ArrowLeft,
  Edit,
  Trash2,
  Save,
  Download,
  Send,
  FileText,
  Building,
  Briefcase,
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
import { useSupabase } from '../../components/SupabaseProvider'
import { formatCurrency, formatDate } from '../../../lib/utils'

interface Invoice {
  id: string
  invoice_number: string
  amount: number
  status: string
  issue_date: string
  due_date: string
  company_id: string | null
  project_id: string | null
  description: string | null
  notes: string | null
  created_at: string
  updated_at: string
  companies?: {
    id: string
    name: string
  } | null
  projects?: {
    id: string
    name: string
  } | null
}

interface Company {
  id: string
  name: string
}

interface Project {
  id: string
  name: string
  company_id: string | null
}

export default function InvoiceDetail({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { supabase } = useSupabase()
  const [invoice, setInvoice] = useState<Invoice | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [companies, setCompanies] = useState<Company[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([])
  
  // Form state
  const [invoiceNumber, setInvoiceNumber] = useState('')
  const [amount, setAmount] = useState('')
  const [status, setStatus] = useState('')
  const [issueDate, setIssueDate] = useState('')
  const [dueDate, setDueDate] = useState('')
  const [companyId, setCompanyId] = useState('')
  const [projectId, setProjectId] = useState('')
  const [description, setDescription] = useState('')
  const [notes, setNotes] = useState('')
  
  // Fetch invoice data
  useEffect(() => {
    async function fetchInvoice() {
      setIsLoading(true)
      setError(null)
      
      try {
        const { data, error } = await supabase
          .from('invoices')
          .select(`
            *,
            companies:company_id (id, name),
            projects:project_id (id, name)
          `)
          .eq('id', params.id)
          .single()
        
        if (error) throw error
        
        setInvoice(data)
        
        // Initialize form state
        setInvoiceNumber(data.invoice_number)
        setAmount(data.amount.toString())
        setStatus(data.status)
        setIssueDate(data.issue_date)
        setDueDate(data.due_date)
        setCompanyId(data.company_id || '')
        setProjectId(data.project_id || '')
        setDescription(data.description || '')
        setNotes(data.notes || '')
      } catch (err) {
        console.error('Error fetching invoice:', err)
        setError('Failed to load invoice details')
      } finally {
        setIsLoading(false)
      }
    }
    
    // Fetch companies and projects
    async function fetchRelatedData() {
      try {
        // Fetch companies
        const { data: companiesData, error: companiesError } = await supabase
          .from('companies')
          .select('id, name')
          .order('name')
        
        if (companiesError) {
          console.error('Error fetching companies:', companiesError)
        } else {
          setCompanies(companiesData || [])
        }
        
        // Fetch projects
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select('id, name, company_id')
          .order('name')
        
        if (projectsError) {
          console.error('Error fetching projects:', projectsError)
        } else {
          setProjects(projectsData || [])
        }
      } catch (err) {
        console.error('Unexpected error:', err)
      }
    }
    
    fetchInvoice()
    fetchRelatedData()
  }, [supabase, params.id])
  
  // Filter projects when company changes
  useEffect(() => {
    if (companyId) {
      setFilteredProjects(projects.filter(project => project.company_id === companyId))
    } else {
      setFilteredProjects(projects)
    }
    
    // Reset project selection if company changes and not in initial load
    if (isEditing && invoice?.company_id !== companyId) {
      setProjectId('')
    }
  }, [companyId, projects, isEditing, invoice])
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    setError(null)
    
    try {
      // Validate form
      if (!invoiceNumber || !amount || !status || !issueDate || !dueDate) {
        setError('Invoice number, amount, status, issue date, and due date are required')
        setIsSaving(false)
        return
      }
      
      // Parse amount to number
      const numericAmount = parseFloat(amount)
      if (isNaN(numericAmount)) {
        setError('Amount must be a valid number')
        setIsSaving(false)
        return
      }
      
      // Update invoice record
      const { error: updateError } = await supabase
        .from('invoices')
        .update({
          invoice_number: invoiceNumber,
          amount: numericAmount,
          status,
          issue_date: issueDate,
          due_date: dueDate,
          company_id: companyId || null,
          project_id: projectId || null,
          description: description || null,
          notes: notes || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', params.id)
      
      if (updateError) throw updateError
      
      // Fetch updated invoice
      const { data: updatedInvoice, error: fetchError } = await supabase
        .from('invoices')
        .select(`
          *,
          companies:company_id (id, name),
          projects:project_id (id, name)
        `)
        .eq('id', params.id)
        .single()
      
      if (fetchError) {
        console.error('Error fetching updated invoice:', fetchError)
      } else {
        setInvoice(updatedInvoice)
      }
      
      setIsEditing(false)
    } catch (err) {
      console.error('Error updating invoice:', err)
      setError('Failed to update invoice')
    } finally {
      setIsSaving(false)
    }
  }
  
  // Handle invoice deletion
  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this invoice? This action cannot be undone.')) {
      return
    }
    
    setIsDeleting(true)
    
    try {
      const { error } = await supabase
        .from('invoices')
        .delete()
        .eq('id', params.id)
      
      if (error) throw error
      
      // Redirect to invoices list
      router.push('/invoices')
    } catch (err) {
      console.error('Error deleting invoice:', err)
      setError('Failed to delete invoice')
      setIsDeleting(false)
    }
  }
  
  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'draft':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-blue-100 text-blue-800'
    }
  }
  
  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
        return <CheckCircle className="h-4 w-4 mr-1" />
      case 'pending':
        return <Clock className="h-4 w-4 mr-1" />
      case 'overdue':
        return <AlertCircle className="h-4 w-4 mr-1" />
      case 'draft':
        return <FileText className="h-4 w-4 mr-1" />
      default:
        return null
    }
  }
  
  // Invoice statuses
  const invoiceStatuses = [
    'Draft',
    'Pending',
    'Paid',
    'Overdue',
    'Cancelled'
  ]
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }
  
  if (error && !invoice) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
        {error}
        <div className="mt-4">
          <Link href="/invoices" className="text-red-700 underline">
            Return to invoices list
          </Link>
        </div>
      </div>
    )
  }
  
  if (!invoice) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md">
        Invoice not found
        <div className="mt-4">
          <Link href="/invoices" className="text-yellow-700 underline">
            Return to invoices list
          </Link>
        </div>
      </div>
    )
  }
  
  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/invoices" className="mr-4 p-2 rounded-full hover:bg-gray-100">
            <ArrowLeft className="h-5 w-5 text-gray-500" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {isEditing ? 'Edit Invoice' : `Invoice #${invoice.invoice_number}`}
            </h1>
            <div className="flex items-center mt-1">
              <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center ${getStatusColor(invoice.status)}`}>
                {getStatusIcon(invoice.status)}
                {invoice.status}
              </span>
              <span className="ml-2 text-gray-600">
                {formatCurrency(invoice.amount)}
              </span>
            </div>
          </div>
        </div>
        
        <div className="flex space-x-2">
          {!isEditing ? (
            <>
              <button
                onClick={() => setIsEditing(true)}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-sm font-medium text-white flex items-center"
              >
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </button>
              <button
                onClick={handleDelete}
                disabled={isDeleting}
                className="px-3 py-2 bg-red-600 hover:bg-red-700 rounded-md text-sm font-medium text-white flex items-center"
              >
                {isDeleting ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                ) : (
                  <Trash2 className="h-4 w-4 mr-1" />
                )}
                Delete
              </button>
            </>
          ) : (
            <button
              onClick={() => setIsEditing(false)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
          )}
        </div>
      </div>
      
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}
      
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        {isEditing ? (
          <form onSubmit={handleSubmit} className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Invoice Number */}
              <div>
                <label htmlFor="invoiceNumber" className="block text-sm font-medium text-gray-700 mb-1">
                  Invoice Number <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="invoiceNumber"
                  value={invoiceNumber}
                  onChange={(e) => setInvoiceNumber(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., INV-001"
                  required
                />
              </div>
              
              {/* Amount */}
              <div>
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                  Amount <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <DollarSign className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="number"
                    id="amount"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    className="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                    required
                  />
                </div>
              </div>
              
              {/* Status */}
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                  Status <span className="text-red-500">*</span>
                </label>
                <select
                  id="status"
                  value={status}
                  onChange={(e) => setStatus(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  {invoiceStatuses.map((statusOption) => (
                    <option key={statusOption} value={statusOption}>
                      {statusOption}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Issue Date */}
              <div>
                <label htmlFor="issueDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Issue Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  id="issueDate"
                  value={issueDate}
                  onChange={(e) => setIssueDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              
              {/* Due Date */}
              <div>
                <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Due Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  id="dueDate"
                  value={dueDate}
                  onChange={(e) => setDueDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              
              {/* Company */}
              <div>
                <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
                  Company
                </label>
                <select
                  id="company"
                  value={companyId}
                  onChange={(e) => setCompanyId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a company</option>
                  {companies.map((company) => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Project */}
              <div>
                <label htmlFor="project" className="block text-sm font-medium text-gray-700 mb-1">
                  Project
                </label>
                <select
                  id="project"
                  value={projectId}
                  onChange={(e) => setProjectId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!companyId}
                >
                  <option value="">Select a project</option>
                  {filteredProjects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
                {!companyId && (
                  <p className="mt-1 text-xs text-gray-500">Select a company first</p>
                )}
              </div>
              
              {/* Description */}
              <div className="col-span-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Describe the invoice..."
                ></textarea>
              </div>
              
              {/* Notes */}
              <div className="col-span-2">
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add any additional notes about this invoice..."
                ></textarea>
              </div>
            </div>
            
            <div className="mt-8 flex justify-end">
              <button
                type="submit"
                disabled={isSaving}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-sm font-medium text-white flex items-center"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-1" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </form>
        ) : (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="col-span-2 md:col-span-1">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Invoice Details</h2>
                
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Invoice Number</p>
                    <p className="text-gray-900">{invoice.invoice_number}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">Amount</p>
                    <p className="text-gray-900 font-medium">{formatCurrency(invoice.amount)}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">Status</p>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium inline-flex items-center ${getStatusColor(invoice.status)}`}>
                      {getStatusIcon(invoice.status)}
                      {invoice.status}
                    </span>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">Issue Date</p>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                      <p className="text-gray-900">{formatDate(invoice.issue_date)}</p>
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">Due Date</p>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                      <p className="text-gray-900">{formatDate(invoice.due_date)}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="col-span-2 md:col-span-1">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Related Information</h2>
                
                <div className="space-y-4">
                  {invoice.companies && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Company</p>
                      <div className="flex items-center mt-1">
                        <Building className="h-4 w-4 text-gray-400 mr-1" />
                        <Link href={`/companies/${invoice.companies.id}`} className="text-blue-600 hover:text-blue-800">
                          {invoice.companies.name}
                        </Link>
                      </div>
                    </div>
                  )}
                  
                  {invoice.projects && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Project</p>
                      <div className="flex items-center mt-1">
                        <Briefcase className="h-4 w-4 text-gray-400 mr-1" />
                        <Link href={`/projects/${invoice.projects.id}`} className="text-blue-600 hover:text-blue-800">
                          {invoice.projects.name}
                        </Link>
                      </div>
                    </div>
                  )}
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">Created</p>
                    <p className="text-gray-900">{formatDate(invoice.created_at)}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">Last Updated</p>
                    <p className="text-gray-900">{formatDate(invoice.updated_at)}</p>
                  </div>
                </div>
                
                <div className="mt-6 flex space-x-3">
                  <button className="px-3 py-2 bg-green-600 hover:bg-green-700 rounded-md text-sm font-medium text-white flex items-center">
                    <Download className="h-4 w-4 mr-1" />
                    Download PDF
                  </button>
                  <button className="px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-sm font-medium text-white flex items-center">
                    <Send className="h-4 w-4 mr-1" />
                    Send Invoice
                  </button>
                </div>
              </div>
              
              {(invoice.description || invoice.notes) && (
                <div className="col-span-2">
                  {invoice.description && (
                    <div className="mb-4">
                      <h2 className="text-lg font-medium text-gray-900 mb-2">Description</h2>
                      <div className="bg-gray-50 p-4 rounded-md">
                        <p className="text-gray-900 whitespace-pre-wrap">{invoice.description}</p>
                      </div>
                    </div>
                  )}
                  
                  {invoice.notes && (
                    <div>
                      <h2 className="text-lg font-medium text-gray-900 mb-2">Notes</h2>
                      <div className="bg-gray-50 p-4 rounded-md">
                        <p className="text-gray-900 whitespace-pre-wrap">{invoice.notes}</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
