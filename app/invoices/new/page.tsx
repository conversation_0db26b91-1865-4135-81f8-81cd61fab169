'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Plus, Trash2 } from 'lucide-react'
import Link from 'next/link'
import { useSupabase } from '../../components/SupabaseProvider'
import { formatCurrency } from '../../../lib/utils'

interface Company {
  id: string
  name: string
}

interface Project {
  id: string
  name: string
  company_id: string | null
}

interface InvoiceItem {
  id: string
  description: string
  quantity: number
  rate: number
  amount: number
}

export default function NewInvoice() {
  const router = useRouter()
  const { supabase, user } = useSupabase()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [companies, setCompanies] = useState<Company[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([])
  const [isLoading, setIsLoading] = useState(true)
  
  // Main invoice data
  const [invoiceData, setInvoiceData] = useState({
    company_id: '',
    project_id: '',
    invoice_number: '',
    issue_date: new Date().toISOString().substring(0, 10), // Today in yyyy-mm-dd format
    due_date: '',
    status: 'draft',
    notes: ''
  })
  
  // Invoice items (line items)
  const [items, setItems] = useState<InvoiceItem[]>([
    {
      id: crypto.randomUUID(),
      description: '',
      quantity: 1,
      rate: 0,
      amount: 0
    }
  ])
  
  // Calculate due date 30 days from issue date initially
  useEffect(() => {
    if (invoiceData.issue_date) {
      const issueDate = new Date(invoiceData.issue_date)
      const dueDate = new Date(issueDate)
      dueDate.setDate(dueDate.getDate() + 30) // 30 days later
      setInvoiceData(prev => ({
        ...prev,
        due_date: dueDate.toISOString().substring(0, 10)
      }))
    }
  }, [])
  
  // Invoice number generation
  useEffect(() => {
    const generateInvoiceNumber = () => {
      const date = new Date()
      const year = date.getFullYear().toString().slice(-2)
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const randomPart = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
      return `INV-${year}${month}-${randomPart}`
    }
    
    setInvoiceData(prev => ({
      ...prev,
      invoice_number: generateInvoiceNumber()
    }))
  }, [])
  
  useEffect(() => {
    async function fetchData() {
      setIsLoading(true)
      
      try {
        console.log('Starting to fetch companies and projects...')
        console.log('Auth user from context:', user?.email || 'Not logged in')
        
        // Check auth status
        const { data: authData, error: authError } = await supabase.auth.getSession()
        console.log('Auth status:', authData?.session ? 'Authenticated' : 'Not authenticated', authError || '')
        
        // Check user role
        if (user) {
          const { data: roleData, error: roleError } = await supabase
            .from('user_roles')
            .select('role')
            .eq('user_id', user.id)
            .single()
            
          console.log('User role:', roleData?.role || 'None', roleError || '')
        }
        
        // Fetch companies
        const { data: companiesData, error: companiesError } = await supabase
          .from('companies')
          .select('id, name')
          .order('name')
        
        if (companiesError) {
          console.error('Error fetching companies:', companiesError)
          
          // Try fallback to API route that uses service role
          console.log('Trying fallback API route for companies...')
          try {
            const response = await fetch('/api/debug/companies')
            const apiData = await response.json()
            
            if (apiData.companies) {
              console.log('Companies fetched from API:', apiData.companies)
              setCompanies(apiData.companies)
            } else {
              throw new Error('No companies returned from API')
            }
          } catch (apiError) {
            console.error('API fallback also failed:', apiError)
            throw companiesError // Still throw the original error
          }
        } else {
          console.log('Companies fetched directly:', companiesData)
          setCompanies(companiesData || [])
        }
        
        // Fetch projects
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select('id, name, company_id')
          .order('name')
        
        if (projectsError) {
          console.error('Error fetching projects:', projectsError)
          throw projectsError
        }
        
        console.log('Projects fetched:', projectsData)
        
        setProjects(projectsData || [])
        console.log('State updated with companies and projects')
        
      } catch (err) {
        console.error('Error fetching data:', err)
        setError('Failed to load companies and projects')
      }
      
      setIsLoading(false)
    }
    
    fetchData()
  }, [])
  
  // Filter projects when company changes
  useEffect(() => {
    console.log('Company ID changed to:', invoiceData.company_id)
    if (invoiceData.company_id) {
      const filtered = projects.filter(project => project.company_id === invoiceData.company_id)
      console.log('Filtered projects:', filtered)
      setFilteredProjects(filtered)
      
      // Reset project selection if current selection is not for this company
      if (invoiceData.project_id) {
        const projectExists = filtered.some(p => p.id === invoiceData.project_id)
        if (!projectExists) {
          setInvoiceData(prev => ({...prev, project_id: ''}))
        }
      }
    } else {
      setFilteredProjects([])
      setInvoiceData(prev => ({...prev, project_id: ''}))
    }
  }, [invoiceData.company_id, projects, invoiceData.project_id])
  
  // Recalculate line item amounts when quantity or rate changes
  useEffect(() => {
    const updatedItems = items.map(item => {
      const amount = item.quantity * item.rate
      return {
        ...item,
        amount
      }
    })
    
    setItems(updatedItems)
  }, [items.map(item => `${item.quantity}-${item.rate}`).join('-')])
  
  const handleInvoiceChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setInvoiceData(prev => ({
      ...prev,
      [name]: value
    }))
  }
  
  const handleItemChange = (id: string, field: keyof InvoiceItem, value: string | number) => {
    setItems(prev => 
      prev.map(item => 
        item.id === id
          ? { ...item, [field]: typeof value === 'string' && field !== 'description' ? parseFloat(value) || 0 : value }
          : item
      )
    )
  }
  
  const addItem = () => {
    setItems(prev => [
      ...prev,
      {
        id: crypto.randomUUID(),
        description: '',
        quantity: 1,
        rate: 0,
        amount: 0
      }
    ])
  }
  
  const removeItem = (id: string) => {
    if (items.length > 1) {
      setItems(prev => prev.filter(item => item.id !== id))
    }
  }
  
  const calculateTotal = (): number => {
    return items.reduce((sum, item) => sum + item.amount, 0)
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)
    
    if (!invoiceData.company_id) {
      setError('Please select a company')
      setIsSubmitting(false)
      return
    }
    
    try {
      // First, insert the invoice
      const { data: invoiceResult, error: invoiceError } = await supabase
        .from('invoices')
        .insert([
          {
            ...invoiceData,
            amount: calculateTotal(),
            // Convert empty string to null for project_id
            project_id: invoiceData.project_id || null,
          }
        ])
        .select()
      
      if (invoiceError) throw invoiceError
      
      if (!invoiceResult || invoiceResult.length === 0) {
        throw new Error('Failed to create invoice - no ID returned')
      }
      
      const invoiceId = invoiceResult[0].id
      
      // Then, insert all the invoice items
      const itemsToInsert = items.map(item => ({
        invoice_id: invoiceId,
        description: item.description,
        quantity: item.quantity,
        unit_price: item.rate,
        amount: item.amount
      }))
      
      const { error: itemsError } = await supabase
        .from('invoice_items')
        .insert(itemsToInsert)
      
      if (itemsError) throw itemsError
      
      // Navigate to invoices list
      router.push('/invoices')
      router.refresh()
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create invoice'
      setError(errorMessage)
      setIsSubmitting(false)
    }
  }
  
  return (
    <div>
      <div className="mb-6">
        <Link
          href="/invoices"
          className="text-gray-500 hover:text-gray-700 inline-flex items-center"
        >
          <ArrowLeft size={16} className="mr-1" />
          Back to Invoices
        </Link>
        <h1 className="text-2xl font-bold text-gray-900 mt-4">Create New Invoice</h1>
        <p className="text-gray-600">Generate a new client invoice</p>
      </div>
      
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md">
          {error}
        </div>
      )}
      
      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="col-span-2">
              <label htmlFor="company_id" className="block text-sm font-medium text-gray-700 mb-1">
                Client Company <span className="text-red-500">*</span>
              </label>
              {isLoading ? (
                <p>Loading companies...</p>
              ) : companies.length === 0 ? (
                <div>
                  <p className="text-red-500">No companies found. Please create a company first.</p>
                  <p className="text-sm text-gray-500">Debug info: isLoading={String(isLoading)}, companies.length={companies.length}</p>
                  <div className="mt-2 p-2 bg-gray-100 rounded text-sm">
                    <p>Technical info:</p>
                    <ul className="list-disc pl-5">
                      <li>User authenticated: {user ? 'Yes' : 'No'}</li>
                      <li>User email: {user?.email || 'None'}</li>
                      <li><Link href="/companies/new" className="text-blue-600 hover:underline">Create a new company</Link></li>
                      <li><Link href="/companies" className="text-blue-600 hover:underline">View all companies</Link></li>
                    </ul>
                  </div>
                </div>
              ) : (
                <div>
                  <select
                    id="company_id"
                    name="company_id"
                    required
                    value={invoiceData.company_id}
                    onChange={handleInvoiceChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select a company</option>
                    {companies.map(company => (
                      <option key={company.id} value={company.id}>
                        {company.name}
                      </option>
                    ))}
                  </select>
                  <p className="text-sm text-gray-500 mt-1">Available companies: {companies.length}</p>
                </div>
              )}
            </div>
            
            <div className="col-span-2">
              <label htmlFor="project_id" className="block text-sm font-medium text-gray-700 mb-1">
                Project
              </label>
              <select
                id="project_id"
                name="project_id"
                value={invoiceData.project_id}
                onChange={handleInvoiceChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading || !invoiceData.company_id}
              >
                <option value="">Select a project (optional)</option>
                {filteredProjects.map(project => (
                  <option key={project.id} value={project.id}>
                    {project.name}
                  </option>
                ))}
              </select>
              {invoiceData.company_id && filteredProjects.length === 0 && (
                <p className="mt-1 text-sm text-amber-600">No projects found for this company</p>
              )}
            </div>
            
            <div>
              <label htmlFor="invoice_number" className="block text-sm font-medium text-gray-700 mb-1">
                Invoice Number
              </label>
              <input
                type="text"
                id="invoice_number"
                name="invoice_number"
                value={invoiceData.invoice_number}
                onChange={handleInvoiceChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g. INV-2023001"
              />
            </div>
            
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={invoiceData.status}
                onChange={handleInvoiceChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="draft">Draft</option>
                <option value="sent">Sent</option>
                <option value="paid">Paid</option>
                <option value="overdue">Overdue</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="issue_date" className="block text-sm font-medium text-gray-700 mb-1">
                Issue Date
              </label>
              <input
                type="date"
                id="issue_date"
                name="issue_date"
                value={invoiceData.issue_date}
                onChange={handleInvoiceChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label htmlFor="due_date" className="block text-sm font-medium text-gray-700 mb-1">
                Due Date
              </label>
              <input
                type="date"
                id="due_date"
                name="due_date"
                value={invoiceData.due_date}
                onChange={handleInvoiceChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          {/* Invoice Items */}
          <div className="mt-8">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Invoice Items</h3>
            
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border border-gray-200 rounded-md">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                      Quantity
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                      Rate
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                      Amount
                    </th>
                    <th className="px-4 py-2 w-16"></th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {items.map((item) => (
                    <tr key={item.id}>
                      <td className="px-4 py-2">
                        <input
                          type="text"
                          value={item.description}
                          onChange={(e) => handleItemChange(item.id, 'description', e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="Item description"
                        />
                      </td>
                      <td className="px-4 py-2">
                        <input
                          type="number"
                          value={item.quantity}
                          min="1"
                          step="1"
                          onChange={(e) => handleItemChange(item.id, 'quantity', e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-4 py-2">
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                            <span className="text-gray-500">$</span>
                          </div>
                          <input
                            type="number"
                            value={item.rate}
                            min="0"
                            step="0.01"
                            onChange={(e) => handleItemChange(item.id, 'rate', e.target.value)}
                            className="w-full pl-6 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                          />
                        </div>
                      </td>
                      <td className="px-4 py-2 text-gray-900 font-medium">
                        {formatCurrency(item.amount)}
                      </td>
                      <td className="px-4 py-2">
                        <button
                          type="button"
                          onClick={() => removeItem(item.id)}
                          className="text-red-500 hover:text-red-700 focus:outline-none disabled:opacity-50"
                          disabled={items.length <= 1}
                        >
                          <Trash2 size={18} />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan={5} className="px-4 py-2">
                      <button
                        type="button"
                        onClick={addItem}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <Plus size={16} className="mr-1" />
                        Add Item
                      </button>
                    </td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td colSpan={3} className="px-4 py-2 text-right font-medium text-gray-700">
                      Total:
                    </td>
                    <td className="px-4 py-2 text-gray-900 font-bold">
                      {formatCurrency(calculateTotal())}
                    </td>
                    <td></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
          
          <div className="mt-6">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              rows={3}
              value={invoiceData.notes}
              onChange={handleInvoiceChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Payment terms, thank you message, or other notes for the client"
            ></textarea>
          </div>
          
          <div className="mt-8 flex justify-end space-x-4">
            <Link
              href="/invoices"
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
            >
              {isSubmitting ? 'Creating Invoice...' : 'Create Invoice'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
} 