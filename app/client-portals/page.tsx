'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import {
  Plus, Search, Building,
  Users, Briefcase, FileText, MoreHorizontal,
  Filter, Trash2, Edit, Eye, ArrowUpRight,
  Key, UserCog, Database, AlertCircle, CheckCircle, Loader2
} from 'lucide-react'
import { createBrowserClient } from '@supabase/ssr'
import { cn, getStatusBadgeClass } from '../../lib/utils'
import ClientCredentialModal from '../components/ClientCredentialModal'

interface Company {
  id: string
  name: string
  industry: string | null
  status: string | null
  _count?: {
    contacts: number
    projects: number
    credentials: number
    invoices: number
  }
}

interface MigrationStatus {
  clientUsersExists: boolean
  clientSessionsExists: boolean
  verifyFunctionExists: boolean
}

export default function ClientPortals() {
  const [companies, setCompanies] = useState<Company[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null)
  const [credentialModalOpen, setCredentialModalOpen] = useState(false)
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null)
  const [migrationStatus, setMigrationStatus] = useState<MigrationStatus | null>(null)
  const [checkingMigration, setCheckingMigration] = useState(false)
  const [runningMigration, setRunningMigration] = useState(false)
  const [migrationMessage, setMigrationMessage] = useState<{ type: 'success' | 'error', message: string } | null>(null)

  // Check if client authentication is set up
  const checkMigrationStatus = async () => {
    setCheckingMigration(true)
    setMigrationMessage(null)

    try {
      // Get the current Supabase session
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )

      const { data: sessionData } = await supabase.auth.getSession()
      const authToken = sessionData?.session?.access_token

      const response = await fetch('/api/migrations/client-portal-auth', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to check migration status')
      }

      const data = await response.json()
      setMigrationStatus(data)
    } catch (err) {
      console.error('Error checking migration status:', err)
      setMigrationMessage({
        type: 'error',
        message: 'Failed to check migration status'
      })
    } finally {
      setCheckingMigration(false)
    }
  }

  // Run the migrations
  const runMigrations = async () => {
    setRunningMigration(true)
    setMigrationMessage(null)

    try {
      // Get the current Supabase session
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )

      const { data: sessionData } = await supabase.auth.getSession()
      const authToken = sessionData?.session?.access_token

      const response = await fetch('/api/migrations/client-portal-auth', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to run migrations')
      }

      const data = await response.json()

      if (data.success) {
        setMigrationMessage({
          type: 'success',
          message: 'Client authentication system set up successfully'
        })

        // Check migration status again
        checkMigrationStatus()
      } else {
        throw new Error(data.error || 'Unknown error')
      }
    } catch (err) {
      console.error('Error running migrations:', err)
      setMigrationMessage({
        type: 'error',
        message: err instanceof Error ? err.message : 'Failed to run migrations'
      })
    } finally {
      setRunningMigration(false)
    }
  }

  useEffect(() => {
    async function fetchCompanies() {
      setIsLoading(true)

      try {
        // Create a Supabase client
        const supabase = createBrowserClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
        )

        const { data, error } = await supabase
          .from('companies')
          .select('id, name, industry, status')
          .order('name')

        if (error) {
          console.error('Error fetching companies:', error)
          setCompanies([])
        } else if (data) {
          // Fetch counts for each company
          const companiesWithCounts = await Promise.all(
            data.map(async (company) => {
              // Get contacts count
              const { count: contactsCount } = await supabase
                .from('contacts')
                .select('id', { count: 'exact', head: true })
                .eq('company_id', company.id)

              // Get projects count
              const { count: projectsCount } = await supabase
                .from('projects')
                .select('id', { count: 'exact', head: true })
                .eq('company_id', company.id)

              // Get credentials count
              const { count: credentialsCount } = await supabase
                .from('credentials')
                .select('id', { count: 'exact', head: true })
                .eq('company_id', company.id)

              // Get invoices count
              const { count: invoicesCount } = await supabase
                .from('invoices')
                .select('id', { count: 'exact', head: true })
                .eq('company_id', company.id)

              return {
                ...company,
                _count: {
                  contacts: contactsCount || 0,
                  projects: projectsCount || 0,
                  credentials: credentialsCount || 0,
                  invoices: invoicesCount || 0
                }
              }
            })
          )

          setCompanies(companiesWithCounts)
        }
      } catch (err) {
        console.error('Unexpected error:', err)
        setCompanies([])
      }

      setIsLoading(false)
    }

    fetchCompanies()
    checkMigrationStatus()
  }, [])

  // Filter companies based on search query
  const filteredCompanies = companies.filter(company => {
    return company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      company.industry?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      company.status?.toLowerCase().includes(searchQuery.toLowerCase())
  })

  // Toggle dropdown menu
  const toggleDropdown = (id: string) => {
    if (dropdownOpen === id) {
      setDropdownOpen(null)
    } else {
      setDropdownOpen(id)
    }
  }

  // Open credential management modal
  const openCredentialModal = (company: Company) => {
    setSelectedCompany(company)
    setCredentialModalOpen(true)
    setDropdownOpen(null) // Close any open dropdown
  }

  // Handle click outside dropdown
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (dropdownOpen && !(e.target as Element).closest('.dropdown-menu')) {
        setDropdownOpen(null)
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [dropdownOpen])

  // Check if client auth is fully set up
  // Temporarily bypass migration check since tables exist
  const isClientAuthSetup = true; // migrationStatus?.clientUsersExists &&
    // migrationStatus?.clientSessionsExists &&
    // migrationStatus?.verifyFunctionExists;

  return (
    <div className="animate-fade-in py-6 space-y-8 max-w-7xl mx-auto">
      {/* Enhanced Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg">
              <Building className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                Client Portals
              </h1>
              <p className="text-gray-600 dark:text-gray-400 text-lg">
                Manage client portals and access client-specific information
              </p>
            </div>
          </div>
        </div>

        <div className="flex gap-3">
          {!isClientAuthSetup && (
            <button
              onClick={runMigrations}
              disabled={runningMigration || checkingMigration}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 border-2 border-gray-300 dark:border-gray-600 rounded-xl font-medium hover:border-orange-500 dark:hover:border-orange-400 hover:text-orange-600 dark:hover:text-orange-400 transition-all duration-200 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {runningMigration ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  Setting up...
                </>
              ) : (
                <>
                  <Database className="h-5 w-5" />
                  Setup Auth System
                </>
              )}
            </button>
          )}
          <Link
            href="/companies/new"
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            <Plus size={20} />
            New Client
          </Link>
        </div>
      </div>

      {/* Migration status message */}
      {migrationMessage && (
        <div className={cn(
          "p-4 rounded-md flex items-start",
          migrationMessage.type === 'success'
            ? "bg-success/10 border border-success/20 text-success"
            : "bg-destructive/10 border border-destructive/20 text-destructive"
        )}>
          {migrationMessage.type === 'success'
            ? <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
            : <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />}
          <div>{migrationMessage.message}</div>
        </div>
      )}

      {/* Auth status */}
      {!isClientAuthSetup && !migrationMessage && (
        <div className="bg-amber-50 border border-amber-200 text-amber-800 p-4 rounded-md flex items-start">
          <Database className="h-5 w-5 mr-2 mt-0.5" />
          <div>
            <p className="font-medium">Client portal authentication system not fully set up</p>
            <p className="mt-1 text-sm">
              You need to set up the client authentication system before you can create client portal users.
              Click the &quot;Setup Auth System&quot; button to create the necessary database tables.
            </p>
          </div>
        </div>
      )}

      {/* Enhanced Search and filter bar */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Enhanced Search */}
        <div className="relative flex-grow">
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500">
            <Search className="h-5 w-5" />
          </div>
          <input
            type="text"
            className="w-full pl-12 pr-4 py-3 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-700 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-0 transition-all duration-200 shadow-sm focus:shadow-md"
            placeholder="Search clients by name, industry, or status..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* Enhanced Filter button */}
        <button className="inline-flex items-center gap-2 px-6 py-3 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 border-2 border-gray-200 dark:border-gray-700 rounded-xl font-medium hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 shadow-sm hover:shadow-md">
          <Filter size={18} />
          <span>Filter</span>
        </button>
      </div>

      {/* Client portals grid */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64" data-testid="loading-indicator">
           <div className="w-16 h-16 relative">
             <div className="absolute top-0 left-0 right-0 bottom-0 rounded-full border-4 border-primary/20"></div>
             <div className="absolute top-0 left-0 right-0 bottom-0 rounded-full border-4 border-transparent border-t-primary animate-spin"></div>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredCompanies.length > 0 ? (
            filteredCompanies.map((company, index) => (
              <div key={company.id} className="group relative">
                {/* Modern Card with Gradient Border */}
                <div className="relative bg-gradient-to-br from-white via-gray-50 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-700 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200/50 dark:border-gray-700/50 overflow-hidden">

                  {/* Gradient Accent Bar */}
                  <div className={`h-1.5 w-full bg-gradient-to-r ${
                    index % 3 === 0 ? 'from-blue-500 to-purple-600' :
                    index % 3 === 1 ? 'from-emerald-500 to-teal-600' :
                    'from-orange-500 to-pink-600'
                  }`} />

                  {/* Dropdown menu trigger */}
                  <div className="absolute top-6 right-6 z-10">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleDropdown(company.id);
                      }}
                      className="p-2 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:bg-white dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none transition-all duration-200 shadow-md hover:shadow-lg"
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </button>

                    {/* Enhanced Dropdown menu */}
                    {dropdownOpen === company.id && (
                      <div className="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-xl shadow-xl ring-1 ring-black/5 dark:ring-white/10 py-2 dropdown-menu border border-gray-100 dark:border-gray-700 backdrop-blur-sm">
                        <button
                          onClick={() => openCredentialModal(company)}
                          className="flex w-full items-center px-4 py-3 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-700 dark:hover:text-blue-300 transition-colors group"
                          disabled={!isClientAuthSetup}
                        >
                          <UserCog className="mr-3 h-4 w-4 text-blue-500 group-hover:text-blue-600" />
                          Manage Access
                        </button>
                        <Link
                          href={`/companies/${company.id}/edit`}
                          className="flex items-center px-4 py-3 text-sm text-gray-700 dark:text-gray-200 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 hover:text-emerald-700 dark:hover:text-emerald-300 transition-colors group"
                        >
                          <Edit className="mr-3 h-4 w-4 text-emerald-500 group-hover:text-emerald-600" />
                          Edit Company
                        </Link>
                        <Link
                          href={`/companies/${company.id}`}
                          className="flex items-center px-4 py-3 text-sm text-gray-700 dark:text-gray-200 hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:text-purple-700 dark:hover:text-purple-300 transition-colors group"
                        >
                          <Eye className="mr-3 h-4 w-4 text-purple-500 group-hover:text-purple-600" />
                          View in CRM
                        </Link>
                        <div className="border-t border-gray-100 dark:border-gray-700 my-1" />
                        <button
                          className="flex w-full items-center px-4 py-3 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-300 transition-colors group"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle delete logic
                            alert(`Delete ${company.name}`);
                          }}
                        >
                          <Trash2 className="mr-3 h-4 w-4 text-red-500 group-hover:text-red-600" />
                          Delete Company
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="p-8">
                    {/* Enhanced Client Info */}
                    <div className="flex items-start space-x-4 mb-6">
                      <div className={`p-4 rounded-2xl shadow-lg ${
                        index % 3 === 0 ? 'bg-gradient-to-br from-blue-500 to-purple-600' :
                        index % 3 === 1 ? 'bg-gradient-to-br from-emerald-500 to-teal-600' :
                        'bg-gradient-to-br from-orange-500 to-pink-600'
                      }`}>
                        <Building className="h-7 w-7 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h2 className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors mb-2 truncate">
                          {company.name}
                        </h2>
                        <div className="flex flex-wrap gap-2 items-center">
                          {company.industry && (
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                              {company.industry}
                            </span>
                          )}
                          {company.status && (
                            <span className={cn(
                              "inline-flex items-center px-3 py-1 rounded-full text-xs font-medium",
                              getStatusBadgeClass(company.status)
                            )}>
                              {company.status}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Metrics Grid */}
                    <div className="grid grid-cols-2 gap-4 mb-8">
                      <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl p-4 border border-blue-200/50 dark:border-blue-700/50">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{company._count?.projects || 0}</p>
                            <p className="text-xs font-medium text-blue-600 dark:text-blue-400 mt-1">Projects</p>
                          </div>
                          <Briefcase className="h-8 w-8 text-blue-500 opacity-80" />
                        </div>
                      </div>

                      <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 rounded-xl p-4 border border-emerald-200/50 dark:border-emerald-700/50">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-2xl font-bold text-emerald-700 dark:text-emerald-300">{company._count?.contacts || 0}</p>
                            <p className="text-xs font-medium text-emerald-600 dark:text-emerald-400 mt-1">Contacts</p>
                          </div>
                          <Users className="h-8 w-8 text-emerald-500 opacity-80" />
                        </div>
                      </div>

                      <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl p-4 border border-purple-200/50 dark:border-purple-700/50">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">{company._count?.credentials || 0}</p>
                            <p className="text-xs font-medium text-purple-600 dark:text-purple-400 mt-1">Credentials</p>
                          </div>
                          <Key className="h-8 w-8 text-purple-500 opacity-80" />
                        </div>
                      </div>

                      <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-xl p-4 border border-orange-200/50 dark:border-orange-700/50">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">{company._count?.invoices || 0}</p>
                            <p className="text-xs font-medium text-orange-600 dark:text-orange-400 mt-1">Invoices</p>
                          </div>
                          <FileText className="h-8 w-8 text-orange-500 opacity-80" />
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Action buttons */}
                    <div className="flex gap-3">
                      <button
                        onClick={() => openCredentialModal(company)}
                        className={cn(
                          "flex-1 inline-flex items-center justify-center gap-2 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200",
                          !isClientAuthSetup
                            ? "bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed"
                            : "bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105"
                        )}
                        disabled={!isClientAuthSetup}
                      >
                        <UserCog className="h-4 w-4" />
                        Manage Access
                      </button>
                      <Link
                        href={`/client-view/${company.id}`}
                        className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-3 rounded-xl text-sm font-medium bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 border-2 border-gray-200 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                      >
                        Access Portal
                        <ArrowUpRight className="h-4 w-4" />
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-1 md:col-span-2 lg:col-span-3">
              <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-2xl border-2 border-dashed border-gray-300 dark:border-gray-600 p-12 flex flex-col items-center justify-center text-center">
                <div className="bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full p-6 mb-6">
                  <Search className="h-12 w-12 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">No clients found</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-md">
                  We couldn&apos;t find any clients matching your search criteria. Try adjusting your search or create a new client.
                </p>
                <div className="flex gap-3">
                  <button
                    onClick={() => setSearchQuery('')}
                    className="px-6 py-3 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 border-2 border-gray-300 dark:border-gray-600 rounded-xl font-medium hover:border-blue-500 dark:hover:border-blue-400 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200 shadow-md hover:shadow-lg"
                  >
                    Clear Search
                  </button>
                  <Link
                    href="/companies/new"
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    Add New Client
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Client Credential Modal */}
      {selectedCompany && (
        <ClientCredentialModal
          isOpen={credentialModalOpen}
          onClose={() => setCredentialModalOpen(false)}
          companyId={selectedCompany.id}
          companyName={selectedCompany.name}
        />
      )}
    </div>
  )
}
