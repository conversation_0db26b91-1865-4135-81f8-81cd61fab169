'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import {
  ArrowLeft,
  Building,
  Briefcase,
  CheckCircle2,
  Clock,
  AlertCircle,
  FileText,
  Key,
  Share2,
  DollarSign,
  Calendar,
  Users
} from 'lucide-react'

import { formatDate, getStatusColor } from '../../../lib/utils'

interface Company {
  id: string
  name: string
  industry: string | null
  status: string | null
  address: string | null
  website: string | null
  notes: string | null
}

interface Project {
  id: string
  name: string
  description: string | null
  status: string | null
  start_date: string | null
  end_date: string | null
}

interface Task {
  id: string
  name: string
  status: string | null
  priority: string | null
  due_date: string | null
  project: {
    id: string
    name: string
  } | null
}

interface Credential {
  id: string
  name: string
  type: string | null
  username: string | null
  url: string | null
  notes: string | null
}

interface Invoice {
  id: string
  invoice_number: string
  amount: number | null
  status: string | null
  issue_date: string | null
  due_date: string | null
}

interface Contact {
  id: string
  name: string
  email: string | null
  phone: string | null
  position: string | null
}

export default function ClientPortal({ params }: { params: { id: string } }) {
  // Get the ID from params - use a state to avoid React warnings about sync params
  const [id] = useState(params.id);
  const [company, setCompany] = useState<Company | null>(null)
  const [projects, setProjects] = useState<Project[]>([])
  const [tasks, setTasks] = useState<Task[]>([])
  const [credentials, setCredentials] = useState<Credential[]>([])
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [contacts, setContacts] = useState<Contact[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchClientData() {
      setIsLoading(true)
      setError(null)

      // Log the ID for debugging
      console.log('Fetching client data for ID:', id)

      if (!id) {
        console.error('Invalid ID: ID is empty or undefined')
        setError('Invalid client ID. Please try again.')
        setIsLoading(false)
        return
      }

      try {
        // Fetch data from the API endpoint
        console.log('Fetching client data from API endpoint')
        const response = await fetch(`/api/client-portal/${id}`)

        if (!response.ok) {
          console.error('API response not OK:', response.status, response.statusText)
          const errorData = await response.json().catch(() => ({}))
          console.error('Error data:', errorData)
          throw new Error(errorData.error || `API error: ${response.status}`)
        }

        const data = await response.json()
        console.log('API response data:', data)

        // Set the data in state
        setCompany(data.company)
        setProjects(data.projects || [])
        setTasks(data.tasks || [])
        setCredentials(data.credentials || [])
        setInvoices(data.invoices || [])
        setContacts(data.contacts || [])

      } catch (err: any) {
        console.error('Error fetching client data:', err)
        // Log more detailed error information
        if (err.message) {
          console.error('Error message:', err.message)
        }
        if (err.code) {
          console.error('Error code:', err.code)
        }
        if (err.details) {
          console.error('Error details:', err.details)
        }
        if (err.hint) {
          console.error('Error hint:', err.hint)
        }
        setError('Failed to load client data. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    fetchClientData()
  }, [id])

  // Get task status color
  const getTaskStatusColor = (status: string | null) => {
    if (!status) return 'bg-gray-100 text-gray-800'

    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'blocked':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Get invoice status color
  const getInvoiceStatusColor = (status: string | null) => {
    if (!status) return 'bg-gray-100 text-gray-800'

    switch (status.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (error || !company) {
    console.log('Rendering error state:', { error, company })
    return (
      <div className="flex justify-center items-center h-full">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative max-w-md w-full" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error || 'Failed to load client data. Please try again.'}</span>
          <div className="mt-4">
            <Link href="/client-portals" className="text-red-700 underline">
              Return to client portals
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div>
      {/* Header with back button */}
      <div className="mb-6">
        <Link
          href="/client-portals"
          className="text-gray-500 hover:text-gray-700 inline-flex items-center"
        >
          <ArrowLeft size={16} className="mr-1" />
          Back to Client Portals
        </Link>
      </div>

      {/* Portal header */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-lg mr-4">
              <Building className="h-8 w-8 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{company.name} Portal</h1>
              <p className="text-gray-600">
                {company.industry && `${company.industry} • `}
                {company.status && (
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(company.status)}`}>
                    {company.status}
                  </span>
                )}
              </p>
            </div>
          </div>

          <div className="flex space-x-4">
            <Link
              href={`/companies/${company.id}`}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              View in CRM
            </Link>
            <Link
              href={`/client-portals/${company.id}/access`}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Manage Access
            </Link>
          </div>
        </div>
      </div>

      {/* Project Overview */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Briefcase className="h-5 w-5 mr-2 text-gray-500" />
          Project Overview
        </h2>

        {projects.length > 0 ? (
          <div className="space-y-4">
            {projects.map(project => (
              <div key={project.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <h3 className="font-medium text-gray-900">{project.name}</h3>
                  {project.status && (
                    <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                      {project.status}
                    </span>
                  )}
                </div>

                {project.description && (
                  <p className="mt-2 text-sm text-gray-600">{project.description}</p>
                )}

                <div className="mt-3 flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-1" />
                  {project.start_date ? (
                    <span>Started: {formatDate(project.start_date)}</span>
                  ) : (
                    <span>No start date</span>
                  )}

                  {project.expected_end_date && (
                    <span className="ml-4">
                      <Clock className="h-4 w-4 mr-1 inline" />
                      Due: {formatDate(project.expected_end_date)}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No active projects at the moment.</p>
        )}
      </div>

      {/* Active Tasks */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium text-gray-900 flex items-center">
            <CheckCircle2 className="h-5 w-5 mr-2 text-gray-500" />
            Active Tasks
          </h2>
        </div>

        {tasks.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Task
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Project
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Due Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {tasks.map(task => (
                  <tr key={task.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {task.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {task.project?.name || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getTaskStatusColor(task.status)}`}>
                        {task.status || 'Not Set'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {task.due_date ? formatDate(task.due_date) : '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-500">No active tasks at the moment.</p>
        )}
      </div>

      {/* Two column layout for Credentials and Invoices */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Project Credentials */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Key className="h-5 w-5 mr-2 text-gray-500" />
            Project Credentials
          </h2>

          {credentials.length > 0 ? (
            <div className="space-y-4">
              {credentials.map(credential => (
                <div key={credential.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <h3 className="font-medium text-gray-900">{credential.name}</h3>
                    {credential.type && (
                      <span className="bg-blue-100 text-blue-800 px-2.5 py-0.5 rounded-full text-xs font-medium">
                        {credential.type}
                      </span>
                    )}
                  </div>

                  {credential.url && (
                    <div className="mt-2 text-sm">
                      <a
                        href={credential.url.startsWith('http') ? credential.url : `https://${credential.url}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 flex items-center"
                      >
                        {credential.url}
                        <Share2 className="h-3 w-3 ml-1" />
                      </a>
                    </div>
                  )}

                  {credential.username && (
                    <div className="mt-1 text-sm text-gray-600">
                      <span className="font-medium">Username:</span> {credential.username}
                    </div>
                  )}

                  {credential.notes && (
                    <div className="mt-2 text-sm text-gray-600">
                      <p>{credential.notes}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">No credentials available.</p>
          )}
        </div>

        {/* Invoices & Expenses */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-gray-500" />
            Invoices & Expenses
          </h2>

          {invoices.length > 0 ? (
            <div className="space-y-4">
              {invoices.map(invoice => (
                <div key={invoice.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <h3 className="font-medium text-gray-900">Invoice #{invoice.invoice_number}</h3>
                    {invoice.status && (
                      <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getInvoiceStatusColor(invoice.status)}`}>
                        {invoice.status}
                      </span>
                    )}
                  </div>

                  <div className="mt-2 flex justify-between items-center">
                    <div className="text-sm text-gray-600">
                      {invoice.issue_date && (
                        <span>Issued: {formatDate(invoice.issue_date)}</span>
                      )}
                      {invoice.due_date && (
                        <span className="ml-4">Due: {formatDate(invoice.due_date)}</span>
                      )}
                    </div>

                    {invoice.amount !== null && (
                      <div className="text-lg font-bold text-gray-900 flex items-center">
                        <DollarSign className="h-4 w-4 mr-1" />
                        {invoice.amount.toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">No invoices available.</p>
          )}
        </div>
      </div>

      {/* Team Contacts */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Users className="h-5 w-5 mr-2 text-gray-500" />
          Team Contacts
        </h2>

        {contacts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {contacts.map(contact => (
              <div key={contact.id} className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-medium text-gray-900">{contact.name}</h3>
                {contact.position && (
                  <p className="text-sm text-gray-600">{contact.position}</p>
                )}

                <div className="mt-3 space-y-1">
                  {contact.email && (
                    <div className="text-sm">
                      <a
                        href={`mailto:${contact.email}`}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {contact.email}
                      </a>
                    </div>
                  )}

                  {contact.phone && (
                    <div className="text-sm">
                      <a
                        href={`tel:${contact.phone}`}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {contact.phone}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No contacts available.</p>
        )}
      </div>
    </div>
  )
}
