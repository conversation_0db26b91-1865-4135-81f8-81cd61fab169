'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  ArrowLeft,
  Building,
  Key,
  Plus,
  Trash2,
  RefreshCw,
  Copy,
  Eye,
  EyeOff,
  Check,
  AlertCircle
} from 'lucide-react'
import { createBrowserClient } from '@supabase/ssr'

interface Company {
  id: string
  name: string
}

interface ClientUser {
  id: string
  email: string
  created_at: string
  last_login: string | null
  is_active: boolean
}

export default function ClientPortalAccess({ params }: { params: { id: string } }) {
  // Get the ID from params
  const id = params?.id;
  const router = useRouter()
  const [company, setCompany] = useState<Company | null>(null)
  const [clientUsers, setClientUsers] = useState<ClientUser[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Form state
  const [newEmail, setNewEmail] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formError, setFormError] = useState<string | null>(null)
  const [formSuccess, setFormSuccess] = useState<string | null>(null)
  const [copiedId, setCopiedId] = useState<string | null>(null)

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true)
      setError(null)

      // Log the ID for debugging
      console.log('Client Portal Access - Fetching data for ID:', id)

      if (!id) {
        console.error('Invalid ID: ID is empty or undefined')
        setError('Invalid client ID. Please try again.')
        setIsLoading(false)
        return
      }

      try {
        // Create a Supabase client
        const supabase = createBrowserClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
        )

        // Fetch company details
        console.log('Access Page - Fetching company data for ID:', id)
        const { data: companyData, error: companyError } = await supabase
          .from('companies')
          .select('id, name')
          .eq('id', id)
          .single()

        console.log('Access Page - Company data response:', { data: companyData, error: companyError })

        if (companyError) {
          console.error('Access Page - Company fetch error:', companyError)
          throw companyError
        }

        if (!companyData) {
          console.error('Access Page - No company data found for ID:', id)
          throw new Error('Company not found')
        }

        console.log('Access Page - Setting company data:', companyData)
        setCompany(companyData)

        // Fetch client users
        const { data: userData, error: userError } = await supabase
          .from('client_users')
          .select('id, email, created_at, last_login, is_active')
          .eq('company_id', id)
          .order('created_at', { ascending: false })

        if (userError) throw userError

        setClientUsers(userData || [])
      } catch (err: any) {
        console.error('Error fetching data:', err)
        // Log more detailed error information
        if (err.message) {
          console.error('Error message:', err.message)
        }
        if (err.code) {
          console.error('Error code:', err.code)
        }
        if (err.details) {
          console.error('Error details:', err.details)
        }
        if (err.hint) {
          console.error('Error hint:', err.hint)
        }
        setError('Failed to load data. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [id])

  // Generate a random password
  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
    let password = ''
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    setNewPassword(password)
  }

  // Copy password to clipboard
  const copyToClipboard = (text: string, id: string) => {
    navigator.clipboard.writeText(text)
    setCopiedId(id)
    setTimeout(() => setCopiedId(null), 2000)
  }

  // Create new client user
  const createClientUser = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormError(null)
    setFormSuccess(null)
    setIsSubmitting(true)

    if (!newEmail || !newPassword) {
      setFormError('Email and password are required')
      setIsSubmitting(false)
      return
    }

    try {
      // Create a Supabase client
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )

      // Check if email already exists
      const { data: existingUser, error: checkError } = await supabase
        .from('client_users')
        .select('id')
        .eq('email', newEmail)
        .maybeSingle()

      if (checkError) throw checkError

      if (existingUser) {
        setFormError('A user with this email already exists')
        setIsSubmitting(false)
        return
      }

      // Create new client user
      const { data, error } = await supabase.rpc('create_client_user', {
        p_email: newEmail,
        p_password: newPassword,
        p_company_id: id
      })

      if (error) throw error

      // Refresh client users list
      const { data: updatedUsers, error: refreshError } = await supabase
        .from('client_users')
        .select('id, email, created_at, last_login, is_active')
        .eq('company_id', id)
        .order('created_at', { ascending: false })

      if (refreshError) throw refreshError

      setClientUsers(updatedUsers || [])
      setFormSuccess('Client user created successfully')
      setNewEmail('')
      setNewPassword('')
    } catch (err: any) {
      console.error('Error creating client user:', err)
      setFormError(err.message || 'Failed to create client user')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Reset password for a client user
  const resetPassword = async (userId: string) => {
    if (!confirm('Are you sure you want to reset this user\'s password?')) {
      return
    }

    setIsSubmitting(true)

    try {
      // Create a Supabase client
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )

      const newPassword = Math.random().toString(36).slice(-10) + Math.random().toString(36).slice(-10)

      // Update password
      const { error } = await supabase.rpc('reset_client_password', {
        p_user_id: userId,
        p_new_password: newPassword
      })

      if (error) throw error

      alert(`Password reset successfully. New password: ${newPassword}`)
    } catch (err: any) {
      console.error('Error resetting password:', err)
      alert('Failed to reset password: ' + (err.message || 'Unknown error'))
    } finally {
      setIsSubmitting(false)
    }
  }

  // Toggle user active status
  const toggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      // Create a Supabase client
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )

      const { error } = await supabase
        .from('client_users')
        .update({ is_active: !currentStatus })
        .eq('id', userId)

      if (error) throw error

      // Update local state
      setClientUsers(clientUsers.map(user =>
        user.id === userId ? { ...user, is_active: !currentStatus } : user
      ))
    } catch (err: any) {
      console.error('Error toggling user status:', err)
      alert('Failed to update user status: ' + (err.message || 'Unknown error'))
    }
  }

  // Delete a client user
  const deleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      return
    }

    try {
      // Create a Supabase client
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )

      const { error } = await supabase
        .from('client_users')
        .delete()
        .eq('id', userId)

      if (error) throw error

      // Update local state
      setClientUsers(clientUsers.filter(user => user.id !== userId))
    } catch (err: any) {
      console.error('Error deleting user:', err)
      alert('Failed to delete user: ' + (err.message || 'Unknown error'))
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (error || !company) {
    console.log('Access Page - Rendering error state:', { error, company })
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline"> {error || 'Company not found'}</span>
        <div className="mt-4">
          <Link href="/client-portals" className="text-red-700 underline">
            Return to client portals
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div>
      {/* Header with back button */}
      <div className="mb-6 max-w-4xl mx-auto">
        <Link
          href={`/client-portals/${id}`}
          className="text-gray-500 hover:text-gray-700 inline-flex items-center"
        >
          <ArrowLeft size={16} className="mr-1" />
          Back to Client Portal
        </Link>
      </div>

      {/* Page header */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6 max-w-4xl mx-auto">
        <div className="flex items-center">
          <div className="bg-blue-100 p-3 rounded-lg mr-4">
            <Building className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{company.name} - Portal Access</h1>
            <p className="text-gray-600">
              Manage client portal access credentials
            </p>
          </div>
        </div>
      </div>

      {/* Create new client user */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6 max-w-4xl mx-auto">
        <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Key className="h-5 w-5 mr-2 text-gray-500" />
          Create Client Portal Access
        </h2>

        <form onSubmit={createClientUser} className="space-y-4">
          {formError && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative flex items-start">
              <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
              <span>{formError}</span>
            </div>
          )}

          {formSuccess && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative flex items-start">
              <Check className="h-5 w-5 mr-2 mt-0.5" />
              <span>{formSuccess}</span>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={newEmail}
                onChange={(e) => setNewEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 pr-24"
                  placeholder="Enter password"
                  required
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2 max-w-2xl mx-auto">
            <button
              type="button"
              onClick={generatePassword}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <RefreshCw size={16} className="mr-2" />
              Generate Password
            </button>

            {newPassword && (
              <button
                type="button"
                onClick={() => copyToClipboard(newPassword, 'new-password')}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                {copiedId === 'new-password' ? (
                  <>
                    <Check size={16} className="mr-2" />
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy size={16} className="mr-2" />
                    Copy
                  </>
                )}
              </button>
            )}
          </div>

          <div className="pt-2 flex justify-center">
            <button
              type="submit"
              disabled={isSubmitting}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <RefreshCw size={16} className="mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus size={16} className="mr-2" />
                  Create Access
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Client users list */}
      <div className="bg-white rounded-lg shadow-sm p-6 max-w-4xl mx-auto">
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          Client Portal Users
        </h2>

        {clientUsers.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Login
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {clientUsers.map(user => (
                  <tr key={user.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {user.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(user.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.last_login
                        ? new Date(user.last_login).toLocaleString()
                        : 'Never logged in'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        user.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {user.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => toggleUserStatus(user.id, user.is_active)}
                          className={`text-xs px-2 py-1 rounded ${
                            user.is_active
                              ? 'bg-red-50 text-red-700 hover:bg-red-100'
                              : 'bg-green-50 text-green-700 hover:bg-green-100'
                          }`}
                        >
                          {user.is_active ? 'Deactivate' : 'Activate'}
                        </button>
                        <button
                          onClick={() => resetPassword(user.id)}
                          className="bg-blue-50 text-blue-700 hover:bg-blue-100 text-xs px-2 py-1 rounded"
                        >
                          Reset Password
                        </button>
                        <button
                          onClick={() => deleteUser(user.id)}
                          className="bg-red-50 text-red-700 hover:bg-red-100 text-xs px-2 py-1 rounded"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">No client users found. Create one above to get started.</p>
          </div>
        )}
      </div>
    </div>
  )
}
