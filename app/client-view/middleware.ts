import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function middleware(request: NextRequest) {
  // Get the session token from cookies
  const sessionToken = request.cookies.get('client_session')?.value

  if (!sessionToken) {
    // No session token, redirect to login
    return NextResponse.redirect(new URL('/client-login', request.url))
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY as string

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables')
      return NextResponse.redirect(new URL('/client-login', request.url))
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Verify the session
    const { data: sessionData, error: sessionError } = await supabase
      .from('client_sessions')
      .select('company_id, user_id, expires_at')
      .eq('token', sessionToken)
      .single()

    if (sessionError || !sessionData) {
      console.error('Session verification error:', sessionError)
      return NextResponse.redirect(new URL('/client-login', request.url))
    }

    // Check if session is expired
    if (new Date(sessionData.expires_at) < new Date()) {
      // Delete the expired session
      await supabase
        .from('client_sessions')
        .delete()
        .eq('token', sessionToken)

      // Redirect to login
      return NextResponse.redirect(new URL('/client-login', request.url))
    }

    // Get the company ID from the URL
    const url = new URL(request.url)
    const path = url.pathname
    const companyId = path.split('/')[2] // /client-view/:id

    // Check if the requested company matches the session company
    if (sessionData.company_id !== companyId) {
      return NextResponse.redirect(new URL('/client-login', request.url))
    }

    // Session is valid, allow access
    return NextResponse.next()
  } catch (err) {
    console.error('Unexpected error in client view middleware:', err)
    return NextResponse.redirect(new URL('/client-login', request.url))
  }
}

export const config = {
  matcher: ['/client-view/:id*'],
}
