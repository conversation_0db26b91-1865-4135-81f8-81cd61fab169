'use client'

import React, { useEffect, useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import {
  FileText,
  Download,
  ExternalLink,
  Search,
  FolderOpen,
  File,
  FileImage,
  FileArchive,
  FilePdf,
  FileCode,
  Plus,
  Filter,
  ChevronDown,
  ChevronRight,
  AlertCircle
} from 'lucide-react'
import { cn } from '../../../../lib/utils'
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

interface ResourcesPageProps {
  params: {
    id: string
  }
}

interface Resource {
  id: string
  name: string
  type: string
  size: string
  lastModified: string
  url: string
  description?: string
}

// Sample resource data - in a real app, this would come from an API
const sampleResources: Resource[] = [
  {
    id: '1',
    name: 'Project Proposal',
    type: 'pdf',
    size: '2.4 MB',
    lastModified: '2024-07-01',
    url: '#',
    description: 'Initial project proposal document outlining scope and deliverables'
  },
  {
    id: '2',
    name: 'Brand Guidelines',
    type: 'pdf',
    size: '5.1 MB',
    lastModified: '2024-06-15',
    url: '#',
    description: 'Official brand guidelines including logo usage, colors, and typography'
  },
  {
    id: '3',
    name: 'Website Mockups',
    type: 'image',
    size: '8.7 MB',
    lastModified: '2024-06-28',
    url: '#',
    description: 'Design mockups for the website homepage and key landing pages'
  },
  {
    id: '4',
    name: 'Technical Requirements',
    type: 'docx',
    size: '1.2 MB',
    lastModified: '2024-06-10',
    url: '#',
    description: 'Detailed technical requirements and specifications document'
  },
  {
    id: '5',
    name: 'Project Timeline',
    type: 'xlsx',
    size: '0.8 MB',
    lastModified: '2024-07-02',
    url: '#',
    description: 'Project timeline with milestones and delivery dates'
  }
]

// Function to get the appropriate icon based on file type
const getFileIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'pdf':
      return <FilePdf className="h-5 w-5 text-red-500" />
    case 'image':
    case 'png':
    case 'jpg':
    case 'jpeg':
      return <FileImage className="h-5 w-5 text-blue-500" />
    case 'zip':
    case 'rar':
      return <FileArchive className="h-5 w-5 text-yellow-500" />
    case 'docx':
    case 'doc':
      return <FileText className="h-5 w-5 text-blue-600" />
    case 'xlsx':
    case 'xls':
      return <FileText className="h-5 w-5 text-green-600" />
    case 'html':
    case 'css':
    case 'js':
      return <FileCode className="h-5 w-5 text-purple-500" />
    default:
      return <File className="h-5 w-5 text-gray-500" />
  }
}

const ResourcesPage: React.FC<ResourcesPageProps> = ({ params }) => {
  const router = useRouter()
  const [resources, setResources] = useState<Resource[]>(sampleResources)
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Filter resources based on search query
  const filteredResources = resources.filter(resource => 
    resource.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    resource.description?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-background relative">
      {/* Background pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none"></div>
      
      <div className="py-8 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto relative">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-foreground bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">Resources</h1>
          <p className="text-muted-foreground mt-1">
            Access project documents, files, and resources
          </p>
        </div>

        {/* Search and filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input 
              placeholder="Search resources..." 
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filter
            <ChevronDown className="h-4 w-4 ml-1" />
          </Button>
        </div>

        {/* Resources list */}
        <div className="card overflow-hidden">
          <div className="border-b border-border p-6 bg-gradient-to-r from-primary/5 to-transparent">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="p-2 bg-primary/10 rounded-lg mr-3 shadow-sm">
                  <FolderOpen className="h-5 w-5 text-primary" />
                </div>
                <h2 className="text-xl font-semibold">Project Resources</h2>
              </div>
              <div className="flex items-center gap-2 px-3 py-1 bg-white/80 rounded-full shadow-sm">
                <span className="text-sm text-muted-foreground">{filteredResources.length} {filteredResources.length === 1 ? 'File' : 'Files'}</span>
              </div>
            </div>
          </div>

          {filteredResources.length > 0 ? (
            <div className="divide-y divide-border">
              {filteredResources.map(resource => (
                <div key={resource.id} className="p-4 hover:bg-muted/30 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start">
                      <div className="p-2 bg-muted rounded-md mr-4">
                        {getFileIcon(resource.type)}
                      </div>
                      <div>
                        <h3 className="text-base font-medium flex items-center">
                          {resource.name}
                          <span className="ml-2 text-xs px-2 py-0.5 bg-muted rounded-full text-muted-foreground uppercase">
                            {resource.type}
                          </span>
                        </h3>
                        {resource.description && (
                          <p className="text-sm text-muted-foreground mt-1">{resource.description}</p>
                        )}
                        <div className="flex items-center mt-2 text-xs text-muted-foreground">
                          <span>{resource.size}</span>
                          <span className="mx-2">•</span>
                          <span>Last updated: {resource.lastModified}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0" title="Download">
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0" title="Open">
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-8 text-center">
              <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-4">
                <AlertCircle className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-1">No resources found</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery ? 'No resources match your search criteria.' : 'There are no resources available for this project yet.'}
              </p>
              {searchQuery && (
                <Button variant="outline" onClick={() => setSearchQuery('')}>
                  Clear Search
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ResourcesPage
