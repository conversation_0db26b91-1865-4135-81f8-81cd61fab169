'use client'

import React from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import {
  Briefcase,
  CheckCircle2,
  Clock,
  AlertCircle,
  FileText,
  Key,
  Calendar,
  Users,
  LogOut,
  MessageSquare,
  CircleDot,
  ExternalLink,
  Home,
  Receipt,
  DollarSign,
  LayoutGrid,
  List
} from 'lucide-react'
import { formatDate, cn } from '../../../lib/utils'
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import ClientCredentialModal from '../../components/ClientCredentialModal';
import SimpleCredentialModal from '../../components/SimpleCredentialModal';
import CredentialsTableView from '../../components/CredentialsTableView';
import { Credential } from '../../../lib/supabase/types';
import { useState, useCallback, useEffect } from 'react'

interface Company {
  id: string
  name: string
  industry: string | null
  status: string | null
  address: string | null
  website: string | null
  notes: string | null
}

interface Project {
  id: string
  name: string
  description: string | null
  status: string | null
  start_date: string | null
  end_date: string | null
}

interface Task {
  id: string
  name: string
  status: string | null
  priority: string | null
  due_date: string | null
  project: {
    id: string
    name: string
  } | null
}

interface Invoice {
  id: string
  invoice_number: string
  amount: number | null
  status: string | null
  issue_date: string | null
  due_date: string | null
}

interface Expense {
  id: string
  name: string
  amount: number
  category: string | null
  date: string | null
  payment_method: string | null
  is_reimbursable: boolean | null
  reimbursement_status: string | null
  project_id: string | null
  receipt_url: string | null
  projects?: { name: string } | null
}

interface Contact {
  id: string;
  name: string | null;
  email: string | null;
  phone: string | null;
  position?: string | null;
}

interface ClientMessage {
  id: string;
  subject?: string;
  body: string;
  isFromClient: boolean;
  createdAt: string;
  replies?: ClientMessage[];
}

interface ClientPortalViewProps {
  params: Promise<{ id: string }>
}

const ClientPortalView: React.FC<ClientPortalViewProps> = ({ params }) => {
  const resolvedParams = React.use(params)
  const router = useRouter()
  const [company, setCompany] = useState<Company | null>(null)
  const [projects, setProjects] = useState<Project[]>([])
  const [tasks, setTasks] = useState<Task[]>([])
  const [credentials, setCredentials] = useState<Credential[]>([])
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [expenses, setExpenses] = useState<Expense[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [messageSubject, setMessageSubject] = useState('');
  const [messageBody, setMessageBody] = useState('');
  const [clientMessages, setClientMessages] = useState<ClientMessage[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [isSubmittingMessage, setIsSubmittingMessage] = useState(false);
  const [messageError, setMessageError] = useState<string | null>(null);
  const [messageSuccess, setMessageSuccess] = useState<string | null>(null);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCredential, setSelectedCredential] = useState<Credential | null>(null);
  const [credentialsViewMode, setCredentialsViewMode] = useState<'cards' | 'table'>('cards');

  const fetchClientMessages = useCallback(async () => {
    if (!resolvedParams.id) {
        console.warn("fetchClientMessages called without resolvedParams.id; this might be okay if API relies purely on session.");
    }
    setIsLoadingMessages(true);
    setMessageError(null);
    try {
      const response = await fetch('/api/client-messages');
      if (!response.ok) {
        if (response.status === 401) {
          console.error('Unauthorized during fetchClientMessages. Redirecting to login.');
          setMessageError('Your session may have expired. Please try refreshing or log in again.')
          router.push('/client-login');
        } else {
          const errorData = await response.json();
          const errMsg = `Failed to load messages: ${errorData.error || response.statusText}`;
          console.error(errMsg);
          setMessageError(errMsg);
        }
        setClientMessages([]);
        return;
      }
      const messagesData = await response.json();
      setClientMessages(messagesData || []);
    } catch (err) {
      console.error("Failed to fetch messages:", err);
      setMessageError('An unexpected error occurred while fetching messages.');
      setClientMessages([]);
    } finally {
      setIsLoadingMessages(false);
    }
  }, [resolvedParams.id, router]);

  useEffect(() => {
    async function fetchClientData() {
      if (!resolvedParams.id) {
        console.error('Company ID is missing from params');
        setError('Failed to load client data: Missing company ID.');
        setLoading(false);
        return;
      }
      try {
        const response = await fetch(
          `/api/client-portal/data?companyId=${resolvedParams.id}`
        );
        if (!response.ok) {
          if (response.status === 401) {
            console.error('Unauthorized during fetchClientData. Redirecting to login.');
            setError('Unauthorized. Please log in again.');
            router.push('/client-login');
          } else {
            const errorData = await response.json();
            const errorMessage = `Failed to load client data: ${errorData.error || response.statusText}`;
            console.error(errorMessage);
            setError(errorMessage);
          }
          return;
        }
        const data = await response.json();
        setCompany(data.company);
        setProjects(data.projects || []);
        setTasks(data.tasks || []);
        if (data.credentials) {
          // Store credentials initially
          setCredentials(data.credentials);

          // Decrypt credentials in the background for table view
          const decryptAllCredentials = async () => {
            try {
              const { getDecryptedCredential } = await import('../../../lib/security');
              const decryptedCredentials = await Promise.all(
                data.credentials.map(async (cred: Credential) => {
                  try {
                    const decryptedCred = await getDecryptedCredential(cred.id);
                    if (decryptedCred) {
                      return {
                        ...cred,
                        password_decrypted: decryptedCred.password
                      };
                    }
                    return cred;
                  } catch (err) {
                    console.error(`Error decrypting credential ${cred.id}:`, err);
                    return cred;
                  }
                })
              );
              setCredentials(decryptedCredentials);
            } catch (err) {
              console.error('Error in decryptAllCredentials:', err);
            }
          };

          // Start decryption process
          decryptAllCredentials();
        }
        setInvoices(data.invoices || []);
        if (data.contacts) {
          setContacts(data.contacts);
        }
        if (data.expenses) {
          setExpenses(data.expenses);
        }
      } catch (fetchError) {
        console.error('Error fetching client data:', fetchError);
        setError('An unexpected error occurred while fetching client data.');
      }
    }

    async function loadAllData() {
      setLoading(true);
      setError(null);
      await Promise.all([
        fetchClientData(),
        fetchClientMessages()
      ]);
      setLoading(false);
    }

    if (resolvedParams.id) {
        loadAllData();
    }

  }, [resolvedParams.id, router, fetchClientMessages]);

  const displayProjectsLength = projects.length;

  const handleLogout = async () => {
    try {
      await fetch('/api/client-auth/logout', {
        method: 'POST',
      })
      router.push('/client-login')
    } catch (err) {
      console.error('Logout error:', err)
    }
  }

  const getStatusBadgeClass = (status: string | null): "default" | "destructive" | "outline" | "secondary" => {
    if (!status) return 'secondary'
    switch (status.toLowerCase()) {
      case 'active':
      case 'completed':
      case 'paid':
        return 'default' // Often green by default or can be styled as success
      case 'inactive':
      case 'blocked':
      case 'overdue':
        return 'destructive'
      case 'lead':
      case 'pending':
      case 'on hold': // Added for project status mapping
        return 'outline' // Often yellow/amber or can be styled as warning
      case 'prospect':
        return 'default' // Or 'secondary' depending on desired color for primary-like status
      default:
        return 'secondary'
    }
  }

  const handleMessageSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!messageBody.trim()) {
      setMessageError('Message body cannot be empty.');
      setTimeout(() => setMessageError(null), 3000);
      return;
    }
    setIsSubmittingMessage(true);
    setMessageError(null);
    setMessageSuccess(null);

    try {
      const response = await fetch('/api/client-messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject: messageSubject.trim() || null,
          messageBody: messageBody.trim(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        const errMsg = `Failed to send message: ${errorData.error || response.statusText}`;
        console.error(errMsg);
        setMessageError(errMsg);
        if (response.status === 401) {
            router.push('/client-login');
        }
        return;
      }
      setMessageSuccess('Message sent successfully!');
      setMessageSubject('');
      setMessageBody('');
      await fetchClientMessages();
      setTimeout(() => setMessageSuccess(null), 3000);
    } catch (err) {
      console.error('Error submitting message:', err);
      setMessageError('An unexpected error occurred while sending your message.');
    } finally {
      setIsSubmittingMessage(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <p className="ml-4 text-lg text-muted-foreground">Loading Client Portal...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-background p-8 text-center">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <h2 className="text-2xl font-semibold text-destructive mb-2">Access Denied or Error</h2>
        <p className="text-muted-foreground mb-6">{error}</p>
        <Button onClick={() => router.push('/client-login')} className="btn btn-primary">
          Go to Login
        </Button>
      </div>
    );
  }

  if (!company) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <p className="ml-4 text-lg text-muted-foreground">Company data not found.</p>
      </div>
    );
  }

  const activeSection = 'dashboard';

  // Function to fetch and decrypt credential password
  const fetchDecryptedCredential = async (credential: Credential): Promise<Credential> => {
    try {
      // Import the getDecryptedCredential function
      const { getDecryptedCredential } = await import('../../../lib/security');

      // Fetch the decrypted credential
      const decryptedCredential = await getDecryptedCredential(credential.id);
      console.log('Decrypted credential result:', decryptedCredential);

      if (decryptedCredential) {
        // Return credential with password_decrypted property
        return {
          ...credential,
          password_decrypted: decryptedCredential.password
        };
      }
    } catch (error) {
      console.error('Error fetching decrypted credential:', error);
    }

    // Return original credential if decryption fails
    return credential;
  };

  // Function to handle credential view in modal
  const handleViewCredential = async (credential: Credential) => {
    console.log('View credential clicked:', credential);
    try {
      setLoading(true);

      // Fetch decrypted credential
      const credentialWithPassword = await fetchDecryptedCredential(credential);
      console.log('Setting credential with password:', credentialWithPassword);

      // Set the credential and open modal
      setSelectedCredential(credentialWithPassword);
      setIsModalOpen(true);
    } catch (error) {
      console.error('Error in handleViewCredential:', error);
      // Still show the modal with the basic credential info
      setSelectedCredential(credential);
      setIsModalOpen(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-muted/40">
      <div className="flex flex-1 overflow-hidden">
        <aside className="fixed top-0 left-0 z-30 hidden h-full pt-16 bg-white dark:bg-slate-950 border-r border-slate-200 dark:border-slate-800 shadow-sm md:block w-64 shrink-0 transition-transform duration-300 ease-in-out md:translate-x-0">
          <div className="flex flex-col h-full">
            <ScrollArea className="flex-1 p-4">
              <div className="text-center border-b border-slate-200 dark:border-slate-800 pb-5 mb-5">
                {company && (
                  <Link href={`/client-view/${company.id}`} className="block">
                    <div className="w-20 h-20 mx-auto mb-3 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 text-3xl font-bold border border-blue-200 dark:border-blue-800/50 shadow-sm">
                      {company.name.charAt(0).toUpperCase()}
                    </div>
                    <h2 className="text-xl font-semibold text-slate-800 dark:text-white">{company.name}</h2>
                  </Link>
                )}
                <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">Client Portal</p>
              </div>

              <nav className="space-y-1">
                {[
                  { href: "#dashboard", icon: Home, label: "Dashboard" },
                  { href: "#projects", icon: Briefcase, label: "Projects" },
                  { href: "#tasks", icon: CheckCircle2, label: "Tasks" },
                  { href: "#expenses", icon: Receipt, label: "Expenses" },
                  { href: "#credentials", icon: Key, label: "Credentials" },
                  { href: "#invoices", icon: FileText, label: "Invoices & Payments" },
                  { href: "#contacts", icon: Users, label: "Contacts" },
                  { href: "#support", icon: MessageSquare, label: "Support" },
                ].map((item) => (
                  <a
                    key={item.label}
                    href={item.href}
                    className={cn(
                      "group flex items-center rounded-md px-3 py-2.5 text-sm font-medium transition-colors",
                      activeSection === item.label.toLowerCase()
                        ? "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 font-medium shadow-sm border border-blue-100 dark:border-blue-800/30"
                        : "text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-800/50 hover:text-slate-900 dark:hover:text-slate-200"
                    )}
                  >
                    <item.icon className="mr-3 h-4.5 w-4.5" />
                    {item.label}
                  </a>
                ))}
              </nav>
            </ScrollArea>
            <div className="p-4 mt-auto border-t border-slate-200 dark:border-slate-800">
              <Button
                variant="ghost"
                onClick={handleLogout}
                className="w-full justify-start text-slate-600 dark:text-slate-400 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400"
              >
                <LogOut className="mr-3 h-4 w-4" />
                Logout
              </Button>
            </div>
          </div>
        </aside>

        <main className="flex-1 md:ml-64 pt-16 overflow-x-hidden">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 pb-4 max-w-full overflow-x-hidden">

            <div id="dashboard" className="bg-white dark:bg-slate-900 rounded-lg shadow-sm border border-slate-200 dark:border-slate-800 p-6 mb-6">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
                <div>
                  <div className="flex items-center mb-2">
                    <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3 border border-blue-200 dark:border-blue-800/50 shadow-sm">
                      {company?.name ? company.name.charAt(0).toUpperCase() : 'C'}
                    </div>
                    <h1 className="text-2xl font-bold tracking-tight text-slate-900 dark:text-white">
                      Welcome back, {company?.name || 'Client'}!
                    </h1>
                  </div>
                  <p className="text-slate-600 dark:text-slate-400 ml-[52px]">Here&apos;s an overview of your account and ongoing activities.</p>
                </div>
                <div className="mt-4 sm:mt-0 flex gap-2">
                  <button onClick={() => router.push(`/client-view/${resolvedParams.id}/messages/new`)}
                    className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium
                    transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-blue-400
                    disabled:pointer-events-none disabled:opacity-50 bg-blue-600 hover:bg-blue-700 text-white shadow-sm h-9 px-4 py-2">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    <span>New Message</span>
                  </button>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
                {[
                  { label: 'Active Projects', value: projects.filter(p => p.status?.toLowerCase() === 'active' || p.status?.toLowerCase() === 'in progress').length, icon: Briefcase, color: 'bg-blue-100 dark:bg-blue-900/30', textColor: 'text-blue-600 dark:text-blue-400', borderColor: 'border-blue-200 dark:border-blue-800/50' },
                  { label: 'Pending Tasks', value: tasks.filter(t => t.status?.toLowerCase() === 'pending' || t.status?.toLowerCase() === 'to do').length, icon: CheckCircle2, color: 'bg-emerald-100 dark:bg-emerald-900/30', textColor: 'text-emerald-600 dark:text-emerald-400', borderColor: 'border-emerald-200 dark:border-emerald-800/50' },
                  { label: 'Overdue Invoices', value: invoices.filter(i => i.status?.toLowerCase() === 'overdue').length, icon: AlertCircle, isAlert: true, color: 'bg-red-100 dark:bg-red-900/30', textColor: 'text-red-600 dark:text-red-400', borderColor: 'border-red-200 dark:border-red-800/50' },
                  { label: 'Expenses', value: expenses.length, icon: Receipt, color: 'bg-amber-100 dark:bg-amber-900/30', textColor: 'text-amber-600 dark:text-amber-400', borderColor: 'border-amber-200 dark:border-amber-800/50' },
                ].map((stat) => (
                  <div key={stat.label} className={cn(
                    "p-4 rounded-lg border flex items-center hover:shadow-sm transition-all",
                    stat.isAlert
                      ? "bg-white dark:bg-slate-900 border-red-200 dark:border-red-800/50"
                      : "bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-800"
                  )}>
                    <div className={cn(
                      "p-2.5 rounded-full mr-4 shadow-sm border",
                      stat.color,
                      stat.textColor,
                      stat.borderColor
                    )}>
                      <stat.icon className="h-5 w-5" />
                    </div>
                    <div>
                      <p className="text-xs text-slate-500 dark:text-slate-400 uppercase tracking-wider font-medium">{stat.label}</p>
                      <p className={cn(
                        "text-2xl font-semibold",
                        stat.isAlert ? "text-red-600 dark:text-red-400" : "text-slate-900 dark:text-white"
                      )}>{stat.value}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div id="projects" className="bg-white p-6 rounded-lg shadow-md mb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Projects ({displayProjectsLength})</h3>
                {projects.length > 3 && (
                  <Link href={`/client-view/${resolvedParams.id}/projects`} className="text-sm font-medium text-primary hover:underline">
                    View All Projects
                  </Link>
                )}
              </div>
              {displayProjectsLength > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 overflow-hidden">
                  {projects.slice(0, 3).map((project) => (
                    <div
                      key={project.id}
                      className="bg-white border border-border rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 cursor-pointer overflow-hidden flex flex-col"
                      onClick={() => {
                        router.push(
                          `/client-view/${resolvedParams.id}/projects/${project.id}`
                        );
                      }}
                    >
                      <CardContent className="p-6 flex-grow overflow-hidden">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="text-lg font-semibold text-foreground hover:text-primary transition-colors truncate mr-2">{project.name}</h3>
                          <Badge variant={getStatusBadgeClass(project.status)} className={cn(getStatusBadgeClass(project.status))}>
                            {project.status || 'N/A'}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                          {project.description || 'No description available.'}
                        </p>
                        <Separator className="my-3" />
                        <div className="space-y-2 text-xs text-muted-foreground">
                          {project.start_date && (
                            <div className="flex items-center">
                              <Calendar className="h-3.5 w-3.5 mr-1.5 opacity-70" />
                              <span>Start: {formatDate(project.start_date)}</span>
                            </div>
                          )}
                          {project.end_date && (
                            <div className="flex items-center">
                              <Clock className="h-3.5 w-3.5 mr-1.5 opacity-70" />
                              <span>End: {formatDate(project.end_date)}</span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-10 bg-card border border-border rounded-xl shadow-sm">
                  <Briefcase className="h-12 w-12 text-muted-foreground/50 mx-auto mb-3" />
                  <p className="text-muted-foreground">No projects found for this client yet.</p>
                </div>
              )}
            </div>

            <div id="tasks" className="bg-white p-6 rounded-lg shadow-md mb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Key Tasks ({tasks.length})</h3>
                {tasks.length > 5 && (
                  <Link href={`/client-view/${resolvedParams.id}/tasks`} className="text-sm font-medium text-primary hover:underline">
                    View All Tasks
                  </Link>
                )}
              </div>
              {tasks.length > 0 ? (
                <div className="overflow-auto max-w-full">
                  <ul className="divide-y divide-border mt-4">
                    {tasks.slice(0,5).map((task) => (
                      <li key={task.id} className="p-4 bg-slate-50 rounded-lg border">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <CircleDot className={cn("h-4 w-4 mr-3", task.status?.toLowerCase() === 'completed' ? "text-green-500" : task.priority?.toLowerCase() === 'high' ? "text-red-500" : "text-muted-foreground/70")} />
                            <div>
                              <span className="font-medium text-gray-800">{task.name}</span>
                              {task.project && <span className="text-xs text-muted-foreground ml-2">({task.project.name})</span>}
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            {task.due_date && <Badge variant="outline" className="text-xs font-normal">Due: {formatDate(task.due_date)}</Badge>}
                            <Badge variant={getStatusBadgeClass(task.status)} className={cn("capitalize", getStatusBadgeClass(task.status))}>{task.status || 'N/A'}</Badge>
                            <Link href={`/client-view/${resolvedParams.id}/tasks/${task.id}`} className="text-primary hover:underline">
                              <ExternalLink className="h-4 w-4 opacity-70 hover:opacity-100" />
                            </Link>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <div className="text-center py-10 bg-card border border-border rounded-xl shadow-sm">
                  <CheckCircle2 className="h-12 w-12 text-muted-foreground/50 mx-auto mb-3" />
                  <p className="text-muted-foreground">No tasks assigned yet.</p>
                </div>
              )}
            </div>

            <div id="expenses" className="bg-white p-6 rounded-lg shadow-md mb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Expenses ({expenses.length})</h3>
              </div>
              {expenses.length > 0 ? (
                <div className="overflow-auto max-w-full">
                  <table className="w-full border-collapse border border-slate-200">
                    <thead>
                      <tr className="bg-slate-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <th className="px-4 py-3 border-b border-slate-200">Name</th>
                        <th className="px-4 py-3 border-b border-slate-200">Date</th>
                        <th className="px-4 py-3 border-b border-slate-200">Amount</th>
                        <th className="px-4 py-3 border-b border-slate-200">Category</th>
                        <th className="px-4 py-3 border-b border-slate-200">Project</th>
                        <th className="px-4 py-3 border-b border-slate-200">Payment</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-slate-200 bg-white">
                      {expenses.map((expense) => (
                        <tr key={expense.id} className="hover:bg-slate-50 transition-colors">
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                            <div className="flex items-center">
                              <DollarSign className="h-4 w-4 text-gray-400 mr-2" />
                              {expense.name}
                            </div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {expense.date ? formatDate(expense.date) : 'N/A'}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            ${expense.amount.toFixed(2)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {expense.category || 'N/A'}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {expense.projects?.name || 'N/A'}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {expense.payment_method || 'N/A'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-10 bg-card border border-border rounded-xl shadow-sm">
                  <Receipt className="h-12 w-12 text-muted-foreground/50 mx-auto mb-3" />
                  <p className="text-muted-foreground">No expenses recorded yet.</p>
                </div>
              )}
            </div>

            <div id="support" className="bg-white p-6 rounded-lg shadow-md mb-6 overflow-hidden">
              <div className="border-b border-border p-6 bg-muted/30">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="p-3 bg-primary/10 rounded-lg mr-4 shadow-sm">
                      <MessageSquare className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-700 mb-4">Contact Us & Support</h3>
                      <p className="text-sm text-muted-foreground">Submit queries, feature requests, or report issues.</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-6 grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
                <div className="lg:col-span-1 flex flex-col bg-white p-6 rounded-lg border border-border shadow-md">
                  <h3 className="text-lg font-medium mb-1 text-foreground">Send a New Message</h3>
                  <p className="text-sm text-muted-foreground mb-4">We typically respond within 24-48 hours.</p>
                  <form onSubmit={handleMessageSubmit} className="space-y-4">
                    <div>
                      <label htmlFor="messageSubject" className="block text-sm font-medium text-muted-foreground mb-1">
                        Subject (Optional)
                      </label>
                      <Input
                        id="messageSubject"
                        type="text"
                        value={messageSubject}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setMessageSubject(e.target.value)}
                        placeholder="e.g., Feature Request for..."
                        className="bg-input/50"
                      />
                    </div>
                    <div>
                      <label htmlFor="messageBody" className="block text-sm font-medium text-muted-foreground mb-1">
                        Your Message
                      </label>
                      <Textarea
                        id="messageBody"
                        value={messageBody}
                        onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setMessageBody(e.target.value)}
                        placeholder="Describe your issue or request in detail..."
                        rows={5}
                        required
                        className="bg-input/50"
                      />
                    </div>
                    <Button type="submit" disabled={isSubmittingMessage} className="w-full">
                      {isSubmittingMessage ? (
                        <span className="flex items-center justify-center">
                          Processing...
                        </span>
                      ) : (
                        <span className="flex items-center justify-center">
                          Send Message
                        </span>
                      )}
                    </Button>
                    {messageSuccess && <p className="text-sm text-green-600 mt-2 text-center">{messageSuccess}</p>}
                    {messageError && <p className="text-sm text-red-600 mt-2 text-center">{messageError}</p>}
                  </form>
                </div>

                <div className="lg:col-span-2 bg-white p-6 rounded-lg border border-border shadow-md min-h-[400px]">
                  <h3 className="text-lg font-medium mb-4 text-foreground">Message History</h3>
                  {isLoadingMessages ? (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-muted-foreground ml-2">Loading messages...</p>
                    </div>
                  ) : clientMessages.length > 0 ? (
                    <ScrollArea className="h-[calc(100%-40px)] pr-3">
                      <div className="space-y-4">
                        {clientMessages.map((msg) => (
                          <div
                            key={msg.id}
                            className={cn(
                              "p-4 rounded-lg shadow-sm border",
                              msg.isFromClient ? "bg-primary/5 border-primary/20" : "bg-muted/20 border-border"
                            )}
                          >
                            {msg.subject && <h4 className={cn("font-semibold mb-1 text-sm", msg.isFromClient ? "text-primary" : "text-foreground")}>{msg.subject}</h4>}
                            <p
                              className={cn(
                                "text-sm whitespace-pre-wrap",
                                msg.isFromClient ? "text-primary-foreground/90" : "text-muted-foreground"
                              )}
                            >
                              {msg.body}
                            </p>
                            <p
                              className={cn(
                                "text-xs mt-2",
                                msg.isFromClient ? "text-primary-foreground/70" : "text-muted-foreground/70"
                              )}
                            >
                              {formatDate(msg.createdAt)}
                            </p>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full text-center py-8">
                      <MessageSquare className="h-12 w-12 text-muted-foreground/50 mb-3" />
                      <p className="text-muted-foreground italic">No messages yet.</p>
                      <p className="text-sm text-muted-foreground/80">Send one using the form on the left.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div id="invoices" className="bg-white p-6 rounded-lg shadow-md mb-6">
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Invoices & Payments</h3>
              {invoices && invoices.length > 0 ? (
                <div className="overflow-auto max-w-full">
                  <ul className="space-y-4 mt-4">
                    {invoices.map((inv) => (
                      <li key={inv.id} className="p-4 bg-slate-50 rounded-lg border">
                        <h4 className="font-medium text-gray-800 truncate">Invoice #{inv.invoice_number}</h4>
                        <p className="text-sm text-gray-600">Status: <Badge variant={getStatusBadgeClass(inv.status)} className="capitalize text-xs">{inv.status || 'N/A'}</Badge></p>
                        <p className="text-sm text-gray-600">Amount: ${inv.amount?.toFixed(2) ?? 'N/A'}</p>
                        <p className="text-sm text-gray-600">Due Date: {inv.due_date ? formatDate(inv.due_date) : 'N/A'}</p>
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <div className="text-center py-10 bg-card border border-border rounded-xl shadow-sm">
                  <FileText className="h-12 w-12 text-muted-foreground/50 mx-auto mb-3" />
                  <p className="text-center text-muted-foreground py-4">No invoices available.</p>
                </div>
              )}
            </div>

            <div id="credentials" className="bg-white p-6 rounded-lg shadow-md mb-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold text-gray-700">Credentials</h3>
                <div className="flex items-center space-x-2 bg-slate-100 rounded-md p-1">
                  <button
                    className={`p-1.5 rounded ${credentialsViewMode === 'cards' ? 'bg-white shadow-sm' : 'text-gray-500 hover:text-gray-700'}`}
                    onClick={() => setCredentialsViewMode('cards')}
                    title="Card View"
                  >
                    <LayoutGrid className="h-4 w-4" />
                  </button>
                  <button
                    className={`p-1.5 rounded ${credentialsViewMode === 'table' ? 'bg-white shadow-sm' : 'text-gray-500 hover:text-gray-700'}`}
                    onClick={() => setCredentialsViewMode('table')}
                    title="Table View"
                  >
                    <List className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {credentials && credentials.length > 0 ? (
                credentialsViewMode === 'cards' ? (
                  <div className="overflow-auto max-w-full">
                    <ul className="space-y-4 mt-4">
                      {credentials.map((cred) => (
                        <li key={cred.id} className="p-4 bg-slate-50 rounded-lg border">
                          <div className="flex justify-between items-center mb-1 flex-wrap gap-2">
                            <h4 className="font-medium text-gray-800 truncate">{cred.name || 'N/A'}</h4>
                            <Button variant="outline" size="sm" onClick={() => handleViewCredential(cred)}>View Details</Button>
                          </div>
                          <p className="text-sm text-gray-600">Type: {cred.credential_type && cred.credential_type.trim() ? cred.credential_type : 'N/A'}</p>
                          <p className="text-sm text-gray-600">Username: {cred.username || 'N/A'}</p>
                        </li>
                      ))}
                    </ul>
                  </div>
                ) : (
                  <div className="mt-4">
                    <CredentialsTableView credentials={credentials} />
                  </div>
                )
              ) : (
                <div className="text-center py-10 bg-card border border-border rounded-xl shadow-sm">
                  <Key className="h-12 w-12 text-muted-foreground/50 mx-auto mb-3" />
                  <p className="text-center text-gray-500 py-4">No credentials available.</p>
                </div>
              )}
            </div>

            <div id="contacts" className="bg-white p-6 rounded-lg shadow-md mb-6">
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Key Contacts</h3>
              {contacts && contacts.length > 0 ? (
                <div className="overflow-auto max-w-full">
                  <ul className="space-y-4 mt-4">
                    {contacts.map((contact) => (
                      <li key={contact.id} className="p-4 bg-slate-50 rounded-lg border">
                        <h4 className="font-medium text-gray-800 truncate">{contact.name || 'N/A'}</h4>
                        {contact.email && <p className="text-sm text-gray-600 overflow-hidden text-ellipsis">Email: {contact.email}</p>}
                        {contact.phone && <p className="text-sm text-gray-600">Phone: {contact.phone}</p>}
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <div className="text-center py-10 bg-card border border-border rounded-xl shadow-sm">
                  <Users className="h-12 w-12 text-muted-foreground/50 mx-auto mb-3" />
                  <p className="text-center text-gray-500 py-4">No contacts available.</p>
                </div>
              )}
            </div>

          </div>
        </main>
      </div>

      {selectedCredential && isModalOpen && (
        <>
          <div style={{ display: 'none' }}>
            <ClientCredentialModal
              isOpen={isModalOpen}
              onClose={() => setIsModalOpen(false)}
              credential={selectedCredential}
            />
          </div>

          <SimpleCredentialModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            credential={selectedCredential}
          />
        </>
      )}
    </div>
  );
};

export default ClientPortalView;
