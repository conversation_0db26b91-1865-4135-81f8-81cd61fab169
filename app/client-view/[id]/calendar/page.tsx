'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  Calendar as CalendarIcon,
  ChevronLeft,
  ChevronRight,
  Clock,
  Users,
  MapPin,
  Plus,
  Filter,
  AlertCircle,
  ArrowRight,
  CheckCircle2,
  Video,
  Phone
} from 'lucide-react'
import { cn } from '../../../../lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'

interface CalendarPageProps {
  params: {
    id: string
  }
}

interface Event {
  id: string
  title: string
  date: string
  startTime: string
  endTime: string
  type: 'meeting' | 'deadline' | 'call'
  location?: string
  attendees?: string[]
  description?: string
  isCompleted?: boolean
}

// Sample events data - in a real app, this would come from an API
const sampleEvents: Event[] = [
  {
    id: '1',
    title: 'Project Kickoff Meeting',
    date: '2024-07-15',
    startTime: '10:00',
    endTime: '11:30',
    type: 'meeting',
    location: 'Zoom',
    attendees: ['<PERSON>', '<PERSON>', '<PERSON>'],
    description: 'Initial project kickoff meeting to discuss goals, timeline, and deliverables'
  },
  {
    id: '2',
    title: 'Design Review',
    date: '2024-07-18',
    startTime: '14:00',
    endTime: '15:00',
    type: 'meeting',
    location: 'Google Meet',
    attendees: ['Sarah Johnson', 'Alex Wong'],
    description: 'Review initial design concepts and provide feedback'
  },
  {
    id: '3',
    title: 'Content Submission Deadline',
    date: '2024-07-22',
    startTime: '18:00',
    endTime: '18:00',
    type: 'deadline',
    description: 'Deadline for submitting all website content and assets'
  },
  {
    id: '4',
    title: 'Progress Update Call',
    date: '2024-07-25',
    startTime: '11:00',
    endTime: '11:30',
    type: 'call',
    location: 'Phone',
    attendees: ['John Smith'],
    description: 'Weekly progress update call'
  },
  {
    id: '5',
    title: 'Final Review Meeting',
    date: '2024-08-01',
    startTime: '15:00',
    endTime: '16:30',
    type: 'meeting',
    location: 'Zoom',
    attendees: ['John Smith', 'Sarah Johnson', 'Michael Brown', 'Alex Wong'],
    description: 'Final review before project launch'
  }
]

// Helper function to format date
const formatDate = (dateString: string) => {
  const options: Intl.DateTimeFormatOptions = {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }
  return new Date(dateString).toLocaleDateString('en-US', options)
}

// Helper function to format time
const formatTime = (timeString: string) => {
  const [hours, minutes] = timeString.split(':')
  const hour = parseInt(hours)
  const ampm = hour >= 12 ? 'PM' : 'AM'
  const formattedHour = hour % 12 || 12
  return `${formattedHour}:${minutes} ${ampm}`
}

// Helper function to get event type badge
const getEventTypeBadge = (type: string) => {
  switch (type) {
    case 'meeting':
      return <Badge className="bg-primary/10 text-primary border border-primary/20">Meeting</Badge>
    case 'deadline':
      return <Badge className="bg-destructive/10 text-destructive border border-destructive/20">Deadline</Badge>
    case 'call':
      return <Badge className="bg-secondary/10 text-secondary border border-secondary/20">Call</Badge>
    default:
      return <Badge>Event</Badge>
  }
}

// Helper function to get event icon
const getEventIcon = (type: string, location?: string) => {
  if (type === 'meeting') {
    if (location?.toLowerCase().includes('zoom') || location?.toLowerCase().includes('meet')) {
      return <Video className="h-5 w-5 text-primary" />
    }
    return <Users className="h-5 w-5 text-primary" />
  }
  if (type === 'deadline') {
    return <Clock className="h-5 w-5 text-destructive" />
  }
  if (type === 'call') {
    return <Phone className="h-5 w-5 text-secondary" />
  }
  return <CalendarIcon className="h-5 w-5 text-muted-foreground" />
}

const CalendarPage: React.FC<CalendarPageProps> = ({ params }) => {
  const router = useRouter()
  const [events, setEvents] = useState<Event[]>(sampleEvents)
  const [currentMonth, setCurrentMonth] = useState(new Date())
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Get current month and year
  const monthYear = currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })

  // Navigate to previous month
  const prevMonth = () => {
    const newMonth = new Date(currentMonth)
    newMonth.setMonth(newMonth.getMonth() - 1)
    setCurrentMonth(newMonth)
  }

  // Navigate to next month
  const nextMonth = () => {
    const newMonth = new Date(currentMonth)
    newMonth.setMonth(newMonth.getMonth() + 1)
    setCurrentMonth(newMonth)
  }

  // Group events by date
  const eventsByDate = events.reduce((acc, event) => {
    if (!acc[event.date]) {
      acc[event.date] = []
    }
    acc[event.date].push(event)
    return acc
  }, {} as Record<string, Event[]>)

  // Sort dates
  const sortedDates = Object.keys(eventsByDate).sort((a, b) =>
    new Date(a).getTime() - new Date(b).getTime()
  )

  return (
    <div className="min-h-screen bg-background relative">
      {/* Background pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none"></div>

      <div className="py-8 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto relative">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-foreground bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">Calendar</h1>
          <p className="text-muted-foreground mt-1">
            View upcoming meetings, deadlines, and events
          </p>
        </div>

        {/* Calendar navigation */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" onClick={prevMonth}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h2 className="text-lg font-medium">{monthYear}</h2>
            <Button variant="outline" size="icon" onClick={nextMonth}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filter
            </Button>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Request Meeting
            </Button>
          </div>
        </div>

        {/* Events list */}
        <div className="space-y-6">
          {sortedDates.length > 0 ? (
            sortedDates.map(date => (
              <div key={date}>
                <h3 className="text-md font-medium mb-3">{formatDate(date)}</h3>
                <div className="space-y-3">
                  {eventsByDate[date].map(event => (
                    <Card key={event.id} className="p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-start gap-4">
                        <div className="p-2 bg-muted rounded-md">
                          {getEventIcon(event.type, event.location)}
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="text-base font-medium">{event.title}</h4>
                              <div className="flex items-center gap-2 mt-1">
                                <span className="text-sm text-muted-foreground">
                                  {formatTime(event.startTime)} - {formatTime(event.endTime)}
                                </span>
                                {getEventTypeBadge(event.type)}
                              </div>
                            </div>
                            <Button variant="ghost" size="sm" className="gap-1">
                              Details
                              <ArrowRight className="h-3 w-3" />
                            </Button>
                          </div>

                          {event.description && (
                            <p className="text-sm text-muted-foreground mt-2">{event.description}</p>
                          )}

                          <div className="flex flex-wrap gap-4 mt-3">
                            {event.location && (
                              <div className="flex items-center text-sm text-muted-foreground">
                                <MapPin className="h-3.5 w-3.5 mr-1" />
                                {event.location}
                              </div>
                            )}

                            {event.attendees && event.attendees.length > 0 && (
                              <div className="flex items-center text-sm text-muted-foreground">
                                <Users className="h-3.5 w-3.5 mr-1" />
                                {event.attendees.length} {event.attendees.length === 1 ? 'Attendee' : 'Attendees'}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            ))
          ) : (
            <div className="card p-8 text-center">
              <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-4">
                <AlertCircle className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-1">No events scheduled</h3>
              <p className="text-muted-foreground mb-4">
                There are no events scheduled for this month.
              </p>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Request Meeting
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CalendarPage
