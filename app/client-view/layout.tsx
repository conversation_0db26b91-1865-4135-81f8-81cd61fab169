'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  LogOut, Menu, X, Bell, User,
  HelpCircle, FileText, Home,
  Settings, ChevronDown, Calendar
} from 'lucide-react'
import { cn } from '../../lib/utils'

export default function ClientViewLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const [notificationsOpen, setNotificationsOpen] = useState(false)
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const pathname = usePathname()

  // Handle scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Close menus when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement
      if (!target.closest('.notifications-menu') && !target.closest('.notifications-trigger')) {
        setNotificationsOpen(false)
      }
      if (!target.closest('.user-menu') && !target.closest('.user-menu-trigger')) {
        setUserMenuOpen(false)
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [])

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header
        className={cn(
          "sticky top-0 z-50 transition-all duration-300 backdrop-blur-sm",
          scrolled
            ? "bg-background/95 border-b border-border py-2 shadow-sm"
            : "bg-background/80 py-3"
        )}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            {/* Logo and brand */}
            <div className="flex items-center">
              <Link href="/client-login" className="flex items-center group">
                <div className="p-2 rounded-xl mr-3 shadow-sm transition-all duration-300 group-hover:shadow-md">
                  <span className="font-bold text-xl text-current">D</span>
                </div>
                <span className="text-xl font-bold text-current transition-all duration-300">
                  DevCRM
                </span>
              </Link>
            </div>

            {/* Desktop navigation */}
            <nav className="hidden md:flex items-center space-x-6">
              <Link
                href="/client-dashboard"
                className={cn(
                  "flex items-center text-sm font-medium transition-all duration-200 px-3 py-2 rounded-md",
                  pathname === '/client-dashboard'
                    ? "bg-primary/10 text-primary font-semibold"
                    : "text-foreground/80 hover:bg-slate-100 hover:text-primary"
                )}
              >
                <Home className="h-4 w-4 mr-1.5" />
                Dashboard
              </Link>
              <Link
                href="/client-support"
                className={cn(
                  "flex items-center text-sm font-medium transition-all duration-200 px-3 py-2 rounded-md",
                  pathname === '/client-support'
                    ? "bg-primary/10 text-primary font-semibold"
                    : "text-foreground/80 hover:bg-slate-100 hover:text-primary"
                )}
              >
                <HelpCircle className="h-4 w-4 mr-1.5" />
                Support
              </Link>
              <Link
                href={`/client-view/${pathname.split('/')[2]}/resources`}
                className={cn(
                  "flex items-center text-sm font-medium transition-all duration-200 px-3 py-2 rounded-md",
                  pathname.includes('/resources')
                    ? "bg-primary/10 text-primary font-semibold"
                    : "text-foreground/80 hover:bg-slate-100 hover:text-primary"
                )}
              >
                <FileText className="h-4 w-4 mr-1.5" />
                Resources
              </Link>
              <Link
                href={`/client-view/${pathname.split('/')[2]}/calendar`}
                className={cn(
                  "flex items-center text-sm font-medium transition-all duration-200 px-3 py-2 rounded-md",
                  pathname.includes('/calendar')
                    ? "bg-primary/10 text-primary font-semibold"
                    : "text-foreground/80 hover:bg-slate-100 hover:text-primary"
                )}
              >
                <Calendar className="h-4 w-4 mr-1.5" />
                Calendar
              </Link>

              {/* Notifications */}
              <div className="relative notifications-menu">
                <button
                  className={cn(
                    "p-1.5 rounded-full transition-colors duration-200 relative notifications-trigger",
                    notificationsOpen ? "bg-secondary" : "hover:bg-secondary/80"
                  )}
                  aria-label="Notifications"
                  onClick={(e) => {
                    e.stopPropagation()
                    setNotificationsOpen(!notificationsOpen)
                    setUserMenuOpen(false)
                  }}
                >
                  <Bell className="w-5 h-5 text-foreground/70" />
                  <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-destructive ring-2 ring-background animate-pulse-subtle" />
                </button>

                {/* Notifications dropdown */}
                {notificationsOpen && (
                  <div className="absolute right-0 mt-2 w-80 bg-card rounded-xl shadow-lg ring-1 ring-black ring-opacity-5 overflow-hidden animate-scale-in">
                    <div className="p-4 border-b border-border">
                      <h3 className="text-sm font-semibold">Notifications</h3>
                    </div>
                    <div className="max-h-96 overflow-y-auto">
                      <div className="py-2 px-4 border-b border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-start gap-3">
                          <div className="bg-primary/10 p-2 rounded-full">
                            <FileText className="h-4 w-4 text-primary" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">New resource available</p>
                            <p className="text-xs text-muted-foreground mt-1">The latest documentation has been published</p>
                            <p className="text-xs text-muted-foreground mt-2">2 hours ago</p>
                          </div>
                        </div>
                      </div>
                      <div className="py-2 px-4 border-b border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-start gap-3">
                          <div className="bg-success/10 p-2 rounded-full">
                            <Bell className="h-4 w-4 text-success" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">Maintenance completed</p>
                            <p className="text-xs text-muted-foreground mt-1">System maintenance has been completed successfully</p>
                            <p className="text-xs text-muted-foreground mt-2">Yesterday</p>
                          </div>
                        </div>
                      </div>
                      <div className="py-2 px-4 hover:bg-muted/50 transition-colors">
                        <div className="flex items-start gap-3">
                          <div className="bg-warning/10 p-2 rounded-full">
                            <HelpCircle className="h-4 w-4 text-warning" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">Your support ticket was updated</p>
                            <p className="text-xs text-muted-foreground mt-1">Ticket #45892 has a new response</p>
                            <p className="text-xs text-muted-foreground mt-2">3 days ago</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="p-2 border-t border-border">
                      <Link href="/notifications" className="block text-xs text-center py-2 text-primary hover:underline">
                        View all notifications
                      </Link>
                    </div>
                  </div>
                )}
              </div>

              {/* User menu */}
              <div className="relative user-menu">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    setUserMenuOpen(!userMenuOpen)
                    setNotificationsOpen(false)
                  }}
                  className="flex items-center space-x-2 user-menu-trigger"
                >
                  <div className="h-8 w-8 rounded-full bg-gradient-to-r from-primary to-accent flex items-center justify-center text-white shadow-sm">
                    <User className="h-4 w-4" />
                  </div>
                  <div className="flex items-center">
                    <ChevronDown className="h-4 w-4 text-foreground/60" />
                  </div>
                </button>

                {/* User dropdown */}
                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-card rounded-xl shadow-lg ring-1 ring-black ring-opacity-5 py-1 animate-scale-in">
                    <Link
                      href="/profile"
                      className="flex items-center px-4 py-2 text-sm text-foreground/80 hover:bg-secondary transition-colors"
                    >
                      <User className="mr-2 h-4 w-4 text-foreground/70" />
                      Your Profile
                    </Link>
                    <Link
                      href="/settings"
                      className="flex items-center px-4 py-2 text-sm text-foreground/80 hover:bg-secondary transition-colors"
                    >
                      <Settings className="mr-2 h-4 w-4 text-foreground/70" />
                      Settings
                    </Link>
                    <div className="border-t border-border my-1"></div>
                    <button
                      onClick={() => {
                        // Handle logout logic here
                        setUserMenuOpen(false)
                      }}
                      className="flex items-center w-full text-left px-4 py-2 text-sm text-foreground/80 hover:bg-secondary transition-colors"
                    >
                      <LogOut className="mr-2 h-4 w-4 text-foreground/70" />
                      Sign out
                    </button>
                  </div>
                )}
              </div>
            </nav>

            {/* Mobile menu button */}
            <div className="md:hidden flex items-center">
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="p-2 rounded-md text-foreground/70 hover:text-foreground hover:bg-secondary transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1"
                aria-expanded={isMobileMenuOpen}
                aria-label="Toggle mobile menu"
              >
                {isMobileMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden bg-card border-t border-border py-2 animate-slide-down">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-1 py-3">
              <div className="px-4 py-2">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 rounded-full bg-gradient-to-r from-primary to-accent flex items-center justify-center text-white shadow-sm">
                    <User className="h-5 w-5" />
                  </div>
                </div>
              </div>
              <div className="border-t border-border my-2"></div>
              <Link
                href="/client-dashboard"
                className="flex items-center px-3 py-2 rounded-lg text-base font-medium text-foreground/80 hover:text-primary hover:bg-secondary transition-all duration-200"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Home className="h-5 w-5 mr-3" />
                Dashboard
              </Link>
              <Link
                href="/client-support"
                className="flex items-center px-3 py-2 rounded-lg text-base font-medium text-foreground/80 hover:text-primary hover:bg-secondary transition-all duration-200"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <HelpCircle className="h-5 w-5 mr-3" />
                Support
              </Link>
              <Link
                href={`/client-view/${pathname.split('/')[2]}/resources`}
                className="flex items-center px-3 py-2 rounded-lg text-base font-medium text-foreground/80 hover:text-primary hover:bg-secondary transition-all duration-200"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <FileText className="h-5 w-5 mr-3" />
                Resources
              </Link>
              <Link
                href={`/client-view/${pathname.split('/')[2]}/calendar`}
                className="flex items-center px-3 py-2 rounded-lg text-base font-medium text-foreground/80 hover:text-primary hover:bg-secondary transition-all duration-200"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Calendar className="h-5 w-5 mr-3" />
                Calendar
              </Link>
              <Link
                href="/notifications"
                className="flex items-center px-3 py-2 rounded-lg text-base font-medium text-foreground/80 hover:text-primary hover:bg-secondary transition-all duration-200"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Bell className="h-5 w-5 mr-3" />
                <span>Notifications</span>
                <span className="ml-auto bg-destructive text-destructive-foreground text-xs rounded-full px-2 py-0.5">3</span>
              </Link>
              <Link
                href="/profile"
                className="flex items-center px-3 py-2 rounded-lg text-base font-medium text-foreground/80 hover:text-primary hover:bg-secondary transition-all duration-200"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <User className="h-5 w-5 mr-3" />
                Profile
              </Link>
              <Link
                href="/settings"
                className="flex items-center px-3 py-2 rounded-lg text-base font-medium text-foreground/80 hover:text-primary hover:bg-secondary transition-all duration-200"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Settings className="h-5 w-5 mr-3" />
                Settings
              </Link>
              <div className="border-t border-border my-2"></div>
              <button
                onClick={() => {
                  // Handle logout logic here
                  setIsMobileMenuOpen(false)
                }}
                className="flex items-center w-full text-left px-3 py-2 rounded-lg text-base font-medium text-foreground/80 hover:text-destructive hover:bg-destructive/10 transition-all duration-200"
              >
                <LogOut className="h-5 w-5 mr-3" />
                Sign Out
              </button>
            </div>
          </div>
        )}
      </header>

      {/* Main content */}
      <main className="flex-1 bg-slate-50 p-4 pt-0 lg:p-8 lg:pt-2">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-muted/50 border-t border-border md:ml-64">
        <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <p className="mt-4 text-sm text-muted-foreground">
                Providing professional development services and client management solutions. Our platform helps streamline communication and project management between clients and development teams.
              </p>
              <div className="mt-6 flex space-x-4">
                <a href="#" className="text-foreground/60 hover:text-primary transition-all duration-200" aria-label="Twitter">
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
                <a href="#" className="text-foreground/60 hover:text-primary transition-all duration-200" aria-label="GitHub">
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
                  </svg>
                </a>
                <a href="#" className="text-foreground/60 hover:text-primary transition-all duration-200" aria-label="LinkedIn">
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                  </svg>
                </a>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-foreground tracking-wider uppercase">Resources</h3>
              <ul className="mt-4 space-y-3">
                <li>
                  <Link href="/documentation" className="text-sm text-muted-foreground hover:text-primary transition-all duration-200">
                    Documentation
                  </Link>
                </li>
                <li>
                  <Link href="/tutorials" className="text-sm text-muted-foreground hover:text-primary transition-all duration-200">
                    Tutorials
                  </Link>
                </li>
                <li>
                  <Link href="/faq" className="text-sm text-muted-foreground hover:text-primary transition-all duration-200">
                    FAQ
                  </Link>
                </li>
                <li>
                  <Link href="/support" className="text-sm text-muted-foreground hover:text-primary transition-all duration-200">
                    Support Center
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-foreground tracking-wider uppercase">Legal</h3>
              <ul className="mt-4 space-y-3">
                <li>
                  <Link href="/privacy" className="text-sm text-muted-foreground hover:text-primary transition-all duration-200">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="text-sm text-muted-foreground hover:text-primary transition-all duration-200">
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link href="/cookie-policy" className="text-sm text-muted-foreground hover:text-primary transition-all duration-200">
                    Cookie Policy
                  </Link>
                </li>
                <li>
                  <Link href="/gdpr" className="text-sm text-muted-foreground hover:text-primary transition-all duration-200">
                    GDPR Compliance
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="mt-8 border-t border-border pt-6 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} DevCRM. All rights reserved.
            </p>
            <div className="mt-4 md:mt-0">
              <p className="text-sm text-muted-foreground">
                <span className="inline-flex items-center">
                  <span className="h-2 w-2 rounded-full bg-success mr-2"></span> All systems operational
                </span>
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
