import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export const dynamic = 'force-dynamic' // Ensure this is not cached

export async function GET() {
  try {
    console.log('API - Fetching direct contacts data')
    
    // Create a direct Supabase client without authentication
    console.log('API - Using anonymous client')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    )
    
    // Fetch contacts with detailed logging
    console.log('API - About to fetch contacts')
    const { data, error, status } = await supabase
      .from('contacts')
      .select('*')
      .order('name')
    
    console.log('API - Raw contacts query result:', { 
      status,
      count: data?.length || 0, 
      error: error ? `${error.code}: ${error.message}` : null,
      sampleData: data && data.length > 0 ? JSON.stringify(data[0]).substring(0, 100) + '...' : 'No data'
    })
    
    if (error) {
      console.error('API - Contacts fetch error:', error)
      return NextResponse.json(
        { error: `Failed to fetch contacts data: ${error.message}` },
        { status: 500 }
      )
    }
    
    if (!data || data.length === 0) {
      console.log('API - No contacts found in database')
      return NextResponse.json({ contacts: [] })
    }
    
    console.log('API - Successfully fetched contacts data:', data.length)
    console.log('API - First contact:', data[0].name)
    
    return NextResponse.json({ 
      contacts: data,
      meta: {
        count: data.length,
        timestamp: new Date().toISOString()
      }
    })
  } catch (err) {
    console.error('API - Unexpected error fetching contacts data:', err)
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: err instanceof Error ? err.message : String(err) },
      { status: 500 }
    )
  }
}
