import { NextResponse } from 'next/server'
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { type SupabaseClient } from '@supabase/supabase-js' // Import SupabaseClient type

// Define interfaces for the expected data structures
interface CompanyData {
  company_name: string;
  industry?: string;
  company_status?: string;
  address?: string;
  website?: string;
  company_notes?: string;
}

interface ProjectData {
  name: string;
  description?: string;
  start_date?: string;
  expected_end_date?: string;
  status?: string;
  company_id: string; 
  value?: number; 
}

interface ContactData {
  name: string;
  email?: string;
  phone?: string;
  position?: string;
  notes?: string;
  company_id: string; 
}

// Combined type for the request body, making project and contact fields optional
interface OnboardingRequestBody extends CompanyData {
  project_name?: string;
  project_description?: string;
  project_start_date?: string;
  project_expected_end_date?: string;
  project_status?: string;
  project_budget?: string; // Keep as string from form, parse to float later

  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
  contact_position?: string;
  contact_notes?: string;
}

async function internalCreateCompany(supabase: SupabaseClient, companyData: CompanyData) {
  const companyToInsert = {
    name: companyData.company_name,
    industry: companyData.industry || null,
    status: companyData.company_status || 'active',
    address: companyData.address || null,
    website: companyData.website || null,
    notes: companyData.company_notes || null,
  };
  const { data, error } = await supabase
    .from('companies')
    .insert(companyToInsert)
    .select('id')
    .single();

  if (error) {
    console.error('Error creating company in API:', error);
    return { error: error.message, data: null };
  }
  return { error: null, data };
}

export async function POST(request: Request) {
  const cookieStore = cookies()
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          // Using 'any' cast as a workaround for type inconsistencies
          (cookieStore as any).set(name, value, options)
        },
        remove(name: string, options: CookieOptions) {
          (cookieStore as any).set(name, '', options) // To remove, set an empty value
        },
      },
    }
  )

  try {
    const body: OnboardingRequestBody = await request.json();
    console.log('Onboarding API received data:', body);

    const {
      company_name,
      industry,
      company_status,
      address,
      website,
      company_notes,
      project_name,
      project_description,
      project_start_date,
      project_expected_end_date,
      project_status,
      project_budget,
      contact_name,
      contact_email,
      contact_phone,
      contact_position,
      contact_notes
    } = body;

    const companyResult = await internalCreateCompany(supabase, {
      company_name,
      industry,
      company_status,
      address,
      website,
      company_notes
    });

    if (companyResult.error || !companyResult.data?.id) {
      return NextResponse.json({ error: `Failed to create company: ${companyResult.error || 'Unknown error'}` }, { status: 500 });
    }
    const companyId = companyResult.data.id;
    console.log('Company created with ID:', companyId);

    if (project_name) {
      const projectToInsert = {
        name: project_name,
        description: project_description || null,
        start_date: project_start_date || null,
        expected_end_date: project_expected_end_date || null,
        status: project_status || 'planning',
        company_id: companyId,
        value: project_budget ? parseFloat(project_budget) : null,
      };
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .insert(projectToInsert)
        .select('id')
        .single();

      if (projectError) {
        console.error(`Error creating project for company ${companyId}:`, projectError);
      } else {
        console.log('Project created with ID:', projectData?.id);
      }
    }

    if (contact_name) {
      const contactToInsert = {
        name: contact_name,
        email: contact_email || null,
        phone: contact_phone || null,
        position: contact_position || null,
        notes: contact_notes || null,
        company_id: companyId,
      };
      const { data: contactData, error: contactError } = await supabase
        .from('contacts')
        .insert(contactToInsert)
        .select('id')
        .single();

      if (contactError) {
        console.error(`Error creating contact for company ${companyId}:`, contactError);
      } else {
        console.log('Contact created with ID:', contactData?.id);
      }
    }

    return NextResponse.json({ 
      message: 'Company onboarded successfully', 
      companyId: companyId 
    }, { status: 201 });

  } catch (e: unknown) {
    let errorMessage = 'An unexpected error occurred during onboarding';
    if (e instanceof Error) {
      errorMessage = e.message;
    }
    console.error('General error in onboarding API:', e);
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
} 