import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Fetch companies without RLS restrictions
    const { data, error } = await supabaseAdmin
      .from('companies')
      .select('id, name')
      .order('name');

    if (error) {
      console.error('Admin API error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Return the companies
    return NextResponse.json({ companies: data });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 