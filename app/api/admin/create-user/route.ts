import { NextRequest, NextResponse } from 'next/server'
import { createAdminClient } from '@/lib/supabase/server'
import { headers } from 'next/headers'

// Rate limiting map (in a production app, use Redis or similar)
const rateLimitMap = new Map<string, { count: number, timestamp: number }>()
const MAX_REQUESTS = 5 // Max 5 requests
const TIME_WINDOW = 60 * 1000 // per minute

export async function POST(request: NextRequest) {
  try {
    // Check request method
    if (request.method !== 'POST') {
      return NextResponse.json(
        { error: 'Method not allowed' },
        { status: 405 }
      )
    }

    // Check content type
    const contentType = request.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      return NextResponse.json(
        { error: 'Content-Type must be application/json' },
        { status: 400 }
      )
    }

    // Check CSRF protection (in a real app, use a proper CSRF token)
    const referer = request.headers.get('referer')
    const origin = request.headers.get('origin')
    const host = request.headers.get('host')

    if (!referer || !host || !referer.includes(host)) {
      console.warn('Possible CSRF attempt:', { referer, origin, host })
      return NextResponse.json(
        { error: 'Invalid request origin' },
        { status: 403 }
      )
    }

    // Get the authenticated user from Supabase
    const supabase = createAdminClient()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Implement rate limiting
    const userId = session.user.id
    const now = Date.now()
    const userRateLimit = rateLimitMap.get(userId) || { count: 0, timestamp: now }

    // Reset count if outside time window
    if (now - userRateLimit.timestamp > TIME_WINDOW) {
      userRateLimit.count = 0
      userRateLimit.timestamp = now
    }

    // Check if rate limit exceeded
    if (userRateLimit.count >= MAX_REQUESTS) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429 }
      )
    }

    // Increment request count
    userRateLimit.count++
    rateLimitMap.set(userId, userRateLimit)

    // Check if the current user is an admin
    const { data: currentUser, error: userError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', session.user.id)
      .single()

    if (userError || currentUser?.role !== 'admin') {
      console.warn('Non-admin user attempted to access admin endpoint:', session.user.id)
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      )
    }

    // Get the new user data from the request
    const { email, password, isAdmin = false } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Create the user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm the email
    })

    if (authError) {
      console.error('Error creating user:', authError)
      return NextResponse.json(
        { error: authError.message },
        { status: 500 }
      )
    }

    // Add the user to the user_roles table
    const { error: insertError } = await supabase
      .from('user_roles')
      .insert({
        user_id: authData.user.id,
        role: isAdmin ? 'admin' : 'user',
        created_at: new Date().toISOString()
      })

    if (insertError) {
      console.error('Error adding user to user_roles table:', insertError)
      return NextResponse.json(
        { error: 'Failed to add user to database' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      user: {
        id: authData.user.id,
        email: authData.user.email,
        isAdmin
      }
    })
  } catch (err) {
    console.error('Unexpected error creating user:', err)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
