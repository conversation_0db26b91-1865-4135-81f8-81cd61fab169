import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'

export const dynamic = 'force-dynamic' // Ensure this is not cached

interface CompanyReference {
  id: string;
  name: string;
}

interface RecentProject {
  id: string;
  name: string;
  status: string | null;
  companies: CompanyReference | null; // Assuming it *should* be one, will cast later if needed
}

interface RecentInvoice {
  id: string;
  invoice_number: string;
  amount: number;
  status: string | null;
  companies: CompanyReference | null; // Assuming it *should* be one, will cast later if needed
}

export async function GET() {
  try {
    console.log('API - Admin - Fetching dashboard data')

    // First, verify the user is authenticated and is an admin
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get: async (name: string) => {
            const cookieStore = await cookies()
            const cookie = cookieStore.get(name)
            return cookie?.value
          },
          set: async (name: string, value: string, options: Record<string, any>) => {
            const cookieStore = await cookies()
            cookieStore.set(name, value, options)
          },
          remove: async (name: string, options: Record<string, any>) => {
            const cookieStore = await cookies()
            cookieStore.set(name, '', { ...options, maxAge: 0 })
          },
        },
      }
    )

    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      console.error('API - Admin - No authenticated session')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Create a service role client to bypass RLS
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Fetch dashboard summary data
    console.log('API - Admin - Fetching dashboard summary data')

    // 1. Get total companies count
    let totalCompanies = 0
    try {
      const { count, error: companiesError } = await supabaseAdmin
        .from('companies')
        .select('*', { count: 'exact', head: true })

      if (companiesError) {
        console.error('API - Admin - Companies count error:', companiesError)
      } else {
        totalCompanies = count || 0
      }
    } catch (err) {
      console.error('API - Admin - Companies count unexpected error:', err)
    }

    // 2. Get active projects count
    let activeProjects = 0
    try {
      const { count, error: projectsError } = await supabaseAdmin
        .from('projects')
        .select('*', { count: 'exact', head: true })
        .in('status', ['active', 'In Progress'])

      if (projectsError) {
        console.error('API - Admin - Active projects count error:', projectsError)
      } else {
        activeProjects = count || 0
      }
    } catch (err) {
      console.error('API - Admin - Active projects count unexpected error:', err)
    }

    // 3. Get pending tasks count
    let pendingTasks = 0
    try {
      const { count, error: tasksError } = await supabaseAdmin
        .from('tasks')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending')

      if (tasksError) {
        console.error('API - Admin - Pending tasks count error:', tasksError)
      } else {
        pendingTasks = count || 0
      }
    } catch (err) {
      console.error('API - Admin - Pending tasks count unexpected error:', err)
    }

    // 4. Get expiring credentials count (credentials expiring in the next 30 days)
    const thirtyDaysFromNow = new Date()
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)

    let expiringCredentials = 0
    try {
      const { count, error: credentialsError } = await supabaseAdmin
        .from('credentials')
        .select('*', { count: 'exact', head: true })
        .lte('expiry_date', thirtyDaysFromNow.toISOString().split('T')[0])
        .gte('expiry_date', new Date().toISOString().split('T')[0])

      if (credentialsError) {
        console.error('API - Admin - Expiring credentials count error:', credentialsError)
      } else {
        expiringCredentials = count || 0
      }
    } catch (err) {
      console.error('API - Admin - Expiring credentials count unexpected error:', err)
    }

    // 5. Get unpaid invoices count
    let unpaidInvoices = 0
    try {
      const { count, error: invoicesCountError } = await supabaseAdmin
        .from('invoices')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'unpaid')

      if (invoicesCountError) {
        console.error('API - Admin - Unpaid invoices count error:', invoicesCountError)
      } else {
        unpaidInvoices = count || 0
      }
    } catch (err) {
      console.error('API - Admin - Unpaid invoices count unexpected error:', err)
    }

    // 6. Get total outstanding amount
    let totalOutstanding = 0
    try {
      const { data: unpaidInvoicesData, error: invoicesError } = await supabaseAdmin
        .from('invoices')
        .select('amount')
        .eq('status', 'unpaid')

      if (invoicesError) {
        console.error('API - Admin - Unpaid invoices data error:', invoicesError)
      } else if (unpaidInvoicesData) {
        totalOutstanding = unpaidInvoicesData.reduce((sum, invoice) => sum + (invoice.amount || 0), 0)
      }
    } catch (err) {
      console.error('API - Admin - Unpaid invoices data unexpected error:', err)
    }

    // 7. Get recent projects
    let recentProjects: RecentProject[] = []
    try {
      const { data, error: recentProjectsError } = await supabaseAdmin
        .from('projects')
        .select(`
          id,
          name,
          status,
          companies (
            id,
            name
          )
        `)
        .order('created_at', { ascending: false })
        .limit(3)

      if (recentProjectsError) {
        console.error('API - Admin - Recent projects error:', recentProjectsError)
      } else {
        // Explicitly cast the data to the expected type, handling potential array for companies
        recentProjects = (data || []).map(p => ({
          ...p,
          companies: Array.isArray(p.companies) ? p.companies[0] : p.companies
        })) as RecentProject[];
      }
    } catch (err) {
      console.error('API - Admin - Recent projects unexpected error:', err)
    }

    // 8. Get recent invoices
    let recentInvoices: RecentInvoice[] = []
    try {
      const { data, error: recentInvoicesError } = await supabaseAdmin
        .from('invoices')
        .select(`
          id,
          invoice_number,
          amount,
          status,
          companies (
            id,
            name
          )
        `)
        .order('created_at', { ascending: false })
        .limit(3)

      if (recentInvoicesError) {
        console.error('API - Admin - Recent invoices error:', recentInvoicesError)
      } else {
        // Explicitly cast the data, handling potential array for companies
        recentInvoices = (data || []).map(i => ({
          ...i,
          companies: Array.isArray(i.companies) ? i.companies[0] : i.companies
        })) as RecentInvoice[];
      }
    } catch (err) {
      console.error('API - Admin - Recent invoices unexpected error:', err)
    }

    // Return the dashboard data
    return NextResponse.json({
      summary: {
        total_companies: totalCompanies || 0,
        active_projects: activeProjects || 0,
        pending_tasks: pendingTasks || 0,
        expiring_credentials: expiringCredentials || 0,
        unpaid_invoices: unpaidInvoices || 0,
        total_outstanding: totalOutstanding || 0
      },
      recentProjects: recentProjects || [],
      recentInvoices: recentInvoices || []
    })
  } catch (err) {
    console.error('API - Admin - Unexpected error fetching dashboard data:', err)
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: err instanceof Error ? err.message : String(err) },
      { status: 500 }
    )
  }
}
