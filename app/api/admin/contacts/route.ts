import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'

export const dynamic = 'force-dynamic' // Ensure this is not cached

export async function GET() {
  try {
    console.log('API - Admin - Fetching contacts data')

    // First, verify the user is authenticated and is an admin
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          async get(name) {
            const cookie = cookieStore.get(name)
            return cookie?.value
          },
          async set(name, value, options) {
            cookieStore.set(name, value, options)
          },
          async remove(name, options) {
            cookieStore.set(name, '', { ...options, maxAge: 0 })
          },
        },
      }
    )

    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      console.error('API - Admin - No authenticated session')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify admin role
    const { data: roleData, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', session.user.id)
      .single()

    if (roleError || !roleData || roleData.role !== 'admin') {
      console.error('API - Admin - User is not an admin:', session.user.email)
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Create a service role client to bypass RLS
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Fetch contacts with detailed logging
    console.log('API - Admin - Fetching contacts with service role')
    const { data, error } = await supabaseAdmin
      .from('contacts')
      .select('*')
      .order('name')

    if (error) {
      console.error('API - Admin - Contacts fetch error:', error)
      return NextResponse.json(
        { error: `Failed to fetch contacts data: ${error.message}` },
        { status: 500 }
      )
    }

    if (!data || data.length === 0) {
      console.log('API - Admin - No contacts found in database')
      return NextResponse.json({ contacts: [] })
    }

    console.log('API - Admin - Successfully fetched contacts data:', data.length)

    return NextResponse.json({
      contacts: data,
      meta: {
        count: data.length,
        timestamp: new Date().toISOString()
      }
    })
  } catch (err) {
    console.error('API - Admin - Unexpected error fetching contacts data:', err)
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: err instanceof Error ? err.message : String(err) },
      { status: 500 }
    )
  }
}
