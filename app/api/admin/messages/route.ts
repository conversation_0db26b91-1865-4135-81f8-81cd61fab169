import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js'; // Import createClient directly
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { SupabaseClient } from '@supabase/supabase-js'; // Import type for SupabaseClient

// Helper function to get Supabase client with service role
function getSupabaseServiceRoleClient(): SupabaseClient {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Missing Supabase environment variables for service role client in admin API');
    throw new Error('Server configuration error for Supabase admin client');
  }
  // Use createClient for service role, not createRouteHandlerClient
  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: { persistSession: false }
  });
}

// Updated admin authentication logic
async function authenticateAdmin(request: NextRequest): Promise<{ adminUserId: string } | { error: string, status: number }> {
  const supabase = createRouteHandlerClient({ cookies });

  const { data: { session }, error: sessionError } = await supabase.auth.getSession();

  if (sessionError) {
    console.error('Error getting session for admin auth:', sessionError);
    return { error: 'Server error during authentication.', status: 500 };
  }

  if (!session) {
    return { error: 'Unauthorized: No active session.', status: 401 };
  }

  const { user } = session;
  if (!user) { // Should not happen if session exists, but good for type safety
    return { error: 'Unauthorized: User not found in session.', status: 401 };
  }

  // Check user_roles table for admin role
  const { data: roleData, error: roleError } = await supabase
    .from('user_roles')
    .select('role')
    .eq('user_id', user.id)
    .eq('role', 'admin') // Specifically check for 'admin' role
    .single();

  if (roleError) {
    if (roleError.code === 'PGRST116') { // No rows returned
      console.warn(`Admin auth attempt by non-admin user: ${user.email} (${user.id})`);
      return { error: 'Forbidden: User is not an administrator.', status: 403 };
    }
    console.error('Error checking admin role:', roleError);
    return { error: 'Server error checking user role.', status: 500 };
  }

  if (!roleData || roleData.role !== 'admin') {
    // This case should ideally be caught by PGRST116, but as a fallback
    console.warn(`Admin auth attempt by user without admin role in DB: ${user.email} (${user.id})`);
    return { error: 'Forbidden: User does not have administrator privileges.', status: 403 };
  }

  console.log(`Admin authenticated: ${user.email} (${user.id})`);
  return { adminUserId: user.id };
}

// Define the expected shape of the data after the Supabase query
interface MessageWithJoins {
  id: string;
  subject?: string | null;
  body: string;
  is_from_client: boolean;
  created_at: string;
  client_user_id?: string | null;
  company_id: string;
  admin_user_id?: string | null;
  parent_message_id?: string | null;
  // Joined fields - these can be null if the join fails or no related record exists
  company: { name: string } | null;
  client_user: { name: string | null; email: string | null } | null; // client_users might have name or email or both
  admin_user: { email: string | null } | null; // auth.users typically has email
  // Include any other fields selected from client_messages with '*'
  updated_at?: string | null; // Example, assuming it exists
  read_at?: string | null;    // Example, assuming it exists
}

export async function GET(request: NextRequest) {
  // const supabaseService = getSupabaseServiceRoleClient(); // Service client for data fetching AFTER auth
  try {
    const authResult = await authenticateAdmin(request); // Auth check uses route handler client
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const supabaseService = getSupabaseServiceRoleClient(); // Use service role for data fetching now

    const { data, error: messagesError } = await supabaseService
      .from('client_messages')
      .select(`
        *,
        company:companies(name),
        client_user:client_users(name, email),
        admin_user:auth.users(email) 
      `)
      .order('created_at', { ascending: false });

    if (messagesError) {
      console.error('Error fetching messages for admin:', messagesError);
      return NextResponse.json({ error: 'Failed to fetch messages', details: messagesError.message }, { status: 500 });
    }

    // Cast the data to our expected type via unknown
    const messages = (data as unknown as MessageWithJoins[]) || [];

    const transformedMessages = messages.map(msg => ({
      id: msg.id,
      subject: msg.subject,
      body: msg.body,
      is_from_client: msg.is_from_client,
      created_at: msg.created_at,
      client_user_id: msg.client_user_id,
      company_id: msg.company_id,
      admin_user_id: msg.admin_user_id,
      parent_message_id: msg.parent_message_id,
      updated_at: msg.updated_at,
      read_at: msg.read_at,
      company_name: msg.company?.name,
      client_user_name: msg.client_user?.name || msg.client_user?.email,
      admin_user_name: msg.admin_user?.email,
    }));

    return NextResponse.json(transformedMessages);

  } catch (e: unknown) {
    const errorMessage = e instanceof Error ? e.message : 'An unknown server error occurred';
    console.error('Unexpected error in GET /api/admin/messages:', e);
    return NextResponse.json({ error: 'An unexpected server error occurred', details: errorMessage }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  // const supabaseService = getSupabaseServiceRoleClient(); // Service client for data fetching AFTER auth
  try {
    const authResult = await authenticateAdmin(request); // Auth check uses route handler client
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }
    const { adminUserId } = authResult;

    const supabaseService = getSupabaseServiceRoleClient(); // Use service role for data fetching now

    const bodyData = await request.json(); // Renamed 'body' to 'bodyData' to avoid conflict
    const { subject, messageBody, company_id, client_user_id, parent_message_id } = bodyData;

    if (!messageBody || typeof messageBody !== 'string' || messageBody.trim() === '') {
      return NextResponse.json({ error: 'Message body is required.' }, { status: 400 });
    }
    if (!company_id || typeof company_id !== 'string') {
      return NextResponse.json({ error: 'Company ID is required for context.' }, { status: 400 });
    }
    // client_user_id might be optional if an admin is initiating a new thread not tied to a specific client message.
    // parent_message_id is also optional.

    const messageToInsert = {
      company_id: company_id,
      client_user_id: client_user_id || null, // Can be null if admin starts a new thread
      admin_user_id: adminUserId,
      subject: subject ? String(subject).trim() : null,
      body: messageBody.trim(),
      is_from_client: false,
      parent_message_id: parent_message_id || null,
      // created_at and updated_at will be handled by Supabase defaults/triggers
    };

    const { data, error: insertError } = await supabaseService
      .from('client_messages')
      .insert(messageToInsert)
      .select(`
        *,
        company:companies(name),
        client_user:client_users(name, email),
        admin_user:auth.users(email)
      `)
      .single();

    if (insertError) {
      console.error('Error sending admin message:', insertError);
      return NextResponse.json({ error: 'Failed to send message', details: insertError.message }, { status: 500 });
    }
    
    // Cast the single returned message as well, via unknown
    const newMessage = data as unknown as MessageWithJoins | null;

    if (!newMessage) {
        // Should not happen if insert was successful and select().single() was used,
        // but good to handle for type safety.
        console.error('Failed to retrieve the newly inserted message after insert.');
        return NextResponse.json({ error: 'Failed to confirm message creation' }, { status: 500 });
    }

    const transformedNewMessage = {
        id: newMessage.id,
        subject: newMessage.subject,
        body: newMessage.body,
        is_from_client: newMessage.is_from_client,
        created_at: newMessage.created_at,
        client_user_id: newMessage.client_user_id,
        company_id: newMessage.company_id,
        admin_user_id: newMessage.admin_user_id,
        parent_message_id: newMessage.parent_message_id,
        updated_at: newMessage.updated_at,
        read_at: newMessage.read_at,
        company_name: newMessage.company?.name,
        client_user_name: newMessage.client_user?.name || newMessage.client_user?.email,
        admin_user_name: newMessage.admin_user?.email,
      };

    return NextResponse.json(transformedNewMessage, { status: 201 });

  } catch (e: unknown) {
    if (e instanceof SyntaxError) {
        return NextResponse.json({ error: 'Invalid request body: Malformed JSON.' }, { status: 400 });
    }
    const errorMessage = e instanceof Error ? e.message : 'An unknown server error occurred';
    console.error('Unexpected error in POST /api/admin/messages:', e);
    return NextResponse.json({ error: 'An unexpected server error occurred', details: errorMessage }, { status: 500 });
  }
} 