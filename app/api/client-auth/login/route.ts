import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY as string

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Verify client credentials
    console.log('Attempting to verify credentials for email:', email)
    const { data, error } = await supabase.rpc('verify_client_credentials', {
      p_email: email,
      p_password: password
    })

    console.log('Verification result:', { data, error })

    if (error || !data) {
      console.error('Authentication error:', error)
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      )
    }

    // Get client user details
    const { data: userData, error: userError } = await supabase
      .from('client_users')
      .select('id, company_id, email, is_active')
      .eq('email', email)
      .single()

    if (userError || !userData) {
      console.error('User fetch error:', userError)
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if user is active
    if (!userData.is_active) {
      return NextResponse.json(
        { error: 'Your account has been deactivated. Please contact support.' },
        { status: 403 }
      )
    }

    // Generate a session token
    const sessionToken = crypto.randomUUID()
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7) // 7 days from now

    // Store the session
    const { error: sessionError } = await supabase
      .from('client_sessions')
      .insert({
        token: sessionToken,
        user_id: userData.id,
        company_id: userData.company_id,
        expires_at: expiresAt.toISOString()
      })

    if (sessionError) {
      console.error('Session creation error:', sessionError)
      return NextResponse.json(
        { error: 'Failed to create session' },
        { status: 500 }
      )
    }

    // Update last login timestamp
    await supabase
      .from('client_users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', userData.id)

    // Set session cookie
    await cookies().set({
      name: 'client_session',
      value: sessionToken,
      expires: expiresAt,
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    })

    return NextResponse.json({
      success: true,
      companyId: userData.company_id
    })
  } catch (err) {
    console.error('Unexpected error during client login:', err)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
