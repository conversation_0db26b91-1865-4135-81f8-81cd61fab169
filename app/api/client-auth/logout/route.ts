import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies()
    const sessionCookie = await cookieStore.get('client_session')
    const sessionToken = sessionCookie?.value

    if (!sessionToken) {
      return NextResponse.json({ success: true })
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY as string

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Delete the session from the database
    await supabase
      .from('client_sessions')
      .delete()
      .eq('token', sessionToken)

    // Clear the session cookie
    await cookies().delete('client_session')

    return NextResponse.json({ success: true })
  } catch (err) {
    console.error('Unexpected error during client logout:', err)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
