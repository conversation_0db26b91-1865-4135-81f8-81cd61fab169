import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Check if any users exist in user_roles
    const { count, error } = await supabase
      .from('user_roles')
      .select('*', { count: 'exact', head: true })
    
    if (error) {
      console.error('Error checking user count:', error)
      return NextResponse.json({ error: 'Failed to check setup status' }, { status: 500 })
    }
    
    // If no users exist in user_roles, setup is allowed
    return NextResponse.json({ setupAllowed: count === 0 })
  } catch (err) {
    console.error('Unexpected error checking setup status:', err)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
