import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    const { email, password } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Check if setup is allowed (no users exist)
    const { count, error: countError } = await supabase
      .from('user_roles')
      .select('*', { count: 'exact', head: true })

    if (countError) {
      console.error('Error checking user count:', countError)
      return NextResponse.json(
        { error: 'Failed to check setup status' },
        { status: 500 }
      )
    }

    // If users already exist, setup is not allowed
    if (count && count > 0) {
      console.error('Unauthorized setup attempt - admin already exists')
      return NextResponse.json(
        { error: 'Setup has already been completed. Please sign in with an existing account.' },
        { status: 403 }
      )
    }

    // Double-check with a direct query to be extra safe
    const { data: existingAdmins, error: adminError } = await supabase
      .from('user_roles')
      .select('id')
      .eq('role', 'admin')
      .limit(1)

    if (adminError) {
      console.error('Error checking admin existence:', adminError)
      return NextResponse.json(
        { error: 'Failed to verify setup status' },
        { status: 500 }
      )
    }

    if (existingAdmins && existingAdmins.length > 0) {
      console.error('Unauthorized setup attempt - admin already exists (secondary check)')
      return NextResponse.json(
        { error: 'Setup has already been completed. Please sign in with an existing account.' },
        { status: 403 }
      )
    }

    // Create the admin user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
    })

    if (authError) {
      return NextResponse.json(
        { error: authError.message },
        { status: 500 }
      )
    }

    if (!authData.user) {
      return NextResponse.json(
        { error: 'Failed to create user' },
        { status: 500 }
      )
    }

    // Add the user to the user_roles table as an admin
    const { error: insertError } = await supabase
      .from('user_roles')
      .insert({
        user_id: authData.user.id,
        role: 'admin',
        created_at: new Date().toISOString()
      })

    if (insertError) {
      return NextResponse.json(
        { error: 'Failed to create admin record' },
        { status: 500 }
      )
    }

    // Sign in the user
    const { error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (signInError) {
      return NextResponse.json(
        { error: 'Account created but failed to sign in' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (err) {
    console.error('Unexpected error during setup:', err)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
