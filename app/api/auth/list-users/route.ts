import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Check if the user is authenticated
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    // Check if the user is an admin
    const { data: userRole, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', session.user.id)
      .single()
    
    if (roleError || userRole?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      )
    }
    
    // Get user roles
    const { data: roleData, error: roleDataError } = await supabase
      .from('user_roles')
      .select('user_id, role, created_at')
      .order('created_at')
    
    if (roleDataError) {
      console.error('Error fetching user roles:', roleDataError)
      return NextResponse.json(
        { error: 'Failed to load users' },
        { status: 500 }
      )
    }
    
    // Get user profiles from auth.users
    const adminClient = createRouteHandlerClient({ cookies })
    
    // Get all users from auth.users
    const { data: users, error: usersError } = await adminClient.auth.admin.listUsers()
    
    if (usersError) {
      console.error('Error fetching auth users:', usersError)
      return NextResponse.json(
        { error: 'Failed to load user details' },
        { status: 500 }
      )
    }
    
    // Combine the data
    const combinedUsers = users.users.map(user => {
      const roleInfo = roleData?.find(r => r.user_id === user.id) || { role: 'unknown' }
      return {
        id: user.id,
        email: user.email,
        role: roleInfo.role,
        created_at: user.created_at,
        last_sign_in_at: user.last_sign_in_at,
      }
    })
    
    return NextResponse.json({ users: combinedUsers })
  } catch (err) {
    console.error('Unexpected error listing users:', err)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
