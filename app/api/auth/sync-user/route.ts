import { NextRequest, NextResponse } from 'next/server'
import { createAdminClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user from Supabase
    const supabase = createAdminClient()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const userId = session.user.id
    const userEmail = session.user.email

    if (!userEmail) {
      return NextResponse.json(
        { error: 'User email not found' },
        { status: 400 }
      )
    }

    // Check if user exists in the user_roles table
    const { data: existingUser, error: userError } = await supabase
      .from('user_roles')
      .select('user_id, role')
      .eq('user_id', userId)
      .single()

    if (userError && userError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
      console.error('Error checking user:', userError)
      return NextResponse.json(
        { error: 'Database error' },
        { status: 500 }
      )
    }

    if (!existingUser) {
      // By default, make the first user an admin
      const { count } = await supabase
        .from('user_roles')
        .select('*', { count: 'exact', head: true })

      // Create new user record
      const { error: insertError } = await supabase
        .from('user_roles')
        .insert({
          user_id: userId,
          role: count === 0 ? 'admin' : 'user', // First user is admin
          created_at: new Date().toISOString()
        })

      if (insertError) {
        console.error('Error creating user record:', insertError)
        return NextResponse.json(
          { error: 'Failed to create user record' },
          { status: 500 }
        )
      }
    }

    return NextResponse.json({ success: true })
  } catch (err) {
    console.error('Unexpected error during user sync:', err)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
