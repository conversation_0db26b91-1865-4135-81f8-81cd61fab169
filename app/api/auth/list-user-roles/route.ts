import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Check if the user is authenticated
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    // Check if the user is an admin
    const { data: userRole, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', session.user.id)
      .single()
    
    if (roleError || userRole?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      )
    }
    
    // Get user roles
    const { data: users, error: usersError } = await supabase
      .from('user_roles')
      .select(`
        user_id,
        role,
        users (
          email
        )
      `)
      .order('created_at')
    
    if (usersError) {
      console.error('Error fetching user roles:', usersError)
      return NextResponse.json(
        { error: 'Failed to load users' },
        { status: 500 }
      )
    }
    
    // Format the response
    const formattedUsers = users.map(user => ({
      id: user.user_id,
      email: user.users?.email || 'Unknown',
      is_admin: user.role === 'admin'
    }))
    
    return NextResponse.json({ users: formattedUsers })
  } catch (err) {
    console.error('Unexpected error listing users:', err)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
