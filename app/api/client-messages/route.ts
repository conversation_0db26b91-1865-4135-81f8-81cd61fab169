import { NextRequest, NextResponse } from 'next/server'
import { createClient, SupabaseClient } from '@supabase/supabase-js'

// Helper function to get Supabase client with service role
function getSupabaseServiceRoleClient(): SupabaseClient {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Missing Supabase environment variables for service role client')
    throw new Error('Server configuration error for Supabase client');
  }
  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: { persistSession: false }
  });
}

interface ValidationResult {
  error: string | null;
  status: number;
  clientUserId: string | null;
  companyId: string | null;
  shouldClearCookie?: boolean; // New flag to signal cookie clearing
}

// Helper function to validate session and set RLS variables
async function validateSessionAndSetRLS(request: NextRequest, supabase: SupabaseClient): Promise<ValidationResult> {
  const sessionToken = request.cookies.get('client_session')?.value;

  if (!sessionToken) {
    return { error: 'Unauthorized: No session token', status: 401, clientUserId: null, companyId: null };
  }

  const { data: sessionData, error: sessionError } = await supabase
    .from('client_sessions')
    .select('user_id, company_id, expires_at')
    .eq('token', sessionToken)
    .single();

  if (sessionError || !sessionData) {
    console.error('Session verification error:', sessionError);
    return { error: 'Invalid session', status: 401, clientUserId: null, companyId: null, shouldClearCookie: true };
  }

  if (new Date(sessionData.expires_at) < new Date()) {
    console.log('Session expired for token:', sessionToken);
    // The calling handler will be responsible for deleting the DB record if needed, 
    // but middleware should ideally handle this. For now, just signal cookie clear.
    // await supabase.from('client_sessions').delete().eq('token', sessionToken);
    return { error: 'Session expired', status: 401, clientUserId: null, companyId: null, shouldClearCookie: true };
  }

  const { error: rlsError } = await supabase.rpc('set_session_vars', {
    client_user_id_var: sessionData.user_id,
    company_id_var: sessionData.company_id
  });

  if (rlsError) {
    console.error('Failed to set RLS session variables:', rlsError);
    return { error: 'Server configuration error while setting RLS', status: 500, clientUserId: null, companyId: null };
  }

  return { error: null, status: 200, clientUserId: sessionData.user_id, companyId: sessionData.company_id };
}

export async function GET(request: NextRequest) {
  const supabase = getSupabaseServiceRoleClient();
  try {
    const validation = await validateSessionAndSetRLS(request, supabase);
    if (validation.error) {
      const response = NextResponse.json({ error: validation.error }, { status: validation.status });
      if (validation.shouldClearCookie) {
        response.cookies.set('client_session', '', { path: '/', maxAge: 0 });
      }
      return response;
    }

    const { data: messages, error: messagesError } = await supabase
      .from('client_messages')
      .select('*') 
      .order('created_at', { ascending: true });

    if (messagesError) {
      console.error('Error fetching client messages:', messagesError);
      return NextResponse.json({ error: 'Failed to fetch messages', details: messagesError.message }, { status: 500 });
    }

    return NextResponse.json(messages);

  } catch (e: unknown) {
    console.error('Unexpected error in GET /api/client-messages:', e);
    const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'An unexpected server error occurred', details: errorMessage }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  const supabase = getSupabaseServiceRoleClient();
  try {
    const validation = await validateSessionAndSetRLS(request, supabase);
    if (validation.error || !validation.clientUserId || !validation.companyId) {
      const response = NextResponse.json({ error: validation.error || 'User or company ID missing after auth' }, { status: validation.status || 401 });
      if (validation.shouldClearCookie) {
        response.cookies.set('client_session', '', { path: '/', maxAge: 0 });
      }
      return response;
    }
    
    const { clientUserId, companyId } = validation; // Destructure after ensuring they exist

    const body = await request.json();
    const { subject, messageBody: msgBody } = body;

    if (!msgBody || typeof msgBody !== 'string' || msgBody.trim() === '') {
      return NextResponse.json({ error: 'Message body is required and cannot be empty.' }, { status: 400 });
    }
    if (subject && typeof subject !== 'string') {
      return NextResponse.json({ error: 'Subject must be a string if provided.' }, { status: 400 });
    }

    const messageToInsert = {
      company_id: companyId,
      client_user_id: clientUserId,
      subject: subject ? subject.trim() : null,
      body: msgBody.trim(),
      is_from_client: true,
    };
    
    const { data: newMessage, error: insertError } = await supabase
      .from('client_messages')
      .insert(messageToInsert)
      .select()
      .single();

    if (insertError) {
      console.error('Error inserting client message:', insertError);
      return NextResponse.json({ error: 'Failed to send message', details: insertError.message }, { status: 500 });
    }

    return NextResponse.json(newMessage, { status: 201 });

  } catch (e: unknown) {
    if (e instanceof SyntaxError) {
        return NextResponse.json({ error: 'Invalid request body: Malformed JSON.' }, { status: 400 });
    }
    console.error('Unexpected error in POST /api/client-messages:', e);
    const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'An unexpected server error occurred', details: errorMessage }, { status: 500 });
  }
} 