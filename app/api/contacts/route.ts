import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function GET() {
  try {
    console.log('API - Fetching contacts data')

    // Create a Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          async get(name) {
            const cookie = cookieStore.get(name)
            return cookie?.value
          },
          async set(name, value, options) {
            cookieStore.set(name, value, options)
          },
          async remove(name, options) {
            cookieStore.set(name, '', { ...options, maxAge: 0 })
          },
        },
      }
    )

    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      console.error('API - No authenticated session')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Fetch contacts
    console.log('API - About to fetch contacts')
    const { data, error } = await supabase
      .from('contacts')
      .select('*')
      .order('name')

    console.log('API - Raw contacts query result:', { data, error })

    if (error) {
      console.error('API - Contacts fetch error:', error)
      return NextResponse.json(
        { error: 'Failed to fetch contacts data' },
        { status: 500 }
      )
    }

    console.log('API - Successfully fetched contacts data:', data?.length)
    console.log('API - First few contacts:', data?.slice(0, 2))
    return NextResponse.json({ contacts: data || [] })
  } catch (err) {
    console.error('API - Unexpected error fetching contacts data:', err)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
