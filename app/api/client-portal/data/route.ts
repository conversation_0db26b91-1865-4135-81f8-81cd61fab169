import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('client_session')
    const sessionToken = sessionCookie?.value

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get the company ID from the query parameters
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY as string

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Verify the session
    const { data: sessionData, error: sessionError } = await supabase
      .from('client_sessions')
      .select('company_id, user_id, expires_at')
      .eq('token', sessionToken)
      .single()

    if (sessionError || !sessionData) {
      console.error('Session verification error:', sessionError)
      return NextResponse.json(
        { error: 'Invalid session' },
        { status: 401 }
      )
    }

    // Check if session is expired
    if (new Date(sessionData.expires_at) < new Date()) {
      // Delete the expired session
      await supabase
        .from('client_sessions')
        .delete()
        .eq('token', sessionToken)

      return NextResponse.json(
        { error: 'Session expired' },
        { status: 401 }
      )
    }

    // Check if the requested company matches the session company
    if (sessionData.company_id !== companyId) {
      return NextResponse.json(
        { error: 'Unauthorized access to this company' },
        { status: 403 }
      )
    }

    // Fetch company details
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('id, name, industry, status, address, website, notes')
      .eq('id', companyId)
      .single()

    if (companyError) {
      console.error('Company fetch error:', companyError)
      return NextResponse.json(
        { error: 'Failed to fetch company data' },
        { status: 500 }
      )
    }

    // Fetch projects
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id, name, description, status, start_date, expected_end_date')
      .eq('company_id', companyId)
      .order('created_at', { ascending: false })

    if (projectsError) {
      console.error('[API /client-portal/data] Projects fetch error:', projectsError); // Keeping this error log for now
    }

    // Fetch active tasks for all projects
    let tasks: any[] = []
    if (projects && projects.length > 0) {
      const projectIds = projects.map(project => project.id)

      const { data: tasksData, error: tasksError } = await supabase
        .from('tasks')
        .select(`
          id,
          name,
          status,
          priority,
          due_date,
          project:project_id (id, name)
        `)
        .in('project_id', projectIds)
        .not('status', 'eq', 'completed')
        .not('status', 'eq', 'cancelled')
        .order('due_date', { ascending: true })
        .limit(10)

      if (tasksError) {
        console.error('Tasks fetch error:', tasksError)
      } else {
        tasks = tasksData || []
      }
    }

    // Fetch credentials
    const { data: credentials, error: credentialsError } = await supabase
      .from('credentials')
      .select('id, name, credential_type, username, url, notes')
      .eq('company_id', companyId)
      .order('name')

    if (credentialsError) {
      console.error('Credentials fetch error:', credentialsError)
    }

    // Fetch invoices
    const { data: invoices, error: invoicesError } = await supabase
      .from('invoices')
      .select('id, invoice_number, amount, status, issue_date, due_date')
      .eq('company_id', companyId)
      .order('issue_date', { ascending: false })

    if (invoicesError) {
      console.error('Invoices fetch error:', invoicesError)
    }

    // Fetch contacts
    const { data: contacts, error: contactsError } = await supabase
      .from('contacts')
      .select('id, name, email, phone, position')
      .eq('company_id', companyId)
      .order('name')

    if (contactsError) {
      console.error('Contacts fetch error:', contactsError)
    }

    // Fetch expenses
    const { data: expenses, error: expensesError } = await supabase
      .from('expenses')
      .select('id, name, amount, category, date, payment_method, is_reimbursable, project_id')
      .eq('company_id', companyId)
      .order('date', { ascending: false })

    if (expensesError) {
      console.error('Expenses fetch error:', expensesError)
    }

    // Return all the data
    return NextResponse.json({
      company,
      projects: projects || [],
      tasks: tasks || [],
      credentials: credentials || [],
      invoices: invoices || [],
      contacts: contacts || [],
      expenses: expenses || []
    })
  } catch (err) {
    console.error('Unexpected error fetching client portal data:', err)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
