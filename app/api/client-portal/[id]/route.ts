import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the ID from params
    const id = params.id
    console.log('API - Fetching client portal data for ID:', id)

    if (!id) {
      console.error('API - Invalid ID: ID is empty or undefined')
      return NextResponse.json(
        { error: 'Invalid client ID' },
        { status: 400 }
      )
    }

    // Create a Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          async get(name) {
            const cookie = cookieStore.get(name)
            return cookie?.value
          },
          async set(name, value, options) {
            cookieStore.set(name, value, options)
          },
          async remove(name, options) {
            cookieStore.set(name, '', { ...options, maxAge: 0 })
          },
        },
      }
    )

    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      console.error('API - No authenticated session')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Fetch company details
    console.log('API - Fetching company data')
    const { data: companyData, error: companyError } = await supabase
      .from('companies')
      .select('*')
      .eq('id', id)
      .single()

    if (companyError) {
      console.error('API - Company fetch error:', companyError)
      return NextResponse.json(
        { error: 'Failed to fetch company data' },
        { status: 500 }
      )
    }

    if (!companyData) {
      console.error('API - No company found with ID:', id)
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Fetch projects
    console.log('API - Fetching projects data')
    const { data: projectsData, error: projectsError } = await supabase
      .from('projects')
      .select('id, name, description, status, start_date, expected_end_date')
      .eq('company_id', id)
      .order('created_at', { ascending: false })

    if (projectsError) {
      console.error('API - Projects fetch error:', projectsError)
      return NextResponse.json(
        { error: 'Failed to fetch projects data' },
        { status: 500 }
      )
    }

    // Fetch tasks
    let tasksData = []
    if (projectsData && projectsData.length > 0) {
      console.log('API - Fetching tasks data')
      const projectIds = projectsData.map(project => project.id)

      const { data: tasks, error: tasksError } = await supabase
        .from('tasks')
        .select(`
          id,
          name,
          status,
          priority,
          due_date,
          project:project_id (id, name)
        `)
        .in('project_id', projectIds)
        .not('status', 'eq', 'completed')
        .not('status', 'eq', 'cancelled')
        .order('due_date', { ascending: true })
        .limit(5)

      if (tasksError) {
        console.error('API - Tasks fetch error:', tasksError)
      } else {
        tasksData = tasks || []
      }
    }

    // Fetch credentials
    console.log('API - Fetching credentials data')
    const { data: credentialsData, error: credentialsError } = await supabase
      .from('credentials_with_type')
      .select('id, name, type, username, url, notes')
      .eq('company_id', id)
      .order('name')

    if (credentialsError) {
      console.error('API - Credentials fetch error:', credentialsError)
      return NextResponse.json(
        { error: 'Failed to fetch credentials data' },
        { status: 500 }
      )
    }

    // Fetch invoices
    console.log('API - Fetching invoices data')
    const { data: invoicesData, error: invoicesError } = await supabase
      .from('invoices')
      .select('id, invoice_number, amount, status, issue_date, due_date')
      .eq('company_id', id)
      .order('issue_date', { ascending: false })

    if (invoicesError) {
      console.error('API - Invoices fetch error:', invoicesError)
      return NextResponse.json(
        { error: 'Failed to fetch invoices data' },
        { status: 500 }
      )
    }

    // Fetch contacts
    console.log('API - Fetching contacts data')
    const { data: contactsData, error: contactsError } = await supabase
      .from('contacts')
      .select('id, name, email, phone, position')
      .eq('company_id', id)
      .order('name')

    if (contactsError) {
      console.error('API - Contacts fetch error:', contactsError)
      return NextResponse.json(
        { error: 'Failed to fetch contacts data' },
        { status: 500 }
      )
    }

    // Return all the data
    console.log('API - Successfully fetched all client portal data')
    return NextResponse.json({
      company: companyData,
      projects: projectsData || [],
      tasks: tasksData || [],
      credentials: credentialsData || [],
      invoices: invoicesData || [],
      contacts: contactsData || []
    })
  } catch (err) {
    console.error('API - Unexpected error fetching client portal data:', err)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
