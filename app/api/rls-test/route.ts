import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'

export const dynamic = 'force-dynamic' // Ensure this is not cached

export async function GET() {
  try {
    console.log('API - Running RLS test')

    // Create a Supabase client with the user's session
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          async get(name) {
            const cookie = cookieStore.get(name)
            return cookie?.value
          },
          async set(name, value, options) {
            cookieStore.set(name, value, options)
          },
          async remove(name, options) {
            cookieStore.set(name, '', { ...options, maxAge: 0 })
          },
        },
      }
    )

    // Create a service role client (bypasses RLS)
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      console.error('API - No authenticated session')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get user info
    const userId = session.user.id
    const userEmail = session.user.email

    console.log(`API - Running RLS test for user: ${userEmail} (${userId})`)

    // Check if user is admin
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select('is_admin')
      .eq('id', userId)
      .single()

    const isAdmin = userData?.is_admin || false

    console.log(`API - User is admin: ${isAdmin}`)

    // Test tables with both clients
    const tables = ['companies', 'projects', 'contacts', 'credentials', 'invoices']
    const results: Record<string, any> = {}

    for (const table of tables) {
      console.log(`API - Testing table: ${table}`)

      // Test with user's client (subject to RLS)
      const { data: userClientData, error: userClientError } = await supabase
        .from(table)
        .select('id')
        .limit(10)

      // Test with admin client (bypasses RLS)
      const { data: adminClientData, error: adminClientError } = await supabaseAdmin
        .from(table)
        .select('id')
        .limit(10)

      results[table] = {
        userClient: {
          count: userClientData?.length || 0,
          error: userClientError ? userClientError.message : null,
        },
        adminClient: {
          count: adminClientData?.length || 0,
          error: adminClientError ? adminClientError.message : null,
        },
        hasRlsIssue: !userClientError &&
                     !adminClientError &&
                     adminClientData?.length > 0 &&
                     userClientData?.length === 0
      }

      console.log(`API - ${table} results:`, results[table])
    }

    // Check for RLS policies
    const { data: rlsPolicies, error: rlsError } = await supabaseAdmin
      .rpc('get_policies')

    if (rlsError) {
      console.error('API - Error fetching RLS policies:', rlsError)
    } else {
      console.log('API - RLS policies:', rlsPolicies)
    }

    return NextResponse.json({
      user: {
        id: userId,
        email: userEmail,
        isAdmin
      },
      tables: results,
      rlsPolicies: rlsPolicies || []
    })
  } catch (err) {
    console.error('API - Unexpected error in RLS test:', err)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
