-- Function to verify client credentials
CREATE OR REPLACE FUNCTION verify_client_credentials(
  p_email TEXT,
  p_password TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user RECORD;
  v_hash TEXT;
BEGIN
  -- Look up the user
  SELECT * INTO v_user
  FROM public.client_users
  WHERE email = p_email
  AND is_active = true;
  
  -- If no user found or user is not active, return false
  IF v_user.id IS NULL THEN
    RETURN false;
  END IF;
  
  -- Hash the provided password with the stored salt
  v_hash := encode(
    digest(
      p_password || v_user.password_salt,
      'sha512'
    ),
    'hex'
  );
  
  -- Compare the computed hash with the stored hash
  IF v_hash = v_user.password_hash THEN
    RETURN true;
  ELSE
    RETURN false;
  END IF;
END;
$$;

-- Function to create the verify_client_credentials function if it's not correctly set up
CREATE OR REPLACE FUNCTION create_verify_client_credentials_func()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- The actual function is created separately, so this is just a placeholder
  -- to handle the migrations for the authentication system
  
  -- Ensure the pgcrypto extension is installed for password hashing
  CREATE EXTENSION IF NOT EXISTS pgcrypto;
  
  -- We would normally check if the function exists, but we're just going to replace it
  -- which is handled by the CREATE OR REPLACE above
END;
$$; 