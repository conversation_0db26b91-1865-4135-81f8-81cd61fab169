-- Function to create client_sessions table if it doesn't exist
CREATE OR REPLACE FUNCTION create_client_sessions_if_not_exists()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Check if the table exists
  IF NOT EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public'
    AND tablename = 'client_sessions'
  ) THEN
    -- Create the client_sessions table
    CREATE TABLE public.client_sessions (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      token TEXT NOT NULL UNIQUE,
      user_id UUID NOT NULL REFERENCES public.client_users(id) ON DELETE CASCADE,
      company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
      created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      expires_at TIMESTAMPTZ NOT NULL,
      ip_address TEXT,
      user_agent TEXT
    );

    -- Add RLS policies
    ALTER TABLE public.client_sessions ENABLE ROW LEVEL SECURITY;
    
    -- Create policies
    CREATE POLICY "<PERSON><PERSON> can do everything with client_sessions"
      ON public.client_sessions
      FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.users
          WHERE users.id = auth.uid()
          AND (users.role = 'admin' OR users.role = 'owner')
        )
      );
      
    -- Create indexes
    CREATE INDEX idx_client_sessions_user_id ON public.client_sessions(user_id);
    CREATE INDEX idx_client_sessions_company_id ON public.client_sessions(company_id);
    CREATE INDEX idx_client_sessions_token ON public.client_sessions(token);
    CREATE INDEX idx_client_sessions_expires_at ON public.client_sessions(expires_at);
    
    -- Add comment
    COMMENT ON TABLE public.client_sessions IS 'Stores session information for client portal users';
  END IF;
END;
$$; 