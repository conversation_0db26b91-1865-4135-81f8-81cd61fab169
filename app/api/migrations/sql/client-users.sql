-- Function to create client_users table if it doesn't exist
CREATE OR REPLACE FUNCTION create_client_users_if_not_exists()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Check if the table exists
  IF NOT EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public'
    AND tablename = 'client_users'
  ) THEN
    -- Create the client_users table
    CREATE TABLE public.client_users (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
      email VARCHAR(255) NOT NULL UNIQUE,
      name VARCHAR(255) NOT NULL,
      password_hash TEXT NOT NULL,
      password_salt TEXT NOT NULL,
      is_active BOOLEAN NOT NULL DEFAULT true,
      created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMPTZ,
      last_login TIMESTAMPTZ,
      created_by UUID REFERENCES public.users(id) ON DELETE SET NULL
    );

    -- Add RLS policies
    ALTER TABLE public.client_users ENABLE ROW LEVEL SECURITY;
    
    -- Create policies
    CREATE POLICY "Admins can do everything with client_users"
      ON public.client_users
      FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.users
          WHERE users.id = auth.uid()
          AND (users.role = 'admin' OR users.role = 'owner')
        )
      );
      
    -- Create indexes
    CREATE INDEX idx_client_users_company_id ON public.client_users(company_id);
    CREATE INDEX idx_client_users_email ON public.client_users(email);
    
    -- Add comment
    COMMENT ON TABLE public.client_users IS 'Stores authentication information for client portal users';
  END IF;
END;
$$; 