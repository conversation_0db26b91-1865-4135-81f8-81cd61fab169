import { NextRequest, NextResponse } from 'next/server'
import { createClient, SupabaseClient } from '@supabase/supabase-js'

// Helper function to verify admin status
async function verifyAdmin(supabase: SupabaseClient, userId: string): Promise<boolean> {
  // First try to use the database function we created
  try {
    const { data: funcResult, error: funcError } = await supabase
      .rpc('verify_admin_status', { user_id: userId })

    if (!funcError && funcResult !== null) {
      return funcResult
    }
  } catch (err) {
    console.log('Error using verify_admin_status function, falling back to direct query:', err)
  }

  // Fallback to direct query on user_roles table
  const { data, error } = await supabase
    .from('user_roles')
    .select('role')
    .eq('user_id', userId)
    .single()

  if (error || !data) {
    console.error('Error checking admin status:', error)
    return false
  }

  return data.role === 'admin' || data.role === 'owner'
}

// Helper function to get user ID from authorization header
async function getUserIdFromAuth(request: NextRequest): Promise<string | null> {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.error('No valid Authorization header found')
      return null
    }

    // Extract the token
    const token = authHeader.split(' ')[1]

    if (!token) {
      console.error('No token in Authorization header')
      return null
    }

    // Initialize Supabase admin client to verify the token
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY as string

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase configuration')
      return null
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Verify the token and get user information
    const { data, error } = await supabase.auth.getUser(token)

    if (error || !data.user) {
      console.error('Error verifying token:', error)
      return null
    }

    return data.user.id
  } catch (err) {
    console.error('Error getting user ID from token:', err)
    return null
  }
}

// POST: Run the database migrations for client portal authentication
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY as string

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user ID from authorization header
    const userId = await getUserIdFromAuth(request)

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized: Invalid or missing authentication token' },
        { status: 401 }
      )
    }

    // Verify admin status
    const isAdmin = await verifyAdmin(supabase, userId)

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      )
    }

    // Run the migrations - create the necessary tables if they don't exist

    // Create all tables and functions in a single SQL query
    try {
      // Execute SQL directly using the Supabase REST API
      const url = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/sql`;
      const sqlQuery = `
              -- 1. Create client_users table if it doesn't exist
              CREATE TABLE IF NOT EXISTS public.client_users (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
                username TEXT NOT NULL,
                email TEXT,
                password_hash TEXT NOT NULL,
                is_active BOOLEAN NOT NULL DEFAULT true,
                created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMPTZ,
                last_login TIMESTAMPTZ
              );

              -- Add RLS policies if the table was just created
              DO $$
              BEGIN
                IF NOT EXISTS (
                  SELECT 1 FROM pg_policies
                  WHERE tablename = 'client_users' AND schemaname = 'public'
                ) THEN
                  ALTER TABLE public.client_users ENABLE ROW LEVEL SECURITY;

                  CREATE POLICY "Admins can do everything with client_users"
                    ON public.client_users
                    FOR ALL
                    USING (
                      EXISTS (
                        SELECT 1 FROM public.user_roles
                        WHERE user_roles.user_id = auth.uid()
                        AND (user_roles.role = 'admin' OR user_roles.role = 'owner')
                      )
                    );
                END IF;
              END
              $$;

              -- 2. Create client_sessions table if it doesn't exist
              CREATE TABLE IF NOT EXISTS public.client_sessions (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                token TEXT NOT NULL UNIQUE,
                user_id UUID NOT NULL REFERENCES public.client_users(id) ON DELETE CASCADE,
                company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
                created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                expires_at TIMESTAMPTZ NOT NULL,
                ip_address TEXT,
                user_agent TEXT
              );

              -- Add RLS policies if the table was just created
              DO $$
              BEGIN
                IF NOT EXISTS (
                  SELECT 1 FROM pg_policies
                  WHERE tablename = 'client_sessions' AND schemaname = 'public'
                ) THEN
                  ALTER TABLE public.client_sessions ENABLE ROW LEVEL SECURITY;

                  CREATE POLICY "Admins can do everything with client_sessions"
                    ON public.client_sessions
                    FOR ALL
                    USING (
                      EXISTS (
                        SELECT 1 FROM public.user_roles
                        WHERE user_roles.user_id = auth.uid()
                        AND (user_roles.role = 'admin' OR user_roles.role = 'owner')
                      )
                    );
                END IF;
              END
              $$;

              -- 3. Create stored procedure for verifying client credentials
              -- Ensure pgcrypto extension is available
              CREATE EXTENSION IF NOT EXISTS pgcrypto;

              -- Create or replace the function
              CREATE OR REPLACE FUNCTION public.verify_client_credentials(
                p_email TEXT,
                p_password TEXT
              )
              RETURNS BOOLEAN
              LANGUAGE plpgsql
              SECURITY DEFINER
              AS $$
              DECLARE
                v_user RECORD;
                v_hash TEXT;
              BEGIN
                -- Look up the user
                SELECT * INTO v_user
                FROM public.client_users
                WHERE email = p_email
                AND is_active = true;

                -- If no user found or user is not active, return false
                IF v_user.id IS NULL THEN
                  RETURN false;
                END IF;

                -- Compare the password hash (simplified for now)
                IF v_user.password_hash = encode(digest(p_password, 'sha256'), 'hex') THEN
                  RETURN true;
                ELSE
                  RETURN false;
                END IF;
              END;
              $$;
            `;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY as string
        },
        body: JSON.stringify({ query: sqlQuery })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`SQL execution failed: ${JSON.stringify(errorData)}`);
      }
    } catch (error) {
      console.error('Error creating client portal auth tables and functions:', error);
      throw error;
    }

    return NextResponse.json({
      success: true,
      message: 'Client portal authentication migrations completed successfully'
    })
  } catch (err) {
    console.error('Migration error:', err)
    return NextResponse.json(
      { error: 'Failed to run migrations: ' + (err instanceof Error ? err.message : 'Unknown error') },
      { status: 500 }
    )
  }
}

// GET: Check if the required tables exist
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY as string

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user ID from authorization header
    const userId = await getUserIdFromAuth(request)

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized: Invalid or missing authentication token' },
        { status: 401 }
      )
    }

    console.log(`Checking migration status for user: ${userId.substring(0, 5)}...`)

    // Verify admin status
    const isAdmin = await verifyAdmin(supabase, userId)

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      )
    }

    // Use a safer approach to check if tables and functions exist
    let clientUsersExists = false;
    let clientSessionsExists = false;
    let verifyFunctionExists = false;

    // Check if client_users table exists by trying to count records
    try {
      const { error } = await supabase
        .from('client_users')
        .select('*', { count: 'exact', head: true });

      // If no error, the table exists
      clientUsersExists = !error;

      if (error && error.code !== '42P01') { // 42P01 is "relation does not exist"
        console.error('Error checking client_users table:', error);
      }
    } catch (err) {
      console.error('Exception checking client_users table:', err);
    }

    // Check if client_sessions table exists by trying to count records
    try {
      const { error } = await supabase
        .from('client_sessions')
        .select('*', { count: 'exact', head: true });

      // If no error, the table exists
      clientSessionsExists = !error;

      if (error && error.code !== '42P01') {
        console.error('Error checking client_sessions table:', error);
      }
    } catch (err) {
      console.error('Exception checking client_sessions table:', err);
    }

    // Check if verify_client_credentials function exists by trying to call it
    try {
      // Try to call the function with invalid credentials (we just want to check if it exists)
      await supabase.rpc('verify_client_credentials', {
        p_email: '<EMAIL>',
        p_password: 'invalid_password'
      });

      // If we get here without an error about the function not existing, it exists
      verifyFunctionExists = true;
    } catch (err) {
      // Check if the error is about the function not existing
      const errorMessage = err instanceof Error ? err.message : String(err);
      if (!errorMessage.includes('function') || !errorMessage.includes('does not exist')) {
        // If it's some other error, the function probably exists
        verifyFunctionExists = true;
        console.log('Function exists but returned an error:', err);
      } else {
        console.log('Function does not exist:', errorMessage);
      }
    }

    return NextResponse.json({
      clientUsersExists: clientUsersExists,
      clientSessionsExists: clientSessionsExists,
      verifyFunctionExists: verifyFunctionExists
    })
  } catch (err) {
    console.error('Error checking migration status:', err)
    return NextResponse.json(
      { error: 'Failed to check migration status: ' + (err instanceof Error ? err.message : 'Unknown error') },
      { status: 500 }
    )
  }
}