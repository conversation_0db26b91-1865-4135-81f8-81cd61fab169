'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Building, Plus, Search, Shield, Edit, Trash2 } from 'lucide-react'
import { getInitials, getStatusBadgeClass } from '@/lib/utils'
import { useSupabase } from '@/app/components/SupabaseProvider'
import ViewToggle from '@/app/components/ViewToggle'

interface Company {
  id: string
  name: string
  industry: string | null
  status: string | null
  address: string | null
  contacts?: { id: string; name: string }[]
  _count?: {
    projects: number
    contacts: number
  }
}

export default function Companies() {
  const [companies, setCompanies] = useState<Company[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'card' | 'table'>('table')
  const { supabase, user } = useSupabase()

  useEffect(() => {
    async function fetchCompanies() {
      setIsLoading(true)
      setError(null)
      console.log('Attempting to fetch companies...')

      try {
        if (!user) {
          console.log('No authenticated user found')
          setError('Authentication required. Please sign in.')
          setCompanies([])
          setIsLoading(false)
          return
        }

        // Try direct Supabase query first (authenticated)
        console.log('Fetching companies with authenticated user:', user.email)
        const { data, error } = await supabase
          .from('companies')
          .select(`
            id,
            name,
            industry,
            status,
            address,
            contacts (id, name)
          `)
          .order('name')

        if (error) {
          console.error('Error fetching companies from Supabase:', error)
          console.log('Trying admin API endpoint as fallback...')

          // Try admin API endpoint as fallback
          try {
            const response = await fetch('/api/admin/companies')

            if (!response.ok) {
              throw new Error(`API error: ${response.status}`)
            }

            const apiData = await response.json()
            console.log('Admin API response:', apiData)

            if (apiData.companies) {
              console.log(`Successfully fetched ${apiData.companies.length} companies from admin API`)
              setCompanies(apiData.companies)
              return
            } else {
              throw new Error('No companies data in API response')
            }
          } catch (apiError) {
            console.error('Error fetching from admin API:', apiError)
            setError(`Failed to load companies: ${error.message}`)
            setCompanies([])
          }
        } else if (data && data.length > 0) {
          console.log(`Successfully fetched ${data.length} companies from Supabase`)

          // Process data to add counts and other calculations
          const processedData = await Promise.all(data.map(async (company: Company) => {
            // Fetch project count for each company
            const { data: projectCountData } = await supabase
              .rpc('get_company_project_count', { company_id: company.id })

            return {
              ...company,
              _count: {
                projects: projectCountData || 0,
                contacts: company.contacts?.length || 0
              }
            }
          }))

          setCompanies(processedData)
        } else {
          console.log('No companies found via Supabase, trying admin API...')

          // Try admin API endpoint
          try {
            const response = await fetch('/api/admin/companies')

            if (!response.ok) {
              throw new Error(`API error: ${response.status}`)
            }

            const apiData = await response.json()
            console.log('Admin API response:', apiData)

            if (apiData.companies) {
              console.log(`Successfully fetched ${apiData.companies.length} companies from admin API`)
              setCompanies(apiData.companies)
            } else {
              console.log('No companies data in API response')
              setCompanies([])
            }
          } catch (apiError) {
            console.error('Error fetching from admin API:', apiError)
            setError('Failed to load companies. Please try again later.')
            setCompanies([])
          }
        }
      } catch (err) {
        console.error('Unexpected error:', err)
        setError('An unexpected error occurred. Please try again.')
        setCompanies([])
      }

      setIsLoading(false)
    }

    fetchCompanies()
  }, [supabase, user])

  // Filter companies based on search query
  const filteredCompanies = companies.filter(company =>
    company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    company.industry?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    company.address?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Companies</h1>
          <p className="text-gray-600">Manage your client companies</p>
        </div>

        <Link
          href="/companies/onboard"
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <Plus size={18} className="mr-1" />
          Onboard New Company
        </Link>
      </div>

      {/* Search and view toggle */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4 items-center">
        <div className="relative w-full md:w-96">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-5 h-5 text-gray-400" />
          </div>
          <input
            type="search"
            className="block w-full py-2 pl-10 pr-3 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search companies..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="ml-auto">
          <ViewToggle onViewChange={setViewMode} initialView={viewMode} />
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded flex items-center">
          <Shield className="h-5 w-5 mr-2" />
          <span>{error}</span>
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      ) : filteredCompanies.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="bg-gray-100 rounded-full p-3 mb-3">
            <Building className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-1">No companies found</h3>
          <p className="text-gray-500 max-w-md">
            {searchQuery
              ? 'Try changing your search terms'
              : 'Add your first company to get started'}
          </p>
          {!searchQuery && (
            <Link
              href="/companies/onboard"
              className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
            >
              Onboard New Company
            </Link>
          )}
        </div>
      ) : viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCompanies.map((company) => (
            <div key={company.id} className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden">
              <div className="p-5">
                <div className="flex justify-between items-start">
                  <Link href={`/companies/${company.id}`} className="flex-1">
                    <div className="flex items-center space-x-3">
                      <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                        <span className="text-blue-800 font-medium text-lg">{getInitials(company.name)}</span>
                      </div>
                      <h3 className="font-medium text-gray-900">{company.name}</h3>
                    </div>
                  </Link>
                  <div className="flex items-center space-x-2">
                    {company.status && (
                      <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(company.status)}`}>
                        {company.status}
                      </span>
                    )}
                  </div>
                </div>

                <Link href={`/companies/${company.id}`} className="block">
                  <div className="mt-4 space-y-2">
                    {company.industry && (
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Industry:</span> {company.industry}
                      </p>
                    )}
                    {company.address && (
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Address:</span> {company.address}
                      </p>
                    )}
                  </div>

                  <div className="mt-4 flex justify-between text-sm text-gray-500">
                    <div>
                      <span className="font-medium">{company._count?.projects || 0}</span> Projects
                    </div>
                    <div>
                      <span className="font-medium">{company._count?.contacts || 0}</span> Contacts
                    </div>
                  </div>
                </Link>

                <div className="mt-4 pt-4 border-t border-gray-100 flex justify-end space-x-2">
                  <Link
                    href={`/companies/${company.id}/edit`}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    <Edit size={18} />
                  </Link>
                  <button
                    onClick={async (e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      if (window.confirm('Are you sure you want to delete this company? This action cannot be undone.')) {
                        try {
                          const { error } = await supabase
                            .from('companies')
                            .delete()
                            .eq('id', company.id)

                          if (error) throw error

                          // Refresh the companies list
                          setCompanies(companies.filter(c => c.id !== company.id))
                        } catch (err) {
                          console.error('Error deleting company:', err)
                          alert('Failed to delete company. Please try again.')
                        }
                      }
                    }}
                    className="text-red-600 hover:text-red-900"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Company
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Industry
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Address
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Projects
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contacts
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredCompanies.map((company) => (
                  <tr key={company.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Link href={`/companies/${company.id}`} className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                          {company.name && (
                            <span className="text-blue-800 font-medium">{getInitials(company.name)}</span>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{company.name}</div>
                        </div>
                      </Link>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{company.industry || '-'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {company.status && (
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusBadgeClass(company.status)}`}>
                          {company.status}
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{company.address || '-'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {company._count?.projects || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {company._count?.contacts || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex space-x-2 justify-end">
                        <Link
                          href={`/companies/${company.id}/edit`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Edit size={18} />
                        </Link>
                        <button
                          onClick={async (e) => {
                            e.preventDefault()
                            e.stopPropagation()
                            if (window.confirm('Are you sure you want to delete this company? This action cannot be undone.')) {
                              try {
                                const { error } = await supabase
                                  .from('companies')
                                  .delete()
                                  .eq('id', company.id)

                                if (error) throw error

                                // Refresh the companies list
                                setCompanies(companies.filter(c => c.id !== company.id))
                              } catch (err) {
                                console.error('Error deleting company:', err)
                                alert('Failed to delete company. Please try again.')
                              }
                            }
                          }}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 size={18} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}