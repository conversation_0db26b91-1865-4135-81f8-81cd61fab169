'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Building, Briefcase, User } from 'lucide-react'
import Link from 'next/link'
// We'll likely use a form library like React Hook Form later

export default function CompanyOnboardingPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  // const [success, setSuccess] = useState<string | null>(null); // For success message

  // Combined form data state
  const [formData, setFormData] = useState({
    // Company fields
    company_name: '',
    industry: '',
    company_status: 'active', // Default
    address: '',
    website: '',
    company_notes: '',
    // Initial Project fields
    project_name: '',
    project_description: '',
    project_start_date: '',
    project_expected_end_date: '',
    project_status: 'planning', // Default
    project_budget: '',
    // Primary Contact fields
    contact_name: '',
    contact_email: '',
    contact_phone: '',
    contact_position: '',
    contact_notes: '',
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)
    // setSuccess(null);

    try {
      // TODO: Implement API call to the backend onboarding endpoint
      console.log('Submitting onboarding data:', formData)

      const response = await fetch('/api/companies/onboard', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to onboard company')
      }
      
      // setSuccess('Company onboarded successfully! Redirecting...');
      // TODO: Redirect to the new company's page or companies list
      router.push('/companies') // Or perhaps result.companyId to go to the new company page
      // router.refresh(); // If needed

    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred'
      setError(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div>
      <div className="mb-6">
        <Link
          href="/companies"
          className="text-gray-500 hover:text-gray-700 inline-flex items-center"
        >
          <ArrowLeft size={16} className="mr-1" />
          Back to Companies
        </Link>
        <h1 className="text-2xl font-bold text-gray-900 mt-4">Onboard New Company</h1>
        <p className="text-gray-600">Streamline setup for new companies, their first project, and primary contact.</p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md">
          {error}
        </div>
      )}
      {/* {success && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 text-green-700 rounded-md">
          {success}
        </div>
      )} */}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6 md:p-8 space-y-8">
        {/* Company Details Section */}
        <section className="space-y-6">
          <div className="flex items-center">
            <Building className="w-6 h-6 mr-2 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-800">Company Information</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="company_name" className="block text-sm font-medium text-gray-700 mb-1">
                Company Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="company_name"
                name="company_name"
                required
                value={formData.company_name}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter company name"
              />
            </div>
            <div>
              <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-1">
                Industry
              </label>
              <input
                type="text"
                id="industry"
                name="industry"
                value={formData.industry}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g. Technology"
              />
            </div>
            <div>
              <label htmlFor="company_status" className="block text-sm font-medium text-gray-700 mb-1">
                Company Status
              </label>
              <select
                id="company_status"
                name="company_status"
                value={formData.company_status}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="lead">Lead</option>
                <option value="prospect">Prospect</option>
                <option value="former">Former Client</option>
              </select>
            </div>
            <div>
              <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-1">
                Website
              </label>
              <input
                type="url"
                id="website"
                name="website"
                value={formData.website}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://example.com"
              />
            </div>
            <div className="md:col-span-2">
              <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                Address
              </label>
              <input
                type="text"
                id="address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Company address"
              />
            </div>
            <div className="md:col-span-2">
              <label htmlFor="company_notes" className="block text-sm font-medium text-gray-700 mb-1">
                Company Notes
              </label>
              <textarea
                id="company_notes"
                name="company_notes"
                rows={3}
                value={formData.company_notes}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Additional information about this company"
              ></textarea>
            </div>
          </div>
        </section>

        {/* Initial Project Section */}
        <section className="space-y-6 border-t border-gray-200 pt-8">
          <div className="flex items-center">
            <Briefcase className="w-6 h-6 mr-2 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-800">Initial Project</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="project_name" className="block text-sm font-medium text-gray-700 mb-1">
                Project Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="project_name"
                name="project_name"
                required
                value={formData.project_name}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., Website Redesign Phase 1"
              />
            </div>
            <div>
              <label htmlFor="project_status" className="block text-sm font-medium text-gray-700 mb-1">
                Project Status
              </label>
              <select
                id="project_status"
                name="project_status"
                value={formData.project_status}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="planning">Planning</option>
                <option value="active">Active</option>
                <option value="on hold">On Hold</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
             <div className="md:col-span-2">
              <label htmlFor="project_description" className="block text-sm font-medium text-gray-700 mb-1">
                Project Description
              </label>
              <textarea
                id="project_description"
                name="project_description"
                rows={3}
                value={formData.project_description}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Brief overview of the project"
              ></textarea>
            </div>
            <div>
              <label htmlFor="project_start_date" className="block text-sm font-medium text-gray-700 mb-1">
                Project Start Date
              </label>
              <input
                type="date"
                id="project_start_date"
                name="project_start_date"
                value={formData.project_start_date}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label htmlFor="project_expected_end_date" className="block text-sm font-medium text-gray-700 mb-1">
                Target End Date
              </label>
              <input
                type="date"
                id="project_expected_end_date"
                name="project_expected_end_date"
                value={formData.project_expected_end_date}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label htmlFor="project_budget" className="block text-sm font-medium text-gray-700 mb-1">
                Project Budget (Value)
              </label>
              <input
                type="number"
                id="project_budget"
                name="project_budget"
                value={formData.project_budget}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., 5000"
              />
            </div>
          </div>
        </section>

        {/* Primary Contact Section */}
        <section className="space-y-6 border-t border-gray-200 pt-8">
          <div className="flex items-center">
            <User className="w-6 h-6 mr-2 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-800">Primary Contact</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="contact_name" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="contact_name"
                name="contact_name"
                required
                value={formData.contact_name}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Full name of the primary contact"
              />
            </div>
            <div>
              <label htmlFor="contact_email" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Email
              </label>
              <input
                type="email"
                id="contact_email"
                name="contact_email"
                value={formData.contact_email}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label htmlFor="contact_phone" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Phone
              </label>
              <input
                type="tel"
                id="contact_phone"
                name="contact_phone"
                value={formData.contact_phone}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Phone number"
              />
            </div>
            <div>
              <label htmlFor="contact_position" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Position
              </label>
              <input
                type="text"
                id="contact_position"
                name="contact_position"
                value={formData.contact_position}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., Project Manager, CEO"
              />
            </div>
            <div className="md:col-span-2">
              <label htmlFor="contact_notes" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Notes
              </label>
              <textarea
                id="contact_notes"
                name="contact_notes"
                rows={3}
                value={formData.contact_notes}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Notes about this contact"
              ></textarea>
            </div>
          </div>
        </section>

        <div className="mt-10 flex justify-end space-x-4 pt-8 border-t border-gray-200">
          <Link
            href="/companies"
            className="px-6 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isSubmitting ? 'Onboarding...' : 'Onboard Company'}
          </button>
        </div>
      </form>
    </div>
  )
} 