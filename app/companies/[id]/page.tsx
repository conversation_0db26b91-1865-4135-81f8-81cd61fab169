'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  ArrowLeft,
  Building,
  Globe,
  MapPin,
  Briefcase,
  Users,
  FileText,
  Key,
  Edit,
  Trash2,
  ExternalLink
} from 'lucide-react'
import supabase from '../../../lib/supabase/client'
import { getStatusColor } from '../../../lib/utils'

interface Company {
  id: string
  name: string
  industry: string | null
  status: string | null
  address: string | null
  website: string | null
  notes: string | null
  created_at: string | null
  updated_at: string | null
}

interface Contact {
  id: string
  name: string
  email: string | null
  phone: string | null
  position: string | null
}

interface Project {
  id: string
  name: string
  status: string | null
  start_date: string | null
  expected_end_date: string | null
}

interface Credential {
  id: string
  name: string
  credential_type: string | null
}

interface Invoice {
  id: string
  invoice_number: string | null
  amount: number | null
  status: string | null
  due_date: string | null
}

export default function CompanyDetail({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [company, setCompany] = useState<Company | null>(null)
  const [contacts, setContacts] = useState<Contact[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [credentials, setCredentials] = useState<Credential[]>([])
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchCompanyData() {
      setIsLoading(true)
      setError(null)

      try {
        // Fetch company details
        const { data: companyData, error: companyError } = await supabase
          .from('companies')
          .select('*')
          .eq('id', params.id)
          .single()

        if (companyError) throw companyError

        setCompany(companyData)

        // Fetch related contacts
        const { data: contactsData, error: contactsError } = await supabase
          .from('contacts')
          .select('id, name, email, phone, position')
          .eq('company_id', params.id)
          .order('name')

        if (contactsError) throw contactsError

        setContacts(contactsData || [])

        // Fetch related projects
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select('id, name, status, start_date, expected_end_date')
          .eq('company_id', params.id)
          .order('created_at', { ascending: false })

        if (projectsError) throw projectsError

        setProjects(projectsData || [])

        // Fetch related credentials
        const { data: credentialsData, error: credentialsError } = await supabase
          .from('credentials')
          .select('id, name, credential_type')
          .eq('company_id', params.id)
          .order('name')

        if (credentialsError) throw credentialsError

        setCredentials(credentialsData || [])

        // Fetch related invoices
        const { data: invoicesData, error: invoicesError } = await supabase
          .from('invoices')
          .select('id, invoice_number, amount, status, due_date')
          .eq('company_id', params.id)
          .order('created_at', { ascending: false })

        if (invoicesError) throw invoicesError

        setInvoices(invoicesData || [])

      } catch (err: any) {
        console.error('Error fetching company data:', err)
        setError('Failed to load company data. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    fetchCompanyData()
  }, [params.id])



  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (error || !company) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative max-w-md w-full" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error || 'Failed to load company data. Please try again.'}</span>
          <div className="mt-4">
            <Link href="/companies" className="text-red-700 underline">
              Return to companies list
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div>
      {/* Header with back button and actions */}
      <div className="mb-6">
        <Link
          href="/companies"
          className="text-gray-500 hover:text-gray-700 inline-flex items-center"
        >
          <ArrowLeft size={16} className="mr-1" />
          Back to Companies
        </Link>

        <div className="flex justify-between items-center mt-4">
          <h1 className="text-2xl font-bold text-gray-900">{company.name}</h1>

          <div className="flex space-x-2">
            <Link
              href={`/companies/${company.id}/edit`}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center"
            >
              <Edit size={16} className="mr-1" />
              Edit
            </Link>
            <button
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md flex items-center"
              onClick={async () => {
                if (window.confirm('Are you sure you want to delete this company? This action cannot be undone.')) {
                  try {
                    const { error } = await supabase
                      .from('companies')
                      .delete()
                      .eq('id', company.id)

                    if (error) throw error

                    // Navigate back to companies list
                    router.push('/companies')
                    router.refresh()
                  } catch (err) {
                    console.error('Error deleting company:', err)
                    alert('Failed to delete company. Please try again.')
                  }
                }
              }}
            >
              <Trash2 size={16} className="mr-1" />
              Delete
            </button>
          </div>
        </div>

        {/* Status badge */}
        <div className="mt-2">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(company.status)}`}>
            {company.status || 'No Status'}
          </span>
        </div>
      </div>

      {/* Company details */}
      <div className="bg-white shadow rounded-lg mb-6">
        <div className="px-6 py-5 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Company Details</h2>
        </div>

        <div className="px-6 py-5">
          <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
            <div>
              <dt className="text-sm font-medium text-gray-500 flex items-center">
                <Building className="w-4 h-4 mr-1" />
                Industry
              </dt>
              <dd className="mt-1 text-sm text-gray-900">{company.industry || 'Not specified'}</dd>
            </div>

            <div>
              <dt className="text-sm font-medium text-gray-500 flex items-center">
                <MapPin className="w-4 h-4 mr-1" />
                Address
              </dt>
              <dd className="mt-1 text-sm text-gray-900">{company.address || 'Not specified'}</dd>
            </div>

            <div>
              <dt className="text-sm font-medium text-gray-500 flex items-center">
                <Globe className="w-4 h-4 mr-1" />
                Website
              </dt>
              <dd className="mt-1 text-sm text-gray-900">
                {company.website ? (
                  <a
                    href={company.website.startsWith('http') ? company.website : `https://${company.website}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 flex items-center"
                  >
                    {company.website}
                    <ExternalLink className="w-3 h-3 ml-1" />
                  </a>
                ) : (
                  'Not specified'
                )}
              </dd>
            </div>

            <div>
              <dt className="text-sm font-medium text-gray-500">Created</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {company.created_at ? new Date(company.created_at).toLocaleDateString() : 'Unknown'}
              </dd>
            </div>
          </dl>

          {company.notes && (
            <div className="mt-6">
              <h3 className="text-sm font-medium text-gray-500">Notes</h3>
              <div className="mt-2 p-3 bg-gray-50 rounded-md text-sm text-gray-900">
                {company.notes}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Related data sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Contacts section */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-5 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900 flex items-center">
              <Users className="w-5 h-5 mr-2 text-gray-500" />
              Contacts
            </h2>
            <Link
              href={`/contacts/new?company=${company.id}`}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              + Add Contact
            </Link>
          </div>

          <div className="px-6 py-5">
            {contacts.length > 0 ? (
              <ul className="divide-y divide-gray-200">
                {contacts.map(contact => (
                  <li key={contact.id} className="py-4">
                    <Link href={`/contacts/${contact.id}`} className="block hover:bg-gray-50">
                      <div className="flex justify-between">
                        <p className="text-sm font-medium text-gray-900">{contact.name}</p>
                        <p className="text-sm text-gray-500">{contact.position || ''}</p>
                      </div>
                      <div className="mt-1">
                        {contact.email && (
                          <p className="text-sm text-gray-500">{contact.email}</p>
                        )}
                        {contact.phone && (
                          <p className="text-sm text-gray-500">{contact.phone}</p>
                        )}
                      </div>
                    </Link>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500 py-4">No contacts found for this company.</p>
            )}
          </div>
        </div>

        {/* Projects section */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-5 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900 flex items-center">
              <Briefcase className="w-5 h-5 mr-2 text-gray-500" />
              Projects
            </h2>
            <Link
              href={`/projects/new?company=${company.id}`}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              + Add Project
            </Link>
          </div>

          <div className="px-6 py-5">
            {projects.length > 0 ? (
              <ul className="divide-y divide-gray-200">
                {projects.map(project => (
                  <li key={project.id} className="py-4">
                    <Link href={`/projects/${project.id}`} className="block hover:bg-gray-50">
                      <div className="flex justify-between">
                        <p className="text-sm font-medium text-gray-900">{project.name}</p>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                          {project.status || 'No Status'}
                        </span>
                      </div>
                      <div className="mt-1 text-sm text-gray-500">
                        {project.start_date && (
                          <span>Started: {new Date(project.start_date).toLocaleDateString()}</span>
                        )}
                        {project.expected_end_date && (
                          <span className="ml-4">Due: {new Date(project.expected_end_date).toLocaleDateString()}</span>
                        )}
                      </div>
                    </Link>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500 py-4">No projects found for this company.</p>
            )}
          </div>
        </div>

        {/* Credentials section */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-5 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900 flex items-center">
              <Key className="w-5 h-5 mr-2 text-gray-500" />
              Credentials
            </h2>
            <Link
              href={`/credentials/new?company=${company.id}`}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              + Add Credential
            </Link>
          </div>

          <div className="px-6 py-5">
            {credentials.length > 0 ? (
              <ul className="divide-y divide-gray-200">
                {credentials.map(credential => (
                  <li key={credential.id} className="py-4">
                    <Link href={`/credentials/${credential.id}`} className="block hover:bg-gray-50">
                      <div className="flex justify-between">
                        <p className="text-sm font-medium text-gray-900">{credential.name}</p>
                        {credential.credential_type && (
                          <p className="text-sm text-gray-500">{credential.credential_type}</p>
                        )}
                      </div>
                    </Link>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500 py-4">No credentials found for this company.</p>
            )}
          </div>
        </div>

        {/* Invoices section */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-5 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900 flex items-center">
              <FileText className="w-5 h-5 mr-2 text-gray-500" />
              Invoices
            </h2>
            <Link
              href={`/invoices/new?company=${company.id}`}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              + Add Invoice
            </Link>
          </div>

          <div className="px-6 py-5">
            {invoices.length > 0 ? (
              <ul className="divide-y divide-gray-200">
                {invoices.map(invoice => (
                  <li key={invoice.id} className="py-4">
                    <Link href={`/invoices/${invoice.id}`} className="block hover:bg-gray-50">
                      <div className="flex justify-between">
                        <p className="text-sm font-medium text-gray-900">
                          {invoice.invoice_number || `Invoice #${invoice.id.substring(0, 8)}`}
                        </p>
                        <p className="text-sm font-medium text-gray-900">
                          ${invoice.amount?.toFixed(2) || '0.00'}
                        </p>
                      </div>
                      <div className="mt-1 flex justify-between">
                        {invoice.status && (
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                            {invoice.status}
                          </span>
                        )}
                        {invoice.due_date && (
                          <p className="text-sm text-gray-500">
                            Due: {new Date(invoice.due_date).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </Link>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500 py-4">No invoices found for this company.</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
