import { notFound } from 'next/navigation';
import { createClient, createAdminClient } from '@/lib/supabase/server';
import { ClientUsersList } from '@/components/client-users/ClientUsersList';

interface ClientUsersPageProps {
  params: {
    id: string;
  };
}

export default async function ClientUsersPage({ params }: ClientUsersPageProps) {
  // Get the authenticated user from Supabase
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();

  if (!session?.user) {
    return notFound();
  }

  // Use admin client for database operations
  const adminClient = createAdminClient();

  // Fetch company details
  const { data: company, error: companyError } = await adminClient
    .from('companies')
    .select('id, name')
    .eq('id', params.id)
    .single();

  if (companyError || !company) {
    return notFound();
  }

  // Fetch client users
  const { data: clientUsers, error: usersError } = await adminClient
    .from('client_users')
    .select('*')
    .eq('company_id', params.id)
    .order('created_at', { ascending: false });

  if (usersError) {
    console.error('Error fetching client users:', usersError);
    return (
      <div className="container py-8">
        <h1 className="text-2xl font-bold mb-6">Client Users for {company.name}</h1>
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          Error loading client users. Please try again later.
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <h1 className="text-2xl font-bold mb-6">Client Users for {company.name}</h1>
      <div className="bg-white rounded-lg shadow p-6">
        <ClientUsersList
          companyId={params.id}
          initialUsers={clientUsers || []}
        />
      </div>
    </div>
  );
}
