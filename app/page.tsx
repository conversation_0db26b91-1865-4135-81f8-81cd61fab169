'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import supabase from '../lib/supabase/client'

export default function Home() {
  const [showSetupLink, setShowSetupLink] = useState(false)
  const [isCheckingSetup, setIsCheckingSetup] = useState(true)

  // Check if this is a first-time setup (no users exist)
  useEffect(() => {
    async function checkFirstTimeSetup() {
      try {
        // Check if any users exist in user_roles
        const { count, error } = await supabase
          .from('user_roles')
          .select('*', { count: 'exact', head: true })

        if (error) {
          console.error('Error checking user count:', error)
          setIsCheckingSetup(false)
          return
        }

        // If no users exist in user_roles, show the setup link
        setShowSetupLink(count === 0)
      } catch (err) {
        console.error('Unexpected error checking setup status:', err)
      } finally {
        setIsCheckingSetup(false)
      }
    }

    checkFirstTimeSetup()
  }, [])
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-blue-50 to-blue-100">
      <div className="max-w-5xl mx-auto text-center px-4">
        <h1 className="text-5xl font-bold text-blue-900 mb-6">Developer CRM</h1>
        <p className="text-xl text-blue-700 mb-8">
          Manage your clients, projects, credentials, and finances in one place
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold text-blue-800 mb-3">Client Management</h2>
            <p className="text-gray-600 mb-4">
              Organize all your client information, contacts, and communication history.
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold text-blue-800 mb-3">Project Tracking</h2>
            <p className="text-gray-600 mb-4">
              Track project progress, manage tasks, and store project details.
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold text-blue-800 mb-3">Secure Credentials</h2>
            <p className="text-gray-600 mb-4">
              Securely store and share client credentials with robust encryption.
            </p>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link
            href="/dashboard"
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-md transition-colors"
          >
            Go to Dashboard
          </Link>

          <Link
            href="/sign-in"
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-md transition-colors"
          >
            Sign In
          </Link>

          {showSetupLink && !isCheckingSetup ? (
            <Link
              href="/setup"
              className="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-8 rounded-md transition-colors"
            >
              First-Time Setup
            </Link>
          ) : (
            <Link
              href="/client-login"
              className="bg-white hover:bg-gray-100 text-blue-600 border border-blue-600 font-medium py-3 px-8 rounded-md transition-colors"
            >
              Client Login
            </Link>
          )}
        </div>
      </div>
    </div>
  )
}