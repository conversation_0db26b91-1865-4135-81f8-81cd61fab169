'use client'

import Link from 'next/link'
import { useSupabase } from '../components/SupabaseProvider'
import { Settings, Users, Key, Bell, Shield, Database } from 'lucide-react'

export default function SettingsPage() {
  const { user } = useSupabase()

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Settings</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* User Management */}
        <Link 
          href="/settings/users" 
          className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow flex flex-col"
        >
          <div className="flex items-center mb-4">
            <div className="bg-blue-100 p-3 rounded-full mr-4">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <h2 className="text-lg font-semibold">User Management</h2>
          </div>
          <p className="text-gray-600 text-sm">
            Create and manage user accounts, reset passwords, and control access permissions.
          </p>
        </Link>

        {/* API Keys */}
        <Link 
          href="/settings/api-keys" 
          className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow flex flex-col"
        >
          <div className="flex items-center mb-4">
            <div className="bg-green-100 p-3 rounded-full mr-4">
              <Key className="h-6 w-6 text-green-600" />
            </div>
            <h2 className="text-lg font-semibold">API Keys</h2>
          </div>
          <p className="text-gray-600 text-sm">
            Manage API keys for integrating with external services and applications.
          </p>
        </Link>

        {/* Notifications */}
        <Link 
          href="/settings/notifications" 
          className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow flex flex-col"
        >
          <div className="flex items-center mb-4">
            <div className="bg-yellow-100 p-3 rounded-full mr-4">
              <Bell className="h-6 w-6 text-yellow-600" />
            </div>
            <h2 className="text-lg font-semibold">Notifications</h2>
          </div>
          <p className="text-gray-600 text-sm">
            Configure email notifications for important events and updates.
          </p>
        </Link>

        {/* Security */}
        <Link 
          href="/settings/security" 
          className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow flex flex-col"
        >
          <div className="flex items-center mb-4">
            <div className="bg-red-100 p-3 rounded-full mr-4">
              <Shield className="h-6 w-6 text-red-600" />
            </div>
            <h2 className="text-lg font-semibold">Security</h2>
          </div>
          <p className="text-gray-600 text-sm">
            Manage security settings, two-factor authentication, and session policies.
          </p>
        </Link>

        {/* Database */}
        <Link 
          href="/settings/database" 
          className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow flex flex-col"
        >
          <div className="flex items-center mb-4">
            <div className="bg-purple-100 p-3 rounded-full mr-4">
              <Database className="h-6 w-6 text-purple-600" />
            </div>
            <h2 className="text-lg font-semibold">Database</h2>
          </div>
          <p className="text-gray-600 text-sm">
            Manage database settings, backups, and data exports.
          </p>
        </Link>

        {/* General Settings */}
        <Link 
          href="/settings/general" 
          className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow flex flex-col"
        >
          <div className="flex items-center mb-4">
            <div className="bg-gray-100 p-3 rounded-full mr-4">
              <Settings className="h-6 w-6 text-gray-600" />
            </div>
            <h2 className="text-lg font-semibold">General Settings</h2>
          </div>
          <p className="text-gray-600 text-sm">
            Configure general application settings, time zones, and display preferences.
          </p>
        </Link>
      </div>
    </div>
  )
}
