'use server'

import { createClient } from '@supabase/supabase-js'
import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { Database } from '../../lib/supabase/types'

// Company form data type
export type CompanyFormData = {
  name: string
  industry?: string
  status?: string
  address?: string
  website?: string
  notes?: string
}

// Create a Supabase client with service role key for server actions
const supabaseAdmin = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL as string,
  process.env.SUPABASE_SERVICE_ROLE_KEY as string
)

// Server action to create a new company
export async function createCompany(formData: CompanyFormData) {
  try {
    // Validate required fields
    if (!formData.name) {
      return { error: 'Company name is required' }
    }

    // Insert the company into the database
    const { data, error } = await supabaseAdmin
      .from('companies')
      .insert([{
        name: formData.name,
        industry: formData.industry || null,
        status: formData.status || null,
        address: formData.address || null,
        website: formData.website || null,
        notes: formData.notes || null
      }])
      .select()

    if (error) {
      console.error('Error creating company:', error)
      return { error: error.message }
    }

    // Revalidate the companies page
    revalidatePath('/companies')
    
    // Return success with the created company
    return { success: true, company: data?.[0] }
  } catch (err) {
    console.error('Unexpected error creating company:', err)
    return { error: 'An unexpected error occurred' }
  }
}
