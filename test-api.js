// Simple test script to check the API
const fetch = require('node-fetch');

async function testAPI() {
  try {
    const response = await fetch('http://localhost:3001/api/client-auth/manage?companyId=39cdf8f1-3aa5-4c39-b7e9-06c97351408a', {
      headers: {
        'Authorization': 'Bearer test-token'
      }
    });
    
    console.log('Status:', response.status);
    const data = await response.json();
    console.log('Response:', data);
  } catch (error) {
    console.error('Error:', error);
  }
}

testAPI();
