# Deploying myCRM to Vercel

This guide provides step-by-step instructions for deploying your myCRM application to Vercel.

## Prerequisites

1. A [Vercel account](https://vercel.com/signup) (you can sign up with GitHub, GitLab, or Bitbucket)
2. Your project pushed to a Git repository (GitHub, GitLab, or Bitbucket)
3. Your Supabase project is set up and running

## Deployment Steps

### 1. Connect Your Repository to Vercel

1. Log in to your [Vercel dashboard](https://vercel.com/dashboard)
2. Click on "Add New..." and select "Project"
3. Import your Git repository by selecting the appropriate Git provider and finding your repository
4. Select the repository containing your myCRM project

### 2. Configure Project Settings

Once you've selected your repository, you'll be taken to the project configuration page:

1. **Project Name**: Keep the default or choose a custom name
2. **Framework Preset**: Vercel should automatically detect Next.js
3. **Root Directory**: Leave as `.` if your project is at the root of the repository

### 3. Environment Variables

Add the following environment variables:

- `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key (mark as secret)
- `NEXT_PUBLIC_APP_URL`: The URL of your deployed application (e.g., `https://your-project.vercel.app`)
- `NEXT_PUBLIC_SUPABASE_AUTH_REDIRECT_URL`: The auth callback URL (e.g., `https://your-project.vercel.app/auth/callback`)

### 4. Build and Output Settings

These should be automatically configured for Next.js, but verify:

- **Build Command**: `npm run build`
- **Output Directory**: `.next`
- **Install Command**: `npm install --legacy-peer-deps`

### 5. Deploy

Click "Deploy" and wait for the build to complete. Vercel will automatically build and deploy your application.

### 6. Update Supabase Auth Settings

After deployment, you need to update your Supabase project's authentication settings:

1. Go to your [Supabase dashboard](https://app.supabase.com/)
2. Select your project
3. Go to Authentication → URL Configuration
4. Update the Site URL to your Vercel deployment URL (e.g., `https://your-project.vercel.app`)
5. Add the redirect URL: `https://your-project.vercel.app/auth/callback`
6. Save the changes

### 7. Verify Deployment

1. Visit your deployed application at the Vercel URL
2. Test the authentication flow
3. Verify that all features are working correctly

## Troubleshooting

If you encounter issues during deployment:

1. **Build Errors**: Check the build logs in Vercel for specific error messages
2. **Authentication Issues**: Ensure your Supabase URL and API keys are correct
3. **Redirect Problems**: Verify that the redirect URLs are properly configured in both Vercel and Supabase
4. **API Errors**: Check that your environment variables are correctly set

## Continuous Deployment

Vercel automatically sets up continuous deployment. Any changes pushed to your main branch will trigger a new deployment.

## Custom Domain (Optional)

To use a custom domain:

1. Go to your project settings in Vercel
2. Navigate to the "Domains" section
3. Add your custom domain and follow the instructions to configure DNS settings

## Need Help?

If you need further assistance, refer to:

- [Vercel Documentation](https://vercel.com/docs)
- [Next.js Deployment Documentation](https://nextjs.org/docs/deployment)
- [Supabase Documentation](https://supabase.com/docs)
