# Testing Guide

This document provides an overview of the testing setup for this project, how to run tests, and how to interpret their results.

## Testing Framework

This project uses [Jest](https://jestjs.io/) as the JavaScript testing framework, along with [React Testing Library](https://testing-library.com/docs/react-testing-library/intro) for testing React components.

## Test File Structure

Test files are located in the `__tests__` directory at the root of the project. Inside `__tests__`, tests are further organized into subdirectories corresponding to the modules or components they are testing (e.g., `__tests__/contacts`, `__tests__/projects`). Test files typically end with `.test.tsx` or `.test.ts`.

### Existing Test Suites

So far, we have identified and worked with the following main test suites:

*   `__tests__/projects/projects.test.tsx`
*   `__tests__/projects/project-detail.test.tsx` - **PASSING** (Covers viewing project details, navigation to edit, and delete functionality)
*   `__tests__/tasks/tasks.test.tsx` - **PASSING** (Covers task list rendering, loading states, and filtering by search term and status)
*   `__tests__/tasks/task-board.test.tsx` - **PASSING** (Covers task board rendering, loading, column display, and all filter functionalities. Note: Some `act(...)` warnings appear in the console during test runs; these relate to asynchronous state updates and could be addressed in a follow-up for stricter test hygiene, though all tests currently pass.)
*   `__tests__/contacts/contacts.test.tsx` - **PASSING** (Covers contact list rendering, loading, search filtering, and navigation link integrity.)
*   `__tests__/auth/auth.test.tsx` - **MOSTLY PASSING (3 passed, 1 todo)** (Covers login form rendering, successful login via API, and API error handling. Client-side empty field validation marked as todo due to complexities with JSDOM and HTML `required` attributes.)

### Other Identified Test Files (Status to be determined)

*   `__tests__/invoices/invoice-detail.test.tsx` (Initially reviewed, placed on hold)
*   `__tests__/dashboard/dashboard.test.tsx`
*   `__tests__/settings/settings.test.tsx`
*   `__tests__/profile/profile.test.tsx`
*   `__tests__/companies/companies.test.tsx`
*   `__tests__/utils/utils.test.ts`

## How to Run Tests

### Running All Tests

To run all tests in the project:

```bash
npm test
# or
npx jest
```

### Running Specific Test Files

To run tests for a specific file:

```bash
npx jest <path_to_test_file>
```

For example:

```bash
npx jest __tests__/contacts/contacts.test.tsx
```

### Running Tests in Watch Mode

To run tests in watch mode, which automatically re-runs tests when files change:

```bash
npm test -- --watch
# or
npx jest --watch
```

## Interpreting Results

*   **PASS**: Indicates the test case ran successfully and all assertions passed.
*   **FAIL**: Indicates the test case failed. The output will provide details about which assertion failed and why.
*   **SKIP**: Indicates a test case was intentionally skipped (e.g., using `test.todo` or `test.skip`).

Review the console output carefully for any error messages or stack traces to help diagnose failing tests.

## Adding New Tests

1.  Create a new file ending in `.test.tsx` (for React components) or `.test.ts` (for utility functions) within the appropriate subdirectory in `__tests__`.
2.  Write your test cases using Jest and React Testing Library syntax.
3.  Ensure your tests cover:
    *   Initial rendering and loading states.
    *   User interactions (e.g., button clicks, form input).
    *   Data fetching and display (with appropriate mocking for external services like Supabase).
    *   Edge cases and error handling.

## API Endpoint Testing

For testing API endpoints, a combination of integration and end-to-end tests is recommended. Tools like Postman, Insomnia, or programmatic HTTP request libraries within Jest (e.g., `supertest` or `axios` with mocks) can be used.

### `POST /api/companies/onboard`

This endpoint is responsible for creating company records, and optionally associated project and contact records. It requires authentication (Admin/Team User).

**Testing Scenarios:**

*   **Integration Tests:**
    *   **Successful Company Creation:**
        *   Send a valid payload with only company data.
        *   Verify a `201 Created` (or appropriate success) status code.
        *   Verify the response body contains the created company details.
        *   Verify the company record is correctly saved in the database.
    *   **Successful Company, Project, and Contact Creation:**
        *   Send a valid payload including company, initial project, and initial contact data.
        *   Verify a `201 Created` (or appropriate success) status code.
        *   Verify the response body contains the created entities.
        *   Verify all records (company, project, contact) are correctly saved and linked in the database.
    *   **Data Validation & Error Handling:**
        *   Send requests with missing required fields (e.g., missing company name). Verify a `400 Bad Request` (or appropriate error) status code and a descriptive error message.
        *   Send requests with invalid data types (e.g., non-string for company name). Verify a `400 Bad Request` status code and error message.
        *   Test other business logic validation (e.g., duplicate company name if not allowed).
    *   **Authentication & Authorization:**
        *   Send a request without an authentication token. Verify a `401 Unauthorized` status code.
        *   Send a request with an authentication token for a user role that is not Admin or Team User (e.g., a Client User if such a role exists and is distinct). Verify a `403 Forbidden` status code.
        *   Send a request with a valid Admin/Team User token. Verify successful processing.

### `GET /api/direct-contacts`

This endpoint retrieves all contacts and is publicly accessible.

**Testing Scenarios:**

*   **Integration Tests:**
    *   **Successful Retrieval of Contacts:**
        *   Ensure some contact records exist in the database.
        *   Send a GET request to the endpoint.
        *   Verify a `200 OK` status code.
        *   Verify the response body is an array and contains the expected contact data.
    *   **No Contacts Exist:**
        *   Ensure no contact records exist in the database.
        *   Send a GET request to the endpoint.
        *   Verify a `200 OK` status code.
        *   Verify the response body is an empty array.
    *   **Public Accessibility:**
        *   Send a GET request without any authentication token.
        *   Verify a `200 OK` status code and successful data retrieval (or empty array if no contacts).
## Current Test Coverage

While we are progressively adding and fixing tests, full test coverage information can be generated by Jest if configured. The goal is to achieve comprehensive coverage for all critical user flows and components.

*(This document will be updated as more tests are added and their statuses are confirmed.)* 