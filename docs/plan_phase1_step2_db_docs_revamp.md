# Plan: Phase 1, Step 2 - Database Documentation Revamp

**Goal:** To update the existing `docs/Database_Schema.md` to be a complete and accurate representation of the live database schema, and to create a new, maintainable Mermaid ERD in `docs/Database_Diagram.md`.

---

## A. Updating `docs/Database_Schema.md`

*   **Objective:** Ensure this document accurately reflects the `public` schema tables, incorporating authentication/role tables and correcting discrepancies against the live schema.
*   **Key Actions & Content Changes:**
    1.  **Add `client_users` Table Definition:**
        *   Schema: `id` (uuid, PK), `company_id` (uuid, FK to `companies.id`), `email` (text, unique), `password_hash` (text), `is_active` (boolean), `created_at` (timestamptz), `updated_at` (timestamptz), `last_login` (timestamptz).
        *   Note data types and constraints.
    2.  **Add `client_sessions` Table Definition:**
        *   Schema: `id` (uuid, PK), `token` (text, unique), `user_id` (uuid, FK to `client_users.id`), `company_id` (uuid, FK to `companies.id`), `created_at` (timestamptz), `expires_at` (timestamptz).
    3.  **Add `user_roles` Table Definition:**
        *   Schema: `id` (uuid, PK), `user_id` (uuid, FK to `auth.users.id`, unique), `role` (text, CHECK constraint: 'admin' or 'user'), `created_at` (timestamptz), `updated_at` (timestamptz).
        *   Emphasize linkage to Supabase's `auth.users` table.
    4.  **Add `client_companies` Table Definition:**
        *   Schema: `id` (uuid, PK), `user_id` (uuid, FK to `auth.users.id`), `company_id` (uuid, FK to `companies.id`), `created_at` (timestamptz), `updated_at` (timestamptz).
        *   Explain purpose: linking admin/team users to client companies.
    5.  **Correct `users` Table Section:**
        *   Replace current `public.users` section.
        *   Explain admin/team user identity is via `auth.users` (Supabase built-in).
        *   Cross-reference `public.user_roles` for application-specific roles.
    6.  **Clarify `client_portals.access_token`:**
        *   In `client_portal` table definition, note `access_token` as likely legacy/unused for primary client login.
    7.  **Review and Update Existing Table Definitions:**
        *   Cross-check columns of other tables (e.g., `companies` for `logo_url`, `size`) against live schema and update.
    8.  **Update Foreign Key Relationships Section:**
        *   Add all new FK relationships for `client_users`, `client_sessions`, `user_roles`, `client_companies`.
        *   Verify accuracy of all listed FKs.
    9.  **Review Security Policies Section:**
        *   Update RLS section to reflect correct tables and role-checking mechanisms (e.g., policies checking `public.user_roles`).

---

## B. Creating a New Database Diagram (Mermaid) in `docs/Database_Diagram.md`

*   **Objective:** Replace the outdated ASCII diagram with a clear, maintainable, and comprehensive Mermaid ERD.
*   **Key Actions & Content:**
    1.  **Identify Key Tables for Diagram:**
        *   Core CRM: `companies`, `contacts`, `projects`, `tasks`, `credentials`, `invoices`, `invoice_items`, `services`, `activities`, `expenses`.
        *   Auth/Roles: `client_users`, `client_sessions`, `user_roles`, `client_companies`.
        *   Conceptually include `auth.users` (noting it's in `auth` schema) to show links.
    2.  **Define Entities and Key Attributes (Mermaid Syntax):**
        *   For each table, list important columns, especially PKs and FKs.
    3.  **Define Relationships (Mermaid Syntax):**
        *   Clearly depict one-to-many and many-to-many relationships using correct syntax.
        *   Examples: `companies` to its related tables, `projects` to its related tables, `auth.users` to `user_roles`/`client_companies`, `client_users` to `client_sessions`, etc.
    4.  **Structure of `docs/Database_Diagram.md`:**
        *   Replace existing ASCII diagram with the new Mermaid code block.
        *   Optionally, retain brief textual explanations of key relationships if they add clarity beyond the diagram.

---