# Plan: Phase 2, Step 3 - Usage and API Guide (`docs/Usage_API_Guide.md`)

**Goal:** To provide developers with comprehensive instructions on using Developer CRM features programmatically, interacting with its APIs (Next.js API routes, Supabase RPCs), and understanding common workflows.

---

## Proposed Outline for `docs/Usage_API_Guide.md`

1.  **Introduction**
    *   Purpose: How to use CRM functionalities programmatically and understand its API.
    *   Audience: Developers.
    *   Overview of interaction methods (Next.js API routes, Supabase client library, DB functions).

2.  **Authentication & Authorization (API Context)**
    *   **Admin/Team Member API Interaction:**
        *   Making authenticated requests to protected Next.js API routes (passing Supabase JWT).
        *   RLS and role checks (`public.user_roles`) enforced on the backend.
    *   **Client Portal API Interaction:**
        *   How client portal frontend makes requests (session cookie).
        *   Validation of sessions by `/api/client-portal/data` and other client-specific endpoints.
    *   Reference `docs/Authentication.md` for detailed auth mechanisms.

3.  **Core CRM API Endpoints (Next.js API Routes)**
    *   Structure for each endpoint:
        *   **Endpoint:** `METHOD /api/path/:param`
        *   **Description:** What it does.
        *   **Permissions Required:** (e.g., Admin, Authenticated User, Client Portal Session).
        *   **Request:** Headers, Path/Query Params, Body (JSON schema/example).
        *   **Response:** Success (Status, JSON schema/example), Error (Status, JSON schema/example).
        *   **Example Usage:** (`curl`, JavaScript `fetch`).
    *   **Key Endpoints to Document (requires review of `app/api/`):**
        *   Admin Management: `/api/admin/create-user`, `/api/admin/reset-password`, etc.
        *   Auth Setup: `/api/auth/setup`, `/api/auth/check-setup`, etc.
        *   Client Auth & Portal: `/api/client-auth/login`, `/api/client-auth/logout`, `/api/client-auth/manage`, `/api/client-portal/data`, etc.
        *   Core Entities (CRUD if via dedicated API routes).
        *   Migrations (Internal/Admin): `/api/migrations/client-portal-auth`.

4.  **Interacting with Supabase Directly**
    *   **Using Supabase JS Client Library (Frontend):**
        *   Initializing client (ref `lib/supabase/client.ts`).
        *   Common CRUD patterns (select, insert, update, delete) respecting RLS.
        *   Example: Fetching companies.
        *   Calling Supabase Auth methods.
    *   **Using Supabase JS Client Library (Next.js API Routes - Service Role):**
        *   Initializing service role client (ref `lib/supabase/server.ts`).
        *   When to use service role (admin tasks, careful with security).
    *   **Calling PostgreSQL Functions (RPC):**
        *   How to call DB functions (e.g., `verify_client_credentials`, `create_client_user`).
        *   Example: `supabase.rpc('function_name', { args })`.
        *   List key public DB functions (cross-ref `docs/Database_Schema.md` / `docs/Authentication.md`).

5.  **Common Workflows & Examples**
    *   Creating a New Company and First Contact.
    *   Assigning a Task to a Project.
    *   Generating an Invoice for a Project.
    *   Admin Creating a New Client User for Portal Access.
    *   Client Logging into Portal and Viewing Data.

6.  **Error Handling**
    *   Common HTTP status codes.
    *   Typical error response format.

7.  **Rate Limiting (if applicable)**
    *   Mention any API rate limits.

8.  **Further Development / API Evolution**
    *   API versioning strategy (if any) or how changes are communicated.

---