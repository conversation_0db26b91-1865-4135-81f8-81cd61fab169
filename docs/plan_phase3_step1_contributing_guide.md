# Plan: Phase 3, Step 1 - Contribution Guidelines (`docs/CONTRIBUTING.md`)

**Goal:** To provide clear guidelines for developers contributing to the Developer CRM project, covering coding standards, testing, and the pull request process.

---

## Proposed Outline for `docs/CONTRIBUTING.md`

1.  **Introduction**
    *   Welcome message.
    *   Importance of following guidelines.
    *   Link to `README.md`.

2.  **Getting Started (for Contributors)**
    *   Prerequisites from `docs/Installation_Guide.md`.
    *   Development environment setup (link or reiterate key steps).
    *   Getting latest code (`git pull`, branching).

3.  **How to Contribute**
    *   **Reporting Bugs:**
        *   Where (e.g., GitHub Issues).
        *   Bug report details (reproduction steps, expected vs. actual, environment).
        *   Check existing issues.
    *   **Suggesting Enhancements / Feature Requests:**
        *   Where (e.g., GitHub Issues, Discussions).
        *   Formulating requests (description, use cases, benefits).
    *   **Working on Issues:**
        *   Claiming issues (if applicable).
        *   Discussing changes before significant work.

4.  **Development Workflow**
    *   **Branching Strategy:**
        *   (e.g., `main`, `develop`, feature branches `feat/feature-name`, bugfix branches `fix/bug-name`).
        *   Branch naming conventions.
    *   **Making Changes:**
        *   Code locally in feature/bugfix branch.
        *   Follow coding standards.
    *   **Committing Changes:**
        *   Commit message conventions (e.g., Conventional Commits).
        *   Small, logical commits.
    *   **Keeping Branch Updated:**
        *   Rebasing from `develop` (`git pull --rebase origin develop`).

5.  **Coding Standards & Conventions**
    *   **Language:** TypeScript (version, `tsconfig.json` strictness).
    *   **Frameworks:** Next.js, React best practices.
    *   **Styling:** Tailwind CSS conventions.
    *   **Linting & Formatting:**
        *   ESLint (`eslint.config.mjs`).
        *   Prettier (if used).
        *   Running linters/formatters (`npm run lint`, `npm run format`).
    *   **Naming Conventions:** Variables, functions, components, files.
    *   **Comments:** When and how.
    *   **API Design (backend contributions):** Follow existing patterns.

6.  **Testing Protocols**
    *   **Types of Tests Expected:**
        *   Unit Tests (Jest, React Testing Library).
        *   Integration Tests.
        *   E2E Tests (Playwright, Cypress).
        *(Adjust based on actual testing setup; if none, state as future improvement).*
    *   **Running Tests:** Commands (`npm test`).
    *   **Writing Tests:** Guidelines, file placement.
    *   **Test Coverage Expectations.**

7.  **Pull Request (PR) Process**
    *   **Creating a PR:**
        *   Push branch, open PR against `develop`.
        *   PR title/description conventions (link issue).
    *   **Code Review:**
        *   Expectations, responding to feedback.
        *   Approvals needed (if any).
    *   **Automated Checks / CI:**
        *   (e.g., GitHub Actions for linting, tests, build). PRs must pass.
    *   **Merging:**
        *   Who merges.
        *   Squash/rebase policy (if any).

8.  **Code of Conduct (Optional)**
    *   Link to `CODE_OF_CONDUCT.md` or state expectations.

9.  **Questions & Getting Help**
    *   Preferred channels (GitHub Discussions, Slack/Discord).

---