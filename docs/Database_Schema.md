# Developer CRM - Database Schema

## Overview
This document outlines the database schema for the Developer CRM application. The schema is designed to support all the core features of the application, including company management, contact tracking, project management, secure credential storage, and invoicing.

## Tables

### 1. companies
| Column     | Type                | Description                        |
|------------|---------------------|------------------------------------|
| id         | uuid                | Primary key (default: uuid_generate_v4()) |
| name       | text                | Company name (not nullable)        |
| industry   | text                | Industry sector                    |
| size       | text                | Company size                       |
| website    | text                | Company website URL                |
| address    | text                | Company address                    |
| logo_url   | text                | URL to company logo                |
| status     | text                | Current status (e.g., active, inactive) |
| notes      | text                | Additional notes                   |
| created_at | timestamp with time zone | Record creation timestamp (default: now()) |
| updated_at | timestamp with time zone | Record update timestamp (default: now()) |

### 2. contacts
| Column     | Type           | Description                        |
|------------|----------------|------------------------------------|
| id         | uuid           | Primary key                        |
| company_id | uuid           | Foreign key to companies           |
| name       | text           | Contact name                       |
| email      | text           | Email address                      |
| phone      | text           | Phone number                       |
| position   | text           | Job title                          |
| notes      | text           | Additional notes                   |
| created_at | timestamptz    | Record creation timestamp          |
| updated_at | timestamptz    | Record update timestamp            |

### 3. projects
| Column      | Type           | Description                       |
|-------------|----------------|-----------------------------------|
| id          | uuid           | Primary key                       |
| company_id  | uuid           | Foreign key to companies          |
| name        | text           | Project name                      |
| description | text           | Project description               |
| start_date  | date           | Project start date                |
| end_date    | date           | Project end date                  |
| status      | text           | Project status                    |
| budget      | numeric        | Project budget                    |
| created_at  | timestamptz    | Record creation timestamp         |
| updated_at  | timestamptz    | Record update timestamp           |

### 4. tasks
| Column      | Type           | Description                       |
|-------------|----------------|-----------------------------------|
| id          | uuid           | Primary key                       |
| project_id  | uuid           | Foreign key to projects           |
| name        | text           | Task name                         |
| description | text           | Task description                  |
| due_date    | date           | Task deadline                     |
| status      | text           | Task status                       |
| priority    | text           | Task priority                     |
| assigned_to | uuid           | Foreign key to users              |
| created_at  | timestamptz    | Record creation timestamp         |
| updated_at  | timestamptz    | Record update timestamp           |

### 5. credentials
| Column         | Type           | Description                      |
|----------------|----------------|----------------------------------|
| id             | uuid           | Primary key                      |
| company_id     | uuid           | Foreign key to companies         |
| project_id     | uuid           | Foreign key to projects          |
| name           | text           | Credential name                  |
| username       | text           | Username                         |
| password       | text           | Encrypted password               |
| credential_type| text           | Type of credential               |
| url            | text           | Related URL                      |
| expiry_date    | date           | Expiration date                  |
| created_at     | timestamptz    | Record creation timestamp        |
| updated_at     | timestamptz    | Record update timestamp          |

### 6. invoices
| Column         | Type           | Description                      |
|----------------|----------------|----------------------------------|
| id             | uuid           | Primary key                      |
| company_id     | uuid           | Foreign key to companies         |
| project_id     | uuid           | Foreign key to projects          |
| invoice_number | text           | Invoice number                   |
| issue_date     | date           | Date issued                      |
| due_date       | date           | Payment due date                 |
| amount         | numeric        | Total amount                     |
| status         | text           | Payment status                   |
| notes          | text           | Additional notes                 |
| created_at     | timestamptz    | Record creation timestamp        |
| updated_at     | timestamptz    | Record update timestamp          |

### 7. invoice_items
| Column         | Type           | Description                      |
|----------------|----------------|----------------------------------|
| id             | uuid           | Primary key                      |
| invoice_id     | uuid           | Foreign key to invoices          |
| description    | text           | Item description                 |
| quantity       | numeric        | Quantity                         |
| rate           | numeric        | Rate per unit                    |
| amount         | numeric        | Total amount for item            |
| created_at     | timestamptz    | Record creation timestamp        |
| updated_at     | timestamptz    | Record update timestamp          |

### 8. services
| Column         | Type           | Description                      |
|----------------|----------------|----------------------------------|
| id             | uuid           | Primary key                      |
| name           | text           | Service name                     |
| description    | text           | Service description              |
| rate           | numeric        | Hourly/fixed rate                |
| created_at     | timestamptz    | Record creation timestamp        |
| updated_at     | timestamptz    | Record update timestamp          |

### 9. activities
| Column         | Type           | Description                      |
|----------------|----------------|----------------------------------|
| id             | uuid           | Primary key                      |
| company_id     | uuid           | Foreign key to companies         |
| contact_id     | uuid           | Foreign key to contacts          |
| project_id     | uuid           | Foreign key to projects          |
| user_id        | uuid           | Foreign key to users             |
| activity_type  | text           | Type of activity                 |
| description    | text           | Activity description             |
| date           | date           | Activity date                    |
| created_at     | timestamptz    | Record creation timestamp        |
| updated_at     | timestamptz    | Record update timestamp          |

### 10. expenses
| Column               | Type           | Description                      |
|----------------------|----------------|----------------------------------|
| id                   | uuid           | Primary key                      |
| name                 | text           | Expense name                     |
| amount               | numeric        | Expense amount                   |
| date                 | date           | Date of expense                  |
| category             | text           | Expense category                 |
| payment_method       | text           | Method of payment                |
| is_reimbursable      | boolean        | Whether expense is reimbursable  |
| reimbursement_status | text           | Status of reimbursement          |
| company_id           | uuid           | Foreign key to companies         |
| project_id           | uuid           | Foreign key to projects          |
| notes                | text           | Additional notes                 |
| receipt_url          | text           | URL to receipt image             |
| created_at           | timestamptz    | Record creation timestamp        |
| updated_at           | timestamptz    | Record update timestamp          |

### 11. Admin/Team Users (`auth.users` & `public.user_roles`)

User identity for administrators and team members is managed by Supabase's built-in `auth.users` table. This table typically includes fields like `id` (UUID, primary key), `email` (TEXT), `encrypted_password` (TEXT), `created_at` (TIMESTAMPTZ), etc. For full details, refer to the Supabase documentation on the `auth.users` table.

Application-specific roles are managed in the `public.user_roles` table:

#### `user_roles` (public schema)
| Column     | Type                | Description                        |
|------------|---------------------|------------------------------------|
| id         | uuid                | Primary key (default: uuid_generate_v4()) |
| user_id    | uuid                | Foreign key to `auth.users.id` (unique, not nullable) |
| role       | text                | User role (e.g., 'admin', 'user', CHECK constraint: 'admin' OR 'user', not nullable) |
| created_at | timestamp with time zone | Record creation timestamp (default: now()) |
| updated_at | timestamp with time zone | Record update timestamp (default: now()) |

### 12. `client_portals` (public schema)
| Column         | Type                | Description                        |
|----------------|---------------------|------------------------------------|
| id             | uuid                | Primary key (default: uuid_generate_v4()) |
| company_id     | uuid                | Foreign key to `companies.id`      |
| name           | text                | Portal name (not nullable)         |
| access_token   | text                | Access token (unique). Note: Likely legacy or not used for primary client login. |
| is_active      | boolean             | Whether the portal is active (default: true) |
| last_accessed  | timestamp with time zone | Timestamp of last access           |
| created_at     | timestamp with time zone | Record creation timestamp (default: now()) |
| updated_at     | timestamp with time zone | Record update timestamp (default: now()) |

### 13. `client_users` (public schema)
| Column         | Type                | Description                        |
|----------------|---------------------|------------------------------------|
| id             | uuid                | Primary key (default: uuid_generate_v4()) |
| company_id     | uuid                | Foreign key to `companies.id` (not nullable) |
| email          | text                | Client's email address (unique, not nullable) |
| password_hash  | text                | Hashed password (not nullable)     |
| is_active      | boolean             | Whether the account is active (default: true) |
| created_at     | timestamp with time zone | Record creation timestamp (default: now()) |
| updated_at     | timestamp with time zone | Record update timestamp (default: now()) |
| last_login     | timestamp with time zone | Last login timestamp               |

### 14. `client_sessions` (public schema)
| Column         | Type                | Description                        |
|----------------|---------------------|------------------------------------|
| id             | uuid                | Primary key (default: uuid_generate_v4()) |
| token          | text                | Session token (unique, not nullable) |
| user_id        | uuid                | Foreign key to `client_users.id` (not nullable) |
| company_id     | uuid                | Foreign key to `companies.id` (not nullable) |
| created_at     | timestamp with time zone | Record creation timestamp (default: now()) |
| expires_at     | timestamp with time zone | Expiration timestamp (not nullable)  |

### 15. `client_companies` (public schema)
| Column     | Type                | Description                        |
|------------|---------------------|------------------------------------|
| id         | uuid                | Primary key (default: uuid_generate_v4()) |
| user_id    | uuid                | Foreign key to `auth.users.id` (not nullable) |
| company_id | uuid                | Foreign key to `companies.id` (not nullable) |
| created_at | timestamp with time zone | Record creation timestamp (default: now()) |
| updated_at | timestamp with time zone | Record update timestamp (default: now()) |

## Foreign Key Relationships

- **contacts.company_id** references **companies.id**
- **projects.company_id** references **companies.id**
- **tasks.project_id** references **projects.id**
- **tasks.assigned_to** references **auth.users.id** (Supabase Auth users)
- **credentials.company_id** references **companies.id**
- **credentials.project_id** references **projects.id**
- **invoices.company_id** references **companies.id**
- **invoices.project_id** references **projects.id**
- **invoice_items.invoice_id** references **invoices.id**
- **activities.company_id** references **companies.id**
- **activities.contact_id** references **contacts.id**
- **activities.project_id** references **projects.id**
- **activities.user_id** references **auth.users.id** (Supabase Auth users)
- **expenses.project_id** references **projects.id**
- **client_portals.company_id** references **companies.id** (Note: table name corrected from `client_portal` to `client_portals` based on live schema)
- **user_roles.user_id** references **auth.users.id** (Supabase Auth users)
- **client_users.company_id** references **companies.id**
- **client_sessions.user_id** references **client_users.id**
- **client_sessions.company_id** references **companies.id**
- **client_companies.user_id** references **auth.users.id** (Supabase Auth users)
- **client_companies.company_id** references **companies.id**
- **shared_credentials.credential_id** references **credentials.id** (Assuming `shared_credentials` table exists as per live schema)
- **shared_tasks.task_id** references **tasks.id** (Assuming `shared_tasks` table exists as per live schema)
- **project_services.project_id** references **projects.id** (Assuming `project_services` table exists as per live schema)
- **project_services.service_id** references **services.id** (Assuming `project_services` table exists as per live schema)
- **invoice_items.expense_id** references **expenses.id** (Assuming `invoice_items` can link to `expenses` as per live schema)
- **invoice_items.service_id** references **services.id** (Assuming `invoice_items` can link to `services` as per live schema)


## Security Policies

### Row Level Security (RLS)

All relevant tables implement Row Level Security (RLS) to ensure data isolation and proper access control:

1.  **Admin/Team Member Access (Supabase Auth Users):**
    *   RLS policies typically check the authenticated user's ID (`auth.uid()`) against the `user_id` in the `public.user_roles` table to determine if they have an 'admin' role.
    *   Administrators (`role = 'admin'`) generally have broader access to manage data across companies and projects.
    *   Non-admin team members (`role = 'user'`) might have access restricted to specific projects or companies they are associated with (e.g., via a linking table like `client_companies` if used for this purpose, or project assignment tables).

2.  **Client Access (Client Portal Users - `public.client_users`):**
    *   RLS policies for data accessible via the client portal (e.g., a specific company's projects, invoices, credentials shared with them) are enforced based on the `client_users.id` and `client_users.company_id` derived from their active `client_sessions.token`.
    *   Clients can only access data related to their own `company_id`.

Specific RLS policy examples can be found in `docs/Authentication.md`.

### Encryption

Sensitive data is encrypted:

- **credentials.password** is encrypted using strong encryption algorithms
- Decryption is only available to authorized users
- Decryption actions are logged for security auditing