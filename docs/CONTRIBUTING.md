# Contributing to Developer CRM

First off, thank you for considering contributing to Developer CRM! We welcome contributions from the community to help make this project better. Whether it's reporting a bug, discussing improvements, or submitting a pull request, your input is valuable.

This document provides guidelines for contributing to the project. Please read it carefully to ensure a smooth and effective collaboration process.

For an overview of the project and setup instructions, please see the main [README.md](../README.md) and the [Installation Guide](Installation_Guide.md).

## Getting Started (for Contributors)

Before you start contributing, please ensure you have:
1.  Met all prerequisites outlined in the [Installation Guide](Installation_Guide.md).
2.  Successfully set up your local development environment as described in the [Installation Guide](Installation_Guide.md).
3.  Fetched the latest changes from the main repository:
    ```bash
    git checkout develop # Or the main development branch, e.g., main
    git pull origin develop # Or the main development branch
    ```
    *(Please verify the primary development branch name for this project, e.g., `main` or `develop`)*

## How to Contribute

### Reporting Bugs
*   **Where to Report:** Please report bugs by opening an issue in the project's GitHub repository (Issues tab).
*   **Bug Report Details:** When submitting a bug report, please include:
    *   A clear and descriptive title.
    *   Steps to reproduce the bug.
    *   What you expected to happen.
    *   What actually happened (actual behavior), including any error messages or screenshots.
    *   Your development environment details (OS, Node.js version, browser version, Supabase CLI version if applicable).
*   **Check Existing Issues:** Before creating a new bug report, please search existing issues to see if the bug has already been reported or addressed.

### Suggesting Enhancements / Feature Requests
*   **Where to Suggest:** Suggestions for enhancements or new features can be made by opening an issue (labeled as "enhancement" or "feature request") or by starting a discussion in the GitHub Discussions tab (if enabled for the repository).
*   **Formulating Requests:** Provide a clear description of the proposed enhancement, the problem it solves or the value it adds, potential use cases, and any proposed implementation ideas if you have them.

### Working on Issues
*   If you'd like to work on an existing issue, please comment on the issue to indicate your interest and to ensure no one else is already actively working on it. This helps prevent duplicated effort.
*   For significant changes or new features, it's always a good idea to discuss your proposed approach with the maintainers in the issue or a discussion thread before investing a lot of time in implementation. This ensures your contribution aligns with the project's direction and technical standards.

## Development Workflow

We generally follow a Gitflow-like branching strategy.

### Branching Strategy
*   **`main`:** This branch represents the latest stable release. Direct commits to `main` are typically restricted.
*   **`develop`:** This is the primary development branch where all feature branches are merged. It should represent the latest development version.
*   **Feature Branches:** Create a new branch from `develop` for each new feature or significant change.
    *   Naming convention: `feat/<feature-name>` (e.g., `feat/invoice-pdf-generation`) or `feature/<feature-name>`.
*   **Bugfix Branches:** Create a new branch from `develop` (or `main` for hotfixes, if applicable) for bug fixes.
    *   Naming convention: `fix/<bug-description>` (e.g., `fix/login-redirect-issue`).
*   **Other Prefixes:** Use `docs/` for documentation changes, `chore/` for build process, tooling, etc.

### Making Changes
*   Code locally in your feature/bugfix branch.
*   Ensure your code adheres to the [Coding Standards & Conventions](#coding-standards--conventions) outlined below.

### Committing Changes
*   Make small, logical commits that represent a single unit of work.
*   Follow **Conventional Commits** specification for commit messages. This helps in generating changelogs and understanding the history.
    *   Format: `<type>[optional scope]: <description>`
    *   Examples:
        *   `feat: add user profile editing page`
        *   `fix: correct calculation for invoice totals`
        *   `docs: update installation guide for Supabase CLI`
        *   `style: refactor company list component styling`
        *   `refactor: improve performance of project data fetching`
        *   `test: add unit tests for credential encryption service`
        *   `chore: update eslint configuration`
    *   Common types: `feat`, `fix`, `docs`, `style`, `refactor`, `perf`, `test`, `build`, `ci`, `chore`, `revert`.

### Keeping Your Branch Updated
*   Before submitting a Pull Request, and periodically during development, update your branch with the latest changes from the `develop` branch:
    ```bash
    git checkout develop
    git pull origin develop
    git checkout your-feature-branch
    git rebase develop
    ```
    Resolve any merge conflicts that arise during the rebase.

## Coding Standards & Conventions

### Language
*   **TypeScript:** This project uses TypeScript. Adhere to strong typing and leverage TypeScript features for better code quality and maintainability.
*   Refer to the project's `tsconfig.json` for specific compiler options and strictness settings.

### Frameworks & Libraries
*   **Next.js:** Follow Next.js best practices, especially regarding the App Router, Server Components, Client Components, and API Route Handlers.
*   **React:** Employ React best practices for component design, state management, and hooks.
*   **Supabase:** Utilize the Supabase client libraries as shown in existing code patterns for database interactions and authentication.

### Styling
*   **Tailwind CSS:** Adhere to Tailwind CSS utility-first principles. Define custom styles or components in a structured manner if necessary, but prefer utility classes where possible.

### Linting & Formatting
*   **ESLint:** The project is configured with ESLint (see `eslint.config.mjs`). Ensure your code passes all linting checks.
    ```bash
    npm run lint
    ```
*   **Prettier (if used):** If Prettier is configured, format your code before committing.
    ```bash
    npm run format # Or equivalent script if available
    ```
    Many IDEs can be configured to format on save.

### Naming Conventions
*   **Variables & Functions:** Use `camelCase`.
*   **Components (React):** Use `PascalCase`.
*   **Files (Components):** Use `PascalCase.tsx` (e.g., `UserProfile.tsx`).
*   **Files (API Routes, Libs):** Use `kebab-case.ts` (e.g., `list-users.ts`, `auth-helpers.ts`).

### Comments
*   Write clear and concise comments to explain complex logic, non-obvious decisions, or important context.
*   Use JSDoc-style comments for functions and modules where appropriate to describe parameters, return values, and purpose.

### API Design (for backend contributions)
*   Follow existing patterns in `app/api/` for consistency in request/response structures, error handling, and status codes.
*   Ensure API routes have appropriate authentication and authorization checks. This includes considering public accessibility for certain endpoints (like `GET /api/direct-contacts`) versus restricted access (like `POST /api/companies/onboard`).
*   **Document new or modified API endpoints thoroughly.** This is crucial for maintainability and collaboration. Ensure that:
    *   The [Product Requirements Document (PRD.md)](PRD.md) is updated to accurately reflect the new functionality or changes.
    *   The endpoint's details (path, method, request/response body, headers, example usage, and specific authentication/authorization requirements) are clearly documented in the [Usage API Guide (Usage_API_Guide.md)](Usage_API_Guide.md).
    *   If the API introduces new authentication mechanisms or utilizes existing ones in a unique way, update [Authentication.md](Authentication.md) accordingly.
    *   Any significant architectural impact is noted in the [System Architecture document (System_Architecture.md)](System_Architecture.md).

## Testing Protocols

*(Note: This section should be updated based on the actual testing setup of the project. If testing is not yet formally established, this section can outline aspirations or be marked as an area needing contribution.)*

We aim to have a robust suite of tests to ensure code quality and prevent regressions.

### Types of Tests Expected
*   **Unit Tests:** For individual functions, components (especially UI logic), and utility modules.
    *   Frameworks: [Jest](https://jestjs.io/), [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/) (or equivalents).
*   **Integration Tests:** To test interactions between components or modules, such as API route handlers interacting with database services.
*   **End-to-End (E2E) Tests:** To simulate user flows through the application.
    *   Frameworks: [Playwright](https://playwright.dev/), [Cypress](https://www.cypress.io/) (or equivalents).

### Running Tests
*   Execute tests using the project's test script:
    ```bash
    npm test # Or equivalent script
    ```

### Writing Tests
*   Write tests that are clear, concise, and easy to understand.
*   Cover positive paths, negative paths, and edge cases.
*   Test files should typically reside alongside the code they are testing (e.g., `component.test.tsx`) or in a dedicated `__tests__` directory.

### Test Coverage
*   While we strive for high test coverage, the immediate focus is on ensuring critical paths and complex logic are well-tested. Contributions that include relevant tests are highly encouraged.

## Pull Request (PR) Process

### Creating a Pull Request
1.  Push your feature or bugfix branch to the remote repository (e.g., your fork or the main repository if you have push access).
    ```bash
    git push origin your-branch-name
    ```
2.  Open a Pull Request (PR) from your branch to the `develop` branch of the main repository.
3.  **PR Title:** Use a clear and descriptive title, similar to the commit message conventions (e.g., `feat: Implement user password reset functionality`).
4.  **PR Description:**
    *   Summarize the changes made.
    *   Explain the "why" behind the changes.
    *   Link to any relevant GitHub issues (e.g., `Closes #123`, `Fixes #456`).
    *   **Confirm that relevant documentation (e.g., [PRD.md](PRD.md), [Usage_API_Guide.md](Usage_API_Guide.md), [Authentication.md](Authentication.md)) has been updated or verified as not needing updates due to the changes in the PR.**
    *   Include screenshots or GIFs for UI changes if helpful.
    *   Note any specific areas that reviewers should focus on.

### Code Review
*   Once a PR is submitted, project maintainers or other contributors will review the code.
*   Be prepared to discuss your changes and address any feedback or questions from reviewers.
*   Make necessary updates to your branch based on the review feedback. Push these changes to your branch; the PR will update automatically.
*   We may require at least one (or more) approvals before a PR can be merged, depending on the complexity and impact of the changes.

### Automated Checks / Continuous Integration (CI)
*   If CI pipelines are configured (e.g., using GitHub Actions), your PR will trigger automated checks. These might include:
    *   Linting and code style checks.
    *   Running unit and integration tests.
    *   Build checks.
*   All automated checks must pass before a PR can be merged.

### Merging
*   Once the PR is approved and all checks have passed, a project maintainer will merge it into the `develop` branch.
*   The merge strategy (e.g., merge commit, squash and merge, rebase and merge) will be decided by the maintainers, but typically feature branches are squashed or rebased for a cleaner history.

## Code of Conduct

All contributors are expected to adhere to the project's Code of Conduct. Please ensure you are familiar with it. (If a `CODE_OF_CONDUCT.md` file exists, link to it here. Otherwise, a brief statement on respectful and collaborative behavior can be included).
*Example: We are committed to providing a welcoming and inclusive environment. Please be respectful and considerate in all interactions.*

## Questions & Getting Help

If you have questions about contributing, the codebase, or a specific issue, please:
*   Check existing documentation first.
*   Use GitHub Issues for bug reports and feature-specific discussions.
*   Use GitHub Discussions (if enabled) for broader questions or architectural discussions.
*   (Specify any other preferred communication channels, e.g., a project Slack or Discord server).

Thank you for contributing to Developer CRM!