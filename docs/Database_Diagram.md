# Developer CRM - Database Relationship Diagram

## Overview

This document provides a visual representation of the database schema and relationships between tables in the Developer CRM application using a Mermaid Entity Relationship Diagram (ERD).

## Entity Relationship Diagram (Mermaid)

```mermaid
erDiagram
    companies {
        uuid id PK
        text name
        text industry
        text size
        text website
        text address
        text logo_url
        text status
        text notes
        timestamptz created_at
        timestamptz updated_at
    }

    contacts {
        uuid id PK
        uuid company_id FK
        text name
        text email
        text phone
        text position
        boolean is_primary
        text notes
        timestamptz created_at
        timestamptz updated_at
    }

    projects {
        uuid id PK
        uuid company_id FK
        text name
        text description
        numeric value
        text phase
        date start_date
        date expected_end_date
        date actual_end_date
        text status
        text notes
        timestamptz created_at
        timestamptz updated_at
    }

    tasks {
        uuid id PK
        uuid project_id FK
        text name
        text description
        text priority
        text status
        text task_type
        date due_date
        uuid assigned_to FK "Refers to auth.users.id"
        timestamptz created_at
        timestamptz updated_at
    }

    credentials {
        uuid id PK
        uuid company_id FK
        uuid project_id FK
        text name
        text credential_type
        text url
        text username
        text encrypted_password "Stored encrypted"
        date expiry_date
        text notes
        timestamptz created_at
        timestamptz updated_at
        timestamptz password_last_updated
    }

    invoices {
        uuid id PK
        uuid company_id FK
        uuid project_id FK
        text invoice_number
        date issue_date
        date due_date
        numeric amount
        text status
        date payment_date
        text notes
        text pdf_url
        timestamptz created_at
        timestamptz updated_at
    }

    invoice_items {
        uuid id PK
        uuid invoice_id FK
        text description
        integer quantity
        numeric unit_price
        numeric amount
        uuid expense_id FK
        uuid service_id FK
    }

    services {
        uuid id PK
        text name
        text service_type
        text description
        numeric price
        boolean is_active
        timestamptz created_at
        timestamptz updated_at
    }

    project_services {
        uuid project_id PK, FK
        uuid service_id PK, FK
        integer quantity
        numeric price
    }

    activities {
        uuid id PK
        uuid company_id FK
        uuid project_id FK
        uuid contact_id FK
        text name
        text activity_type
        timestamptz date
        text details
        text status
        timestamptz created_at
        timestamptz updated_at
    }

    expenses {
        uuid id PK
        uuid company_id FK
        uuid project_id FK
        text name
        numeric amount
        text category
        date date
        text payment_method
        text receipt_url
        text notes
        boolean is_reimbursable
        text reimbursement_status
        timestamptz created_at
        timestamptz updated_at
    }

    user_roles {
        uuid id PK
        uuid user_id FK "Refers to auth.users.id"
        text role "CHECK ('admin', 'user')"
        timestamptz created_at
        timestamptz updated_at
    }

    client_portals {
        uuid id PK
        uuid company_id FK
        text name
        text access_token "Likely legacy"
        boolean is_active
        timestamptz last_accessed
        timestamptz created_at
        timestamptz updated_at
    }

    client_users {
        uuid id PK
        uuid company_id FK
        text email "UNIQUE"
        text password_hash
        boolean is_active
        timestamptz created_at
        timestamptz updated_at
        timestamptz last_login
    }

    client_sessions {
        uuid id PK
        text token "UNIQUE"
        uuid user_id FK "Refers to client_users.id"
        uuid company_id FK
        timestamptz created_at
        timestamptz expires_at
    }

    client_companies {
        uuid id PK
        uuid user_id FK "Refers to auth.users.id"
        uuid company_id FK
        timestamptz created_at
        timestamptz updated_at
    }
    
    shared_credentials {
        uuid id PK
        uuid credential_id FK
        boolean is_shared_with_client
        timestamptz created_at
        timestamptz updated_at
    }

    shared_tasks {
        uuid id PK
        uuid task_id FK
        boolean is_shared_with_client
        timestamptz created_at
        timestamptz updated_at
    }

    "auth.users" {
        uuid id PK "Supabase Auth User ID"
        text email
        // Other Supabase internal columns
    }


    companies ||--o{ contacts : "has"
    companies ||--o{ projects : "has"
    companies ||--o{ credentials : "can_have"
    companies ||--o{ invoices : "receives"
    companies ||--o{ activities : "related_to"
    companies ||--o{ expenses : "can_have"
    companies ||--o{ client_portals : "has_one"
    companies ||--o{ client_users : "has_many"
    companies ||--o{ client_companies : "linked_to_staff"

    projects ||--o{ tasks : "contains"
    projects ||--o{ credentials : "can_have"
    projects ||--o{ invoices : "can_be_for"
    projects ||--o{ activities : "related_to"
    projects ||--o{ expenses : "incurs"
    projects ||--|{ project_services : "uses"

    contacts ||--o{ activities : "involved_in"
    
    tasks ||--o{ shared_tasks : "can_be"
    "auth.users" ||--o{ tasks : "assigned_to"
    "auth.users" ||--o{ user_roles : "has_role"
    "auth.users" ||--o{ client_companies : "manages"
    
    credentials ||--o{ shared_credentials : "can_be"

    invoices ||--o{ invoice_items : "has_items"

    services ||--|{ project_services : "used_in"
    services ||--o{ invoice_items : "can_be"
    
    expenses ||--o{ invoice_items : "can_be"

    client_users ||--o{ client_sessions : "has_session"
```

## Relationships Explanation (Summary)

This diagram visually represents the primary relationships. Key points:
-   A `company` can have many `contacts`, `projects`, `credentials`, `invoices`, `client_users`, etc.
-   `projects` belong to a `company` and can have many `tasks`, `expenses`, etc.
-   Admin/team user authentication is managed by Supabase's `auth.users` table, with application-specific roles in `user_roles`.
-   Client portal authentication uses the `client_users` and `client_sessions` tables, scoped to a `company`.
-   The `client_companies` table links `auth.users` (staff) to `companies`.
-   Junction tables like `project_services`, `shared_credentials`, and `shared_tasks` manage many-to-many type relationships or sharing states.

(The previous detailed textual explanations of relationships can be removed or significantly condensed as the Mermaid diagram is now the primary source of this information.)

## Additional Notes
- Row-level security (RLS) is applied to these tables to enforce data access policies based on user roles and client associations.
- Sensitive information, particularly in the `credentials` table, is encrypted.