# Plan: Phase 2, Step 2 - System Architecture Document (`docs/System_Architecture.md`)

**Goal:** To provide a high-level overview of the Developer CRM's system design, its key components, their interactions, and data flow.

---

## Proposed Outline for `docs/System_Architecture.md`

1.  **Introduction**
    *   Purpose: Describe Developer CRM system architecture.
    *   Audience: Developers.
    *   Architectural goals (e.g., scalability, maintainability, security).

2.  **High-Level Architecture Overview (C4 Model - Level 1: System Context Diagram - Conceptual)**
    *   Diagram: Developer CRM system interacting with Admin/Team Users, Client Users, and Supabase.
    *   **Mermaid Diagram (System Context):**
        ```mermaid
        graph TD
            AdminUser[Admin/Team User] -- Manages CRM via Web App --> DevCRM[Developer CRM System]
            ClientUser[Client User] -- Accesses Portal via Web App --> DevCRM
            DevCRM -- Uses for Auth, DB, Storage --> Supabase[Supabase Backend]
        ```
    *   Brief explanation.

3.  **Key Components & Technologies (C4 Model - Level 2: Container Diagram - Conceptual)**
    *   **Frontend Application (Next.js Web Application):**
        *   Technology: Next.js, React, TypeScript, Tailwind CSS.
        *   Responsibilities: UI, client-side logic, API interactions, routing.
        *   State Management: React Query for server state, React Context for global UI state.
    *   **Backend Services (Supabase):**
        *   **Supabase Auth:** Handles Admin/Team user authentication.
        *   **Supabase Database (PostgreSQL):** Primary data storage.
        *   **Supabase Storage (if used):** For file storage.
    *   **Next.js API Routes (`app/api/`):**
        *   Responsibilities: Handling client requests, Supabase interactions (DB functions, service role queries), custom logic (e.g., client portal auth).
    *   **Mermaid Diagram (Containers/Components):**
        ```mermaid
        graph TD
            subgraph UserInterfaces["User Interfaces"]
                WebApp[Next.js Frontend Application]
            end
            subgraph BackendSystems["Backend Systems & Data"]
                NextApiRoutes[Next.js API Routes]
                SupabaseDB[Supabase PostgreSQL Database]
                SupabaseAuth[Supabase Authentication]
                SupabaseStorage[Supabase Storage (Optional)]
            end
            WebApp -- HTTP API Calls --> NextApiRoutes
            WebApp -- Direct SDK Calls for Auth/Data --> SupabaseAuth
            WebApp -- Direct SDK Calls for Data (RLS Enforced, via React Query) --> SupabaseDB
            WebApp -- Accesses Files --> SupabaseStorage
            NextApiRoutes -- Service Role Access --> SupabaseDB
            NextApiRoutes -- Interacts With --> SupabaseAuth
            NextApiRoutes -- Interacts With --> SupabaseStorage
        ```
    *   Detailed explanation of each component.

4.  **Authentication & Authorization Architecture**
    *   **Admin/Team Member Authentication:** Flow (Login -> Supabase Auth -> JWT -> Session), Authorization (RBAC via `public.user_roles`).
    *   **Client User Authentication (Client Portal):** Flow (Login -> Custom API -> `public.client_users` -> `public.client_sessions` -> Cookie), Authorization (Session token validation, RLS via `client_sessions.company_id`).
    *   Reference `docs/Authentication.md`.

5.  **Data Management & Storage**
    *   Primary Database: Supabase PostgreSQL.
    *   Key entities (refer to `docs/Database_Schema.md`, `docs/Database_Diagram.md`).
    *   Data security: RLS, encryption.
    *   Migrations: `supabase/migrations`.
    *   File Storage: Supabase Storage (if applicable).

6.  **Frontend Architecture**
    *   Next.js App Router (`app/` directory).
    *   Patterns: Server Components, Client Components, API Route Handlers.
    *   State Management: React Query for server state, React Context for global UI state.
    *   Styling: Tailwind CSS.

7.  **API Design**
    *   RESTful APIs via Next.js API Routes.
    *   Supabase client libraries (RLS respected).
    *   Database functions/RPCs for complex/secure logic.

8.  **Deployment Architecture (High-Level)**
    *   Frontend: Vercel, Netlify, or Node.js hosting.
    *   Backend: Supabase (managed service).
    *   Environment variable management.

9.  **Key Data Flows (Illustrative Examples)**
    *   Admin User Login and Data Fetch.
    *   Client User Login and Portal Data Fetch.
    *   Creating a new Project by an Admin.
    *   (Consider Mermaid sequence diagrams for these).

10. **Scalability & Performance Considerations (Briefly)**
    *   Supabase scaling.
    *   Frontend optimization (Next.js).

11. **Security Considerations (Summary)**
    *   Supabase Auth, RLS, custom client auth, input validation, HTTPS.
    *   Refer to other documents.

---