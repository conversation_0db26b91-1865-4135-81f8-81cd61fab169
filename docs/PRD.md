# Developer CRM - Product Requirements Document

## Executive Summary
Developer CRM is a comprehensive customer relationship management system designed specifically for independent developers and small development teams. It centralizes client information, project management, secure credential storage, and invoicing in one platform.

## Product Overview
The Developer CRM serves as a centralized hub for managing client relationships, projects, and finances. It offers:
- Client company management
- Contact tracking
- Project and task tracking
- Secure credential storage with encryption
- Invoicing and payment tracking
- Activity logging and calendar integration

## User Personas
1. **Independent Developer**: Freelancers managing multiple clients and projects
2. **Small Dev Teams**: Teams of 2-5 developers collaborating on client work
3. **Development Agency Owner**: Managing client relationships and team workload
4. **Project Manager**: Tracking progress and task completion

## Core Features

### 1. Company Management
- Store and organize client company information
- Track company status and relationships
- Filter and search capabilities
- Industry categorization
- Streamlined company onboarding process via `POST /api/companies/onboard` endpoint, allowing creation of a company along with an optional initial project and primary contact.

### 2. Contact Management
- Associate contacts with companies
- Store contact details and communication preferences
- Log communication history
- Track relationship status
- Contacts can be initially created as part of the company onboarding process (`POST /api/companies/onboard`).
- Retrieve a list of all contacts via `GET /api/direct-contacts` endpoint.

### 3. Project Management
- Create and track client projects
- Assign tasks and deadlines
- Monitor project progress
- Store project details and requirements
- Track billable hours
- Initial project setup can be part of the company onboarding flow (`POST /api/companies/onboard`).

### 4. Secure Credential Storage
- Encrypted storage for client credentials
- Access control based on user roles
- Password masking and secure viewing
- Expiry tracking and notification

### 5. Invoice Management
- Generate professional invoices
- Track payment status
- Send payment reminders
- Record payment history

### 6. Dashboard & Reporting
- Overview of active projects and tasks
- Financial insights and forecasting
- Client relationship status
- Upcoming deadlines and schedule

## User Flows

### Company & Contact Management
1. User creates a new company profile, potentially with an initial project and contact, through an onboarding process (e.g., via `POST /api/companies/onboard`).
2. User adds further associated contacts (initial contact can be part of onboarding). All contacts can be listed via `GET /api/direct-contacts`.
3. User can search, filter, and view company/contact details
4. User logs activities and communications with clients

### Project & Task Management
1. User creates a new project associated with a company (an initial project can be created during company onboarding via `POST /api/companies/onboard`).
2. User adds tasks, deadlines, and assigns resources
3. User tracks progress and updates status
4. User logs time spent on billable activities

### Credential Management
1. User securely stores client credentials
2. User controls access to sensitive information
3. System tracks credential expiry
4. User can securely decrypt and view credentials as needed

### Invoice Process
1. User creates invoice based on completed work
2. User sends invoice to client
3. User tracks payment status
4. System generates reports on outstanding payments

## Technical Requirements
- Next.js frontend with TypeScript
- Supabase backend for database and authentication
- Tailwind CSS for styling
- React Query for data fetching
- Secure credential handling with encryption
- Responsive design for mobile and desktop

## Security Considerations
- Row Level Security (RLS) policies to control data access
- Encryption for credential storage
- Role-based access control
- Client data visibility controls
- Secure authentication and authorization
- Regular security audits and logging

## Timeline Estimation
- Phase 1 (Basic Functionality): 2-3 weeks
  - Complete CRUD operations for all main entities
  - Finish detail pages and forms
  - Implement basic authentication

- Phase 2 (Enhanced Features): 2-3 weeks
  - Task management system
  - Expense tracking
  - Calendar integration
  - Reporting basics

- Phase 3 (Advanced Features): 3-4 weeks
  - Client portal
  - Advanced reporting
  - Mobile optimization
  - Security hardening