# Plan: Phase 1, Step 1 - Global Correction of Admin Authentication Information

**Goal:** Systematically update all existing documentation to accurately reflect that Supabase Auth is used for admin authentication, not Clerk. This includes explaining the roles of the `auth.users` table (Supabase's built-in user table) and the `public.user_roles` table (for application-specific roles like 'admin' or 'user').

---

## 1. `README.md`

*   **Current State References to Clerk:**
    *   Line 42: "Admin authentication is handled by Clerk"
    *   Line 59: "Configure your Clerk application for production"
*   **Proposed Changes:**
    *   **Line 42:** Change to "Admin authentication is handled by **Supabase Auth**, leveraging Supabase's built-in user management."
    *   **Line 48 (Role-Based Access Control Context):** Add a brief mention: "User roles (e.g., 'admin', 'user') are managed in the `public.user_roles` table, linking to Supabase's `auth.users` table."
    *   **Line 59:** Change to "Ensure your Supabase project (including Auth settings, database, and API keys) is properly configured for production."
*   **Rationale:** To correct outdated information and provide accurate context regarding the authentication mechanism and deployment.

---

## 2. `docs/Authentication.md`

*   **Current State References to Clerk:**
    *   Line 7 (Section Title): "Admin Authentication (Clerk)"
    *   Line 16: "The application uses [Clerk](https://clerk.dev) for admin authentication..."
    *   Line 339 (Conclusion): "For Admins (via Clerk)"
*   **Proposed Changes:**
    *   **Section Title (approx. Line 7):** Change to "Admin Authentication (Supabase Auth)".
    *   **Introduction to Admin Auth (approx. Line 16):** Rewrite to state: "The application uses [Supabase Auth](https://supabase.com/docs/guides/auth) for admin and team member authentication. Supabase Auth provides robust user management, authentication flows (sign-in, sign-up, password recovery), session management, and profile management directly within the Supabase ecosystem."
    *   **Configuration Section (Lines 23-37):** Retain this section as it accurately describes Supabase setup, ensuring wording is neutral if any implicit Clerk context exists.
    *   **First-Time Setup (Lines 38-50):** Review and update this section to describe the first admin user creation process specifically with Supabase Auth and the `public.user_roles` table.
    *   **Sign-Up Restriction (Lines 52-65):** Update to explain how admin-only user creation is achieved with Supabase Auth (disabling public sign-ups, application logic for creating `auth.users` entries via Supabase API and corresponding `public.user_roles` entries).
    *   **Conclusion (approx. Line 339):** Change "For Admins (via Clerk)" to "For Admins (via Supabase Auth)". Update bullet points to reflect Supabase Auth benefits (e.g., "Integration with Supabase database and RLS via JWTs and `auth.uid()`").
*   **Rationale:** To completely re-align this core authentication document with Supabase Auth, ensuring all explanations and concepts accurately reflect the current implementation.

---

## 3. `docs/Client_Portal_Access.md`

*   **Current State Reference to Clerk:**
    *   Line 11: "Admin Authentication (Clerk)"
*   **Proposed Changes:**
    *   **Line 11:** Change to "Admin Authentication (Supabase Auth)". Add a brief note: "This system manages access for CRM administrators and team members, distinct from the client authentication described below. It utilizes Supabase's built-in authentication and the `public.user_roles` table for role management."
*   **Rationale:** To correct the outdated reference and clearly delineate between the admin auth system (Supabase Auth) and the custom client auth system.

---

## 4. `docs/ToDo.md`

*   **Current State Reference to Clerk:**
    *   Line 25: "- [x] Set up Clerk authentication"
*   **Proposed Changes:**
    *   **Line 25:** Change to "- [x] Set up Supabase authentication (for admin/team users)".
    *   **Line 31 ("Implement role-based permissions"):** Note that this is likely related to `public.user_roles` and its current implementation status should be considered when updating this item.
*   **Rationale:** To reflect the correct authentication system in the project's task list.

---