# Authentication in Developer CRM

## Overview

Developer CRM uses a dual authentication system:

1. **Admin Authentication (Supabase Auth)**: For admin and team member access to the main CRM
2. **Client Authentication (Custom)**: For client access to their dedicated portals

This document explains how both authentication systems work and how they interact with the Supabase database.

## Admin Authentication with Supabase Auth

### Setup

The application uses [Supabase Auth](https://supabase.com/docs/guides/auth) for admin authentication, which provides:

- User management
- Authentication flows (sign-in, sign-up)
- Session management
- Profile management

### Configuration

Supabase Auth is configured in the following files:

1. **`.env.local`**: Contains Supabase API keys
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
   ```

2. **`app/layout.tsx`**: Wraps the application in a `SupabaseProvider`
3. **`middleware.ts`**: Protects routes that require authentication by checking Supabase session
4. **`lib/supabase/server.ts`**: Provides server-side Supabase clients

### First-Time Setup

The application includes a special first-time setup process for the initial administrator:

1. When no users exist in Supabase Auth (`auth.users` table), the `/setup` page is available.
2. This page allows creating the first user account directly in Supabase Auth.
3. Upon successful creation in Supabase Auth, the application logic assigns this user the 'admin' role in the `public.user_roles` table.
4. After the first admin user is created and their role is set, the `/setup` page becomes inaccessible.
5. This ensures a secure initial setup without requiring manual database intervention for the first admin.

To set up the application for the first time:
1. Navigate to `/setup` in your browser.
2. Create your admin account with an email and password. This account will be created in Supabase Auth.
3. The system will then assign the 'admin' role to this account in the `public.user_roles` table.
4. You'll be automatically signed in using Supabase Auth and redirected to the dashboard.

### Sign-Up Restriction

After the initial admin setup, public sign-up is disabled. New user accounts (for other administrators or team members) can only be created by existing administrators:

1.  **Disabled Public Sign-Up:** Supabase Auth settings are configured to prevent new users from signing up publicly.
2.  **Admin User Creation:**
    *   Administrators use the user management interface (typically at `/settings/users`).
    *   When an admin creates a new user, the application interacts with Supabase Auth to create the user in the `auth.users` table.
    *   Simultaneously, the application creates an entry in the `public.user_roles` table, assigning the appropriate role (e.g., 'admin' or 'user') to the new Supabase user ID.
3.  **Password Resets:** Administrators can also initiate password resets for existing users through Supabase Auth mechanisms, typically managed via the admin interface.

The admin user management interface provides:
- Creating new user accounts (which involves creating a Supabase Auth user and assigning a role in `public.user_roles`).
- Resetting passwords for existing users (leveraging Supabase Auth).
- Viewing all user accounts (from `auth.users`) and their assigned roles (from `public.user_roles`).

### Security Measures

The application implements several security measures to protect against common attacks:

1. **API Endpoint Protection**:
   - Admin API endpoints are generally protected with multiple layers of authentication.
   - Requests are typically validated for proper origin, content type, and method.
   - Non-admin users usually cannot access admin-specific endpoints.
   - However, authentication mechanisms can vary per endpoint. Notable examples related to recent features include:
     - **`POST /api/companies/onboard`**: This endpoint is used for streamlined company creation. It requires an authenticated Admin or Team User, verified via a Supabase JWT (JSON Web Token) passed in cookies.
     - **`GET /api/direct-contacts`**: This endpoint is designed for public access to retrieve all contacts. It utilizes an anonymous Supabase client, meaning it does not require user authentication and bypasses user-specific Row Level Security (RLS) for the data it serves.

2. **Rate Limiting**:
   - API requests are rate-limited to prevent abuse
   - Login attempts are rate-limited to prevent brute force attacks
   - Temporary lockouts are implemented after multiple failed attempts

3. **CSRF Protection**:
   - All API endpoints validate the request origin
   - Requests must come from the same host as the application

4. **Security Logging**:
   - Security events are logged for auditing purposes
   - Login attempts, admin actions, and security violations are tracked

5. **Error Handling**:
   - Generic error messages are shown to prevent information leakage
   - Detailed errors are logged server-side for debugging

## Client Authentication (Custom)

### Database Tables

Client authentication uses the following tables in Supabase:

1. **`client_users`**: Stores client user credentials. (Note: The live schema includes `id`, `company_id`, `email`, `password_hash`, `is_active`, `created_at`, `updated_at`, and `last_login`. Fields like `first_name`, `last_name`, and `created_by` are not present in the current live table.)
   - `id`: Primary key (UUID)
   - `company_id`: Foreign key to `companies.id` (UUID)
   - `email`: Client's email address (TEXT, unique)
   - `password_hash`: Hashed password (TEXT)
   - `is_active`: Whether the account is active (BOOLEAN, default: true)
   - `created_at`: Creation timestamp (TIMESTAMPTZ, default: now())
   - `updated_at`: Last update timestamp (TIMESTAMPTZ, default: now())
   - `last_login`: Last login timestamp (TIMESTAMPTZ)

2. **`client_sessions`**: Stores active client sessions
   - `id`: Primary key
   - `token`: Session token
   - `user_id`: Foreign key to client_users
   - `company_id`: Foreign key to companies
   - `created_at`: Creation timestamp
   - `expires_at`: Expiration timestamp

### Client User Management

Client users are managed by admin users through a dedicated interface:

1. **Creating Client Users**:
   - Admins navigate to the company's client users page
   - Fill out the client user form with email and password. (Note: Name fields are not directly on the `client_users` table).
   - System creates the user with the `create_client_user` function
   - Client receives login credentials (manually shared by admin)

2. **Managing Client Users**:
   - Admins can view all client users for a company
   - Admins can reset passwords
   - Admins can activate/deactivate accounts

### Authentication Flow

1. **Client Login**:
   - Client enters email and password on `/client-login`
   - Request is sent to `/api/client-auth/login`
   - Server verifies credentials using `verify_client_credentials` RPC function
   - On success, creates a session and sets a session cookie
   - Redirects to client portal view

2. **Session Verification**:
   - Each request to `/api/client-portal/data` includes the session cookie
   - Server verifies the session token and checks expiration
   - Server ensures the requested company matches the session company
   - On success, returns the requested data

3. **Logout**:
   - Client clicks logout button
   - Request is sent to `/api/client-auth/logout`
   - Server deletes the session and clears the cookie
   - Redirects to login page

### Security Measures

- Passwords are hashed using pgcrypto's crypt function
- Sessions expire after 7 days
- Session tokens are cryptographically secure UUIDs
- Row Level Security ensures clients can only access their own data
- Failed login attempts are logged
- Client users can be deactivated instantly by admins

## Integration with Supabase

### Database Functions

The following Supabase database functions handle client authentication:

1. **`create_client_user`**: Creates a new client user
   ```sql
   CREATE OR REPLACE FUNCTION public.create_client_user(
     p_company_id UUID,
     p_email TEXT,
     p_password TEXT
     -- Note: p_first_name, p_last_name, p_created_by are removed
     -- as they are not in the live client_users table schema.
   ) RETURNS UUID
   LANGUAGE plpgsql
   SECURITY DEFINER
   AS $$
   DECLARE
     v_user_id UUID;
   BEGIN
     -- Check if email already exists
     IF EXISTS (SELECT 1 FROM public.client_users WHERE email = p_email) THEN
       RAISE EXCEPTION 'Email already exists';
     END IF;

     -- Insert new client user
     INSERT INTO public.client_users (
       company_id,
       email,
       password_hash
       -- first_name, last_name, created_by are removed
     ) VALUES (
       p_company_id,
       p_email,
       crypt(p_password, gen_salt('bf'))
       -- p_first_name, p_last_name, p_created_by are removed
     ) RETURNING id INTO v_user_id;

     RETURN v_user_id;
   END;
   $$;
   ```

2. **`verify_client_credentials`**: Verifies client login credentials
   ```sql
   CREATE OR REPLACE FUNCTION public.verify_client_credentials(
     p_email TEXT,
     p_password TEXT
   ) RETURNS BOOLEAN
   LANGUAGE plpgsql
   SECURITY DEFINER
   AS $$
   DECLARE
     v_stored_hash TEXT;
     v_is_active BOOLEAN;
   BEGIN
     -- Get the stored password hash and active status
     SELECT password_hash, is_active INTO v_stored_hash, v_is_active
     FROM public.client_users
     WHERE email = p_email;

     -- If no user found or user is inactive, return false
     IF v_stored_hash IS NULL OR NOT v_is_active THEN
       RETURN FALSE;
     END IF;

     -- Check if password matches (using pgcrypto's crypt function)
     RETURN v_stored_hash = crypt(p_password, v_stored_hash);
   END;
   $$;
   ```

3. **`reset_client_password`**: Resets a client's password
   ```sql
   CREATE OR REPLACE FUNCTION public.reset_client_password(
     p_user_id UUID,
     p_new_password TEXT
   ) RETURNS BOOLEAN
   LANGUAGE plpgsql
   SECURITY DEFINER
   AS $$
   BEGIN
     UPDATE public.client_users
     SET password_hash = crypt(p_new_password, gen_salt('bf')),
         updated_at = NOW()
     WHERE id = p_user_id;

     RETURN FOUND;
   END;
   $$;
   ```

### Row Level Security (RLS)

Supabase RLS policies ensure data security:

1.  **Admin/Team Users (`auth.users` and `public.user_roles`):**
    *   Access to the `auth.users` table (Supabase's built-in user table) is typically managed by Supabase's own internal policies.
    *   RLS policies for application data that needs to be restricted based on admin/team user roles would typically check against the `public.user_roles` table. For example, a policy on a sensitive table might look like:
    ```sql
    -- Example RLS policy for a hypothetical 'admin_settings' table
    CREATE POLICY "Admins can access admin_settings"
    ON public.admin_settings FOR ALL
    USING (
      EXISTS (
        SELECT 1 FROM public.user_roles
        WHERE user_id = auth.uid() AND role = 'admin'
      )
    );
    ```
    *   The RLS policies for `public.users` shown below are likely outdated or refer to a previous schema structure. The current system uses `auth.users` for user identities and `public.user_roles` for application-level roles.

    *(The following policies on `public.users` are likely outdated as user identity is in `auth.users` and roles in `public.user_roles`)*
   ```sql
   -- Outdated: CREATE POLICY "Users can view their own data" ON public.users
   -- FOR SELECT USING (auth.uid() = id);

   -- Outdated: CREATE POLICY "Admins can view all users" ON public.users
   -- FOR SELECT USING (
   -- EXISTS (
   -- SELECT 1 FROM public.users
   -- WHERE id = auth.uid() AND is_admin = TRUE
   -- )
   -- );
   ```

2. **Client Users Table**:
   ```sql
   CREATE POLICY "Admins can manage client users" ON public.client_users
     USING (
       EXISTS (
         SELECT 1 FROM public.users
         WHERE id = auth.uid() AND is_admin = TRUE
       )
     );
   ```

3. **Companies Table**:
   ```sql
   CREATE POLICY "Clients can only view their own company"
   ON companies FOR SELECT
   USING (id IN (
     SELECT company_id FROM client_sessions
     WHERE token = current_setting('request.headers.client-session', true)::text
   ));
   ```

4. **Projects Table**:
   ```sql
   CREATE POLICY "Clients can only view their company's projects"
   ON projects FOR SELECT
   USING (company_id IN (
     SELECT company_id FROM client_sessions
     WHERE token = current_setting('request.headers.client-session', true)::text
   ));
   ```

Similar policies are applied to all relevant tables.

## Best Practices

1. **Secure Password Handling**:
   - Never store plaintext passwords
   - Use strong hashing algorithms (pgcrypto with bcrypt)
   - Implement password complexity requirements

2. **Session Management**:
   - Use secure, HttpOnly cookies
   - Implement session expiration
   - Allow admins to manage client sessions

3. **Access Control**:
   - Implement principle of least privilege
   - Verify authorization on every request
   - Log access attempts

4. **Security Monitoring**:
   - Monitor failed login attempts
   - Implement rate limiting
   - Log security events

## Conclusion

The dual authentication system provides:

1. **For Admins/Team Members (via Supabase Auth)**:
   - Secure, managed authentication provided by Supabase.
   - User identity management within Supabase's `auth.users` table.
   - Application-specific role management via the `public.user_roles` table (e.g., 'admin', 'user').
   - Profile management capabilities inherent to Supabase Auth.
   - Seamless integration with Supabase database and Row Level Security via JWTs and `auth.uid()`.

2. **For Clients (via Custom System)**:
   - Controlled access to client portals
   - Admin-managed credentials
   - Secure session management
   - Limited access to only their own data

This approach allows for a clear separation between admin and client access while maintaining security and usability for both user types.
