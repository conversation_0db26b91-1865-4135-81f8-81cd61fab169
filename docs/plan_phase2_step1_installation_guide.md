# Plan: Phase 2, Step 1 - Installation Guide (`docs/Installation_Guide.md`)

**Goal:** To provide clear, step-by-step instructions for developers to set up their local development environment and get the Developer CRM application running.

---

## Proposed Outline for `docs/Installation_Guide.md`

1.  **Introduction**
    *   Purpose: Guide for local development setup.
    *   Audience: Developers.

2.  **Prerequisites**
    *   **Software:**
        *   Node.js (e.g., LTS v18.x, v20.x) + Link.
        *   npm / yarn / pnpm / bun.
        *   Git + Link.
        *   Supabase CLI (optional but recommended) + Link.
    *   **Accounts & Access:**
        *   Supabase Account + Link.
        *   GitHub account (or other VCS, if applicable).

3.  **Environment Setup**
    *   **Cloning the Repository:**
        *   `git clone <repository-url>`
        *   `cd <project-directory-name>`
    *   **Installing Dependencies:**
        *   `npm install` (or equivalent).
    *   **Environment Variables (`.env.local`):**
        *   Create from `.env.example` or manually.
        *   List required variables:
            *   `NEXT_PUBLIC_SUPABASE_URL` (how to find).
            *   `NEXT_PUBLIC_SUPABASE_ANON_KEY` (how to find).
            *   `SUPABASE_SERVICE_ROLE_KEY` (how to find, keep secret warning).
            *   `NEXT_PUBLIC_APP_URL` (e.g., `http://localhost:3000`).
            *   Other project-specific variables.

4.  **Supabase Project Setup**
    *   **Option A: Using Hosted Supabase Project:**
        *   Create new project on [supabase.com](https://supabase.com) if needed.
    *   **Option B: Supabase CLI for Local Development (Recommended):**
        *   `supabase login`
        *   `supabase init` (if new local Supabase setup)
        *   `supabase link --project-ref <your-project-ref>` (optional, to link to hosted)
        *   `supabase start` (Docker prerequisite).

5.  **Database Schema Setup (Migrations)**
    *   Explanation: Migrations in `supabase/migrations`.
    *   **Applying Migrations:**
        *   Supabase CLI (local/linked):
            *   `supabase db reset` (clean local setup).
            *   `supabase migration up` (pending to linked).
        *   Hosted Supabase (no CLI): Manual SQL application (less ideal).
    *   Creating new migrations: `supabase migration new <migration_name>`.

6.  **First-Time Application Setup (Initial Admin User)**
    *   Reiterate steps:
        *   Supabase running, env vars set.
        *   Navigate to `/setup`.
        *   Create admin account (details in `auth.users`, role in `public.user_roles`).
        *   Confirm redirection.

7.  **Running the Application**
    *   `npm run dev` (or equivalent).
    *   Access at `http://localhost:3000`.

8.  **Troubleshooting (Optional)**
    *   Common issues: Supabase connection, Docker, env var errors.

---