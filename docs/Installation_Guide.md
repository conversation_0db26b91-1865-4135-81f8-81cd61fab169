# Developer CRM - Installation Guide

## Introduction

This guide provides step-by-step instructions for developers to set up their local development environment and get the Developer CRM application running. Following these instructions will help you prepare for contributing to the project or running a local instance for testing and development.

## Prerequisites

Before you begin, ensure you have the following software installed and accounts set up:

### Software:

*   **Node.js:** Recommended LTS versions are v18.x or v20.x. You can download it from [nodejs.org](https://nodejs.org/).
*   **Package Manager:** npm (comes with Node.js), [yarn](https://yarnpkg.com/), [pnpm](https://pnpm.io/), or [bun](https://bun.sh/). This guide will primarily use `npm` in examples, but equivalents for other managers can be used.
*   **Git:** Required for cloning the repository. Download from [git-scm.com](https://git-scm.com/).
*   **Supabase CLI:** Optional but highly recommended for local Supabase development and managing database migrations. Installation instructions can be found in the [Supabase CLI documentation](https://supabase.com/docs/guides/cli).
*   **Docker:** Required if you plan to run Supabase services locally using the Supabase CLI. Download from [docker.com](https://www.docker.com/products/docker-desktop/).

### Accounts & Access:

*   **Supabase Account:** You'll need a Supabase account to create and manage your backend project. A free tier is available at [supabase.com](https://supabase.com).
*   **GitHub Account (or other VCS):** If the project code is hosted in a version control system, you'll need access to clone it.

## Environment Setup

### 1. Cloning the Repository

If you haven't already, clone the project repository to your local machine:

```bash
git clone <your-repository-url>
cd <project-directory-name> # e.g., cd developer-crm
```
Replace `<your-repository-url>` with the actual URL of the Git repository and `<project-directory-name>` with the name of the cloned folder.

### 2. Installing Dependencies

Navigate to the project's root directory and install the necessary Node.js dependencies:

```bash
npm install
```
Alternatively, use your preferred package manager:
```bash
# yarn install
# pnpm install
# bun install
```

### 3. Environment Variables (`.env.local`)

The application requires environment variables to connect to your Supabase backend and for other configurations.

1.  Create a new file named `.env.local` in the root of your project directory.
2.  If an `.env.example` file exists, you can copy its content as a template. Otherwise, add the following required variables:

    ```env
    NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
    NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_public_anon_key
    SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
    NEXT_PUBLIC_APP_URL=http://localhost:3000
    
    # Add any other project-specific environment variables here
    ```

    *   **`NEXT_PUBLIC_SUPABASE_URL`**: Your Supabase project's URL. Find this in your Supabase Dashboard: Project Settings > API > Project URL.
    *   **`NEXT_PUBLIC_SUPABASE_ANON_KEY`**: Your Supabase project's public anonymous key. Find this in your Supabase Dashboard: Project Settings > API > Project API keys > `anon` `public`.
    *   **`SUPABASE_SERVICE_ROLE_KEY`**: Your Supabase project's service role key. Find this in your Supabase Dashboard: Project Settings > API > Project API keys > `service_role` `secret`. **Important:** Keep this key secret and never expose it in client-side code or commit it to version control.
    *   **`NEXT_PUBLIC_APP_URL`**: The base URL for your local development environment.

## Supabase Project Setup

You have two main options for setting up the Supabase backend for local development:

### Option A: Using a Hosted Supabase Project (Recommended for Simplicity)

1.  Go to [supabase.com](https://supabase.com) and create a new project if you don't have one already.
2.  Once the project is created, its database will be ready. You'll use this project's URL and API keys in your `.env.local` file.
3.  Proceed to the "Database Schema Setup (Migrations)" section.

### Option B: Using Supabase CLI for Local Development (Advanced, for Isolated Environment)

This option allows you to run Supabase services (PostgreSQL, Auth, Storage, etc.) locally using Docker.

1.  **Install Supabase CLI:** If you haven't already, install the CLI following the [official instructions](https://supabase.com/docs/guides/cli/getting-started).
2.  **Log in to Supabase CLI:**
    ```bash
    supabase login
    ```
3.  **Initialize Supabase in your project:** If your project doesn't already have a `supabase` directory, navigate to your project's root and run:
    ```bash
    supabase init
    ```
    This creates a `supabase` folder with configuration files.
4.  **(Optional) Link to a remote project:** If you want your local setup to use a specific remote project's settings as a base or to manage its migrations:
    ```bash
    supabase link --project-ref <your-project-ref>
    ```
    Replace `<your-project-ref>` with the ID of your Supabase project.
5.  **Start local Supabase services:**
    ```bash
    supabase start
    ```
    This will pull necessary Docker images and start the local Supabase stack. It will output local Supabase URL, anon key, and service role key, which you should use in your `.env.local` file.

## Database Schema Setup (Migrations)

This project uses Supabase's built-in migration system. Database schema changes are defined in SQL files located in the `supabase/migrations` directory.

*   **If using Supabase CLI with a local Supabase instance (`supabase start`):**
    To set up your local database schema for the first time or to reset it to the latest state defined by migrations:
    ```bash
    supabase db reset
    ```
    This command drops the local database, recreates it, and applies all migration files from the `supabase/migrations` folder in chronological order.

*   **If using Supabase CLI linked to a hosted Supabase project:**
    To apply any pending local migrations to your linked remote database:
    ```bash
    supabase migration up
    ```
    **Caution:** Be careful when applying migrations directly to a shared or production Supabase project.

*   **If using a hosted Supabase project without the CLI:**
    You would need to manually execute the SQL from each migration file in the `supabase/migrations` directory in the correct order using the SQL Editor in your Supabase project dashboard. This is generally less convenient and more error-prone.

*   **Creating New Migrations:**
    If you make schema changes locally (e.g., using Supabase Studio or direct SQL), you can create a new migration file with:
    ```bash
    supabase migration new <your_migration_name>
    ```
    This will diff your local database against the last applied migration and generate a new SQL file. Review and edit this file as needed before committing it.

For more details, refer to the [Supabase Migrations documentation](https://supabase.com/docs/guides/database/migrations).

## First-Time Application Setup (Initial Admin User)

After setting up your environment variables and database schema:

1.  Ensure your Supabase instance (local or hosted) is running.
2.  The Developer CRM application has a special one-time setup page for creating the first administrator account. When no users exist in the Supabase Auth system (`auth.users` table) for your project, the `/setup` route will be accessible.
3.  Navigate to `http://localhost:3000/setup` in your browser.
4.  Fill in the form to create your admin account (email and password). This will create a user in Supabase Auth.
5.  The application will then automatically assign the 'admin' role to this new user in the `public.user_roles` table.
6.  Once the first admin is successfully created, the `/setup` page will no longer be accessible.
7.  You should be automatically logged in and redirected to the application dashboard.

For more details on the authentication systems, refer to the [`docs/Authentication.md`](docs/Authentication.md) document.

## Running the Application

Once all the above steps are completed, you can run the development server:

```bash
npm run dev
```
Or, using your preferred package manager:
```bash
# yarn dev
# pnpm dev
# bun dev
```
The application should now be accessible at `http://localhost:3000`.

## Troubleshooting

*   **Supabase Connection Issues:**
    *   Double-check your `.env.local` file for correct Supabase URL and keys.
    *   Ensure your Supabase project (local or hosted) is running and accessible.
    *   Check network connectivity and any firewalls.
*   **Docker Not Running (for local Supabase CLI):**
    *   Ensure Docker Desktop is installed and running before executing `supabase start`.
*   **Environment Variable Misconfiguration:**
    *   Verify that all required environment variables are present and correctly spelled in `.env.local`.
    *   Restart the Next.js development server after making changes to `.env.local`.
*   **Migration Errors:**
    *   If `supabase db reset` or `supabase migration up` fails, check the console output for specific SQL errors. You might need to inspect the problematic migration file.

This guide should help you get the Developer CRM up and running locally. If you encounter further issues, please refer to the other documentation or seek assistance from the project maintainers.