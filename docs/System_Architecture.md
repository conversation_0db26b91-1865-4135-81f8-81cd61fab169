# Developer CRM - System Architecture

## 1. Introduction

This document describes the system architecture of the Developer CRM application. It is intended for developers (both new and existing contributors) to understand the overall structure, key components, their interactions, and data flows within the system.

The primary architectural goals for Developer CRM include:
*   **Maintainability:** A clear separation of concerns to make the codebase easier to understand, modify, and extend.
*   **Scalability:** Leveraging Supabase's managed services to handle growth in data and user load.
*   **Security:** Implementing robust authentication, authorization, and data protection mechanisms.
*   **Developer Experience:** Utilizing modern tools and frameworks like Next.js and TypeScript to create an efficient development environment.

## 2. High-Level Architecture Overview (System Context)

At the highest level, the Developer CRM system interacts with its users (Administrators/Team Members and Client Users) through a web application and relies on Supabase for its backend services.

```mermaid
graph TD
    AdminUser[Admin/Team User] -- Manages CRM via Web App --> DevCRM[Developer CRM System]
    ClientUser[Client User] -- Accesses Portal via Web App --> DevCRM
    DevCRM -- Uses for Auth, DB, Storage --> Supabase[Supabase Backend]
```

*   **Admin/Team User:** Interacts with the main CRM functionalities (company, project, invoice management, etc.) through the web application.
*   **Client User:** Interacts with a dedicated client portal to view project status, shared documents, and invoices.
*   **Developer CRM System:** The core application built with Next.js and Supabase.
*   **Supabase Backend:** Provides authentication, database (PostgreSQL), and potentially file storage services.

## 3. Key Components & Technologies

The Developer CRM system is composed of several key components:

```mermaid
graph TD
    subgraph UserInterfaces["User Interfaces"]
        WebApp[Next.js Frontend Application]
    end
    subgraph BackendSystems["Backend Systems & Data"]
        NextApiRoutes[Next.js API Routes]
        SupabaseDB[Supabase PostgreSQL Database]
        SupabaseAuth[Supabase Authentication]
        SupabaseStorage[Supabase Storage (if used)]
    end
    WebApp -- HTTP API Calls --> NextApiRoutes
    WebApp -- Direct SDK Calls for Auth/Data --> SupabaseAuth
    WebApp -- Direct SDK Calls for Data (RLS Enforced, via React Query) --> SupabaseDB
    WebApp -- Accesses Files (if applicable) --> SupabaseStorage
    NextApiRoutes -- Service Role Access --> SupabaseDB
    NextApiRoutes -- Interacts With --> SupabaseAuth
    NextApiRoutes -- Interacts With (if applicable) --> SupabaseStorage
```

### 3.1. Frontend Application (Next.js Web Application)

*   **Technology:** [Next.js](https://nextjs.org/) (using the App Router), [React](https://react.dev/), [TypeScript](https://www.typescriptlang.org/), [Tailwind CSS](https://tailwindcss.com/).
*   **Responsibilities:**
    *   Rendering the user interface for both the main CRM and the client portal.
    *   Handling user interactions and client-side logic.
    *   Making API calls to Next.js API Routes for custom backend operations.
    *   Interacting directly with Supabase client libraries for authentication and data fetching (respecting Row Level Security).
    *   Managing client-side routing.
*   **State Management:**
    *   **React Query:** Primarily used for managing server state, including fetching, caching, and updating data from Supabase.
    *   **React Context:** Used for managing global UI state or shared state that doesn't fit well into server state management (e.g., theme, user session context).
*   **Deployment:** Typically deployed on platforms like Vercel, Netlify, or other Node.js compatible hosting services.

### 3.2. Backend Services (Supabase)

Supabase provides the core backend infrastructure for the application.

*   **Supabase Authentication:**
    *   Handles user sign-up, sign-in, session management, and password recovery for Admin/Team Members of the CRM.
    *   Utilizes JWTs for secure session handling.
    *   User identities are stored in the `auth.users` table.
*   **Supabase Database (PostgreSQL):**
    *   The primary data store for all application entities, including companies, contacts, projects, tasks, invoices, client users, client sessions, user roles, etc.
    *   The schema is defined and managed via migrations located in the `supabase/migrations` directory.
    *   Employs Row Level Security (RLS) policies to enforce data access control.
    *   Utilizes PostgreSQL functions for complex or security-sensitive database operations (e.g., `create_client_user`, `verify_client_credentials`).
*   **Supabase Storage (if used):**
    *   Can be used for storing user-uploaded files such as company logos, project attachments, or generated invoice PDFs.
    *   Access control can be managed via Supabase Storage policies.
    *(Note: Current usage of Supabase Storage needs to be confirmed from implementation details.)*

### 3.3. Next.js API Routes (`app/api/`)

*   **Responsibilities:**
    *   Provide server-side endpoints for operations that require privileged access or complex business logic not suitable for direct client-to-database interaction.
    *   Handle custom authentication flows, particularly for the client portal (`/api/client-auth/login`, `/api/client-auth/logout`).
    *   Interact with the Supabase Database using the service role key for administrative tasks or when RLS for the currently authenticated user is insufficient (used with caution).
    *   Perform data validation and sanitization before database operations.
    *   May integrate with other third-party services if needed in the future.

## 4. Authentication & Authorization Architecture

The application employs a dual authentication system:

### 4.1. Admin/Team Member Authentication

*   **Mechanism:** Supabase Auth.
*   **Flow:**
    1.  User navigates to a sign-in page within the Next.js application.
    2.  Credentials (email/password) are submitted to Supabase Auth via the Supabase client library.
    3.  Supabase Auth verifies credentials and, if successful, issues a JSON Web Token (JWT).
    4.  The Supabase client library manages the session and stores the JWT securely (typically in `localStorage` or a cookie).
    5.  Subsequent requests to Supabase from the client include this JWT for authentication.
*   **Authorization:**
    *   Application-specific roles (e.g., 'admin', 'user') are stored in the `public.user_roles` table, linked to the `auth.users.id`.
    *   Next.js API Routes and Supabase Row Level Security (RLS) policies use the authenticated user's ID (`auth.uid()`) and their role from `public.user_roles` to control access to data and features.

### 4.2. Client User Authentication (Client Portal)

*   **Mechanism:** Custom system built using Next.js API Routes and Supabase database tables (`public.client_users`, `public.client_sessions`).
*   **Flow:**
    1.  Client navigates to the `/client-login` page.
    2.  Credentials (email/password) are submitted to the `/api/client-auth/login` Next.js API route.
    3.  The API route calls the `verify_client_credentials` PostgreSQL function (which checks against `public.client_users.password_hash`).
    4.  If credentials are valid, a new session token is generated and stored in the `public.client_sessions` table, linked to the `client_users.id` and `client_users.company_id`.
    5.  A secure, HTTPOnly cookie containing the session token is set in the client's browser.
    6.  The client is redirected to their dedicated portal view.
*   **Authorization (Client Portal):**
    *   Subsequent requests from the client portal to fetch data include the session cookie.
    *   Next.js API routes serving client portal data validate the session token against the `public.client_sessions` table (checking for existence and expiration).
    *   Supabase RLS policies for client-accessible data (e.g., projects, invoices for their company) are typically based on the `company_id` associated with the validated client session.

For more detailed information, refer to [`docs/Authentication.md`](docs/Authentication.md).

## 5. Data Management & Storage

*   **Primary Database:** Supabase PostgreSQL (see [`docs/Database_Schema.md`](docs/Database_Schema.md) and [`docs/Database_Diagram.md`](docs/Database_Diagram.md) for detailed schema).
*   **Key Data Entities:** Companies, Contacts, Projects, Tasks, Credentials, Invoices, Invoice Items, Services, Activities, Expenses, User Roles, Client Users, Client Sessions, Client Companies.
*   **Data Security:**
    *   **Row Level Security (RLS):** Extensively used to ensure users can only access data they are permitted to see based on their role and associations (e.g., company membership).
    *   **Encryption:** Sensitive data, such as passwords in the `credentials` table (for client systems, not user passwords which are handled by Supabase Auth or hashed for `client_users`), is encrypted.
*   **Database Migrations:** Managed using Supabase's built-in migration tooling. SQL migration files are located in the `supabase/migrations` directory.
*   **File Storage (if applicable):** Supabase Storage can be utilized for storing files like company logos, project-related documents, or generated invoice PDFs. Access control policies for Storage buckets would be configured as needed.

## 6. Frontend Architecture

*   **Structure:** The frontend is built using Next.js with the App Router paradigm, promoting a component-based architecture within the `app/` directory.
*   **Key Patterns:**
    *   **Server Components:** Used for rendering static content or fetching data on the server, reducing client-side JavaScript.
    *   **Client Components:** Used for interactive UI elements and components that require browser APIs or manage client-side state.
    *   **API Route Handlers:** Located in `app/api/`, these handle backend logic for client requests.
*   **State Management:**
    *   **React Query:** Used for managing server state, including data fetching, caching, optimistic updates, and synchronization with the backend.
    *   **React Context:** Employed for managing global UI-related state (e.g., theme preferences, user authentication status for UI display) that is shared across multiple components.
*   **Styling:** [Tailwind CSS](https://tailwindcss.com/) is used for utility-first CSS styling, enabling rapid UI development.

## 7. API Design

*   The application exposes backend functionality primarily through **RESTful APIs** implemented as Next.js API Route Handlers within the `app/api/` directory.
*   Client-side interactions with Supabase (e.g., fetching data for display) often use the Supabase JavaScript client library directly. These interactions are secured by RLS policies enforced by Supabase.
*   For more complex or security-critical database operations (especially those requiring elevated privileges beyond what a standard user session might have, or multi-step transactional logic), **PostgreSQL functions (RPCs)** are defined in the database and called from Next.js API Routes using the service role client. Examples include `create_client_user` and `verify_client_credentials`.
*   Additionally, certain API routes may be designed for public access, potentially utilizing Supabase's anonymous key to fetch non-sensitive, publicly available data. In such cases, RLS policies must be carefully configured to ensure only intended data is exposed via the anonymous role. The `/api/direct-contacts` endpoint is an example of this pattern.

## 8. Deployment Architecture (High-Level)

*   **Frontend Application (Next.js):**
    *   Typically deployed to a platform specializing in Node.js and Jamstack applications, such as [Vercel](https://vercel.com/) (recommended for Next.js projects), [Netlify](https://www.netlify.com/), or a containerized environment (e.g., Docker) on cloud providers.
*   **Backend Services (Supabase):**
    *   Supabase is a managed backend-as-a-service platform. The database, authentication, and storage services are hosted and managed by Supabase.
*   **Environment Variables:**
    *   Configuration (API keys, database URLs, etc.) is managed through environment variables. These must be set securely in the respective deployment environments (e.g., Vercel project settings, server environment).

## 9. Key Data Flows (Illustrative Examples)

*(These could be further detailed with Mermaid sequence diagrams if desired)*

*   **Admin User Login and Data Fetch:**
    1.  Admin enters credentials on the login page.
    2.  Next.js app calls Supabase Auth (client-side).
    3.  Supabase Auth validates, returns JWT. Session established.
    4.  Admin navigates to a data page (e.g., Companies List).
    5.  React Query hook triggers data fetch using Supabase JS client.
    6.  Request goes to Supabase DB, RLS policies (checking `auth.uid()` against `user_roles`) are applied.
    7.  Data returned to frontend and displayed.

*   **Client User Login and Portal Data Fetch:**
    1.  Client enters credentials on `/client-login`.
    2.  Next.js app POSTs to `/api/client-auth/login`.
    3.  API route calls `verify_client_credentials` DB function.
    4.  If valid, API route creates a record in `client_sessions` and sets HTTPOnly cookie.
    5.  Client is redirected to their portal.
    6.  Client portal page makes API calls (e.g., to `/api/client-portal/data`) with the session cookie.
    7.  API route validates session, fetches data from Supabase DB (RLS policies based on `client_sessions.company_id` apply).
    8.  Data returned to client portal.

*   **Creating a new Project by an Admin:**
    1.  Admin fills out the new project form in the Next.js app.
    2.  On submit, the app (likely via a React Query mutation calling a Next.js API route or directly if RLS allows) sends project data.
    3.  The Next.js API route (or direct Supabase call) inserts the new project into the `projects` table in Supabase DB.
    4.  RLS policies ensure the admin has permission to create a project for the specified company.

## 10. Scalability & Performance Considerations

*   **Supabase:** Designed to scale. As a managed service, database and authentication performance are largely handled by Supabase infrastructure. Proper indexing and efficient query design are still important.
*   **Next.js Frontend:**
    *   Leverages server-side rendering (SSR), static site generation (SSG), and Incremental Static Regeneration (ISR) for performance.
    *   Code splitting and optimized image handling are built-in features.
    *   React Query helps optimize data fetching and caching.

## 11. Security Considerations (Summary)

Security is a critical aspect of the Developer CRM:
*   **Authentication:** Robust authentication via Supabase Auth for admins/team and a custom secure system for clients.
*   **Authorization:** Granular access control through Supabase Row Level Security (RLS) and application-level role checks.
*   **Data Encryption:** Encryption at rest for sensitive data within Supabase and encryption in transit (HTTPS). Specific fields like stored external system passwords in the `credentials` table are also encrypted within the database.
*   **Input Validation:** All user inputs and API request payloads should be validated to prevent common vulnerabilities (e.g., XSS, SQL injection - though Supabase helps mitigate direct SQLi).
*   **Secure Headers & Cookies:** Use of HTTPOnly, Secure cookies for sessions.
*   Regular review of dependencies for vulnerabilities.

Refer to [`docs/Authentication.md`](docs/Authentication.md) and [`docs/Database_Schema.md`](docs/Database_Schema.md) for more specific details on security implementations.