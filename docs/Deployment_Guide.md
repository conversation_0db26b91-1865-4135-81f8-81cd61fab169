# Developer CRM - Deployment Guide

## 1. Introduction

This guide provides comprehensive instructions for deploying the Developer CRM application to a production or staging environment. It covers the necessary steps for configuring the Supabase backend and deploying the Next.js frontend application.

This guide assumes you have a production-ready version of the codebase and have already created a Supabase project that will serve as your backend.

## 2. Prerequisites for Deployment

Before starting the deployment process, ensure you have the following:

*   **Production-Ready Supabase Project:** A Supabase project dedicated to your production (or staging) environment. This should not be your local development instance.
*   **Hosting Platform Account:** An account with a hosting provider suitable for Next.js applications. Common choices include:
    *   [Vercel](https://vercel.com) (highly recommended for Next.js)
    *   [Netlify](https://www.netlify.com/)
    *   AWS (e.g., Amplify, S3/CloudFront, ECS)
    *   Google Cloud Platform (e.g., Cloud Run)
    *   Other Docker-compatible environments.
*   **Domain Name (Optional but Recommended):** A custom domain name for your production application.
*   **Production Environment Variables:** You will need to gather all necessary environment variables for your production Supabase project and any other services.

## 3. Supabase Backend Configuration for Production

Your Supabase project needs to be correctly configured for a production environment.

### 3.1. Project Setup
*   Ensure your Supabase project is created in the desired region for optimal performance and data residency.

### 3.2. Authentication Settings
*   **Disable Public Sign-ups (if applicable):** In your Supabase project dashboard (Authentication > Providers), if user registration is meant to be exclusively handled by admins within the CRM, ensure that "Enable new users" is turned OFF for the Email provider.
*   **OAuth Providers:** If you use OAuth providers (Google, GitHub, etc.), ensure you have configured them with production Redirect URIs that point to your deployed application's domain. Development URIs (like `http://localhost:3000`) will not work for production.
*   **Custom SMTP Server:** For reliable delivery and branding of authentication emails (password recovery, email confirmations, magic links), configure a custom SMTP server in Supabase (Authentication > SMTP Settings). Supabase's default email service has rate limits.
*   **Email Templates:** Review and customize the email templates used by Supabase Auth (Authentication > Email Templates) to match your application's branding.

### 3.3. Database
*   **Apply Migrations:** Ensure that all database migrations located in your project's `supabase/migrations` directory have been successfully applied to your production Supabase database.
    *   If using the Supabase CLI and your project is linked: `supabase migration up`
    *   Otherwise, you may need to apply them manually via the Supabase SQL Editor, ensuring they are run in the correct order.
*   **Database Backups:** Supabase automatically handles backups for projects on paid plans (and provides Point-In-Time-Recovery). For free-tier projects, backups are less frequent. Understand the backup policy for your project's plan. Consider manual backups if needed.
*   **Performance & Instance Size:** Monitor your database performance. If you anticipate high load, consider upgrading your Supabase project to an appropriate compute instance size.
*   **Row Level Security (RLS):** Double-check that RLS policies are ENABLED on all tables containing sensitive or user-specific data. Thoroughly test these policies to ensure they correctly restrict access as intended in a production scenario.

### 3.4. Storage (if used)
*   If your application uses Supabase Storage for file uploads (e.g., company logos, project attachments):
    *   Ensure your Storage buckets are created.
    *   Configure appropriate access policies for these buckets (e.g., public read for logos, restricted access for private documents).

### 3.5. API Keys & URLs
*   You will use the production **Project URL** and **public anon key** from your Supabase project settings (Project Settings > API) for `NEXT_PUBLIC_SUPABASE_URL` and `NEXT_PUBLIC_SUPABASE_ANON_KEY` respectively in your frontend deployment environment.
*   The **service_role key** (`SUPABASE_SERVICE_ROLE_KEY`) is highly sensitive and must be stored securely as a server-side environment variable in your Next.js hosting environment.

### 3.6. Custom Domains
*   If you are using a custom domain for your application, you might also consider configuring custom domains for your Supabase services (API, Auth) for consistency, though this is optional.

## 4. Frontend Application Deployment (Next.js)

### 4.1. Building for Production
Before deploying, create an optimized production build of your Next.js application:
```bash
npm run build
```
This command compiles your TypeScript, bundles your JavaScript, optimizes assets, and prepares the application for production.

### 4.2. Environment Variables for Production Build
*   **Public Variables (`NEXT_PUBLIC_`):** These must be available during the build process on your deployment platform. They will be bundled into the client-side code.
    *   `NEXT_PUBLIC_SUPABASE_URL`
    *   `NEXT_PUBLIC_SUPABASE_ANON_KEY`
    *   `NEXT_PUBLIC_APP_URL` (should be your production domain, e.g., `https://yourcrm.com`)
*   **Server-Side Variables:** These are used by Next.js API Routes or server-side rendering logic and must be set securely in the hosting environment. They are NOT bundled into client-side code.
    *   `SUPABASE_SERVICE_ROLE_KEY`
    *   Any other backend-specific API keys or secrets.

### 4.3. Deployment Platforms

#### Vercel (Recommended for Next.js)
1.  Sign up or log in to [Vercel](https://vercel.com).
2.  Connect your Git repository (e.g., GitHub, GitLab, Bitbucket) to Vercel.
3.  **Configure Project Settings:**
    *   **Build Command:** Usually `npm run build` or `yarn build`. Vercel often auto-detects this for Next.js.
    *   **Output Directory:** Vercel typically auto-detects this for Next.js (`.next`).
    *   **Install Command:** `npm install` or `yarn install`.
    *   **Environment Variables:** Add all necessary `NEXT_PUBLIC_` and server-side variables (like `SUPABASE_SERVICE_ROLE_KEY`) in your Vercel project settings (Settings > Environment Variables). Mark sensitive variables as "Secret".
4.  Deploy your project. Vercel will automatically build and deploy upon pushes to your connected branch (e.g., `main` or `develop`).
5.  **Custom Domain:** Configure your custom domain in Vercel project settings (Settings > Domains).

#### Netlify
1.  Sign up or log in to [Netlify](https://www.netlify.com/).
2.  Connect your Git repository.
3.  **Configure Build Settings:**
    *   **Build command:** `npm run build`.
    *   **Publish directory:** `.next`.
4.  **Environment Variables:** Add environment variables in Netlify site settings (Site settings > Build & deploy > Environment).
5.  Deploy.
6.  **Custom Domain:** Configure your custom domain in Netlify site settings.

#### Docker / Containerization (Advanced)
1.  **Create a `Dockerfile`:** Define the steps to build and run your Next.js application in a Docker container. This typically involves:
    *   Using a Node.js base image.
    *   Copying `package.json` and `lock` file, then running `npm install`.
    *   Copying the rest of the application code.
    *   Running `npm run build`.
    *   Setting the `CMD` to run `npm run start`.
    *   Exposing the application port (e.g., 3000).
2.  **Build the Docker Image:**
    ```bash
    docker build -t your-crm-app .
    ```
3.  **Push to a Container Registry:** (e.g., Docker Hub, AWS ECR, Google Container Registry).
4.  **Deploy to a Container Hosting Service:** (e.g., AWS ECS, Google Cloud Run, Kubernetes, DigitalOcean App Platform).
    *   Ensure environment variables are securely passed to the running containers.

## 5. Post-Deployment Checklist & Best Practices

After deploying your application:

*   **Verify Environment Variables:** Ensure all production environment variables are correctly set and accessible by the application in the hosting environment.
*   **Test Core Functionalities:**
    *   Admin login and critical admin operations (e.g., user creation, viewing key data).
    *   Client portal login and data visibility.
    *   Key CRUD operations (e.g., creating a company, project, task, invoice).
*   **Check Authentication Flows:** Test password resets, email confirmations (if enabled), and OAuth flows (if applicable) using production settings.
*   **HTTPS:** Confirm that SSL/TLS is enabled and your site is served over HTTPS. Most modern hosting platforms handle this automatically or provide easy setup.
*   **Security Headers:** Consider implementing or verifying security headers like Content Security Policy (CSP), HTTP Strict Transport Security (HSTS), X-Frame-Options, etc., to enhance security.
*   **Monitoring & Logging:**
    *   Utilize logging services provided by your hosting platform (e.g., Vercel Logs, Netlify Functions logs).
    *   Monitor your Supabase project's logs and usage statistics via the Supabase dashboard.
    *   Consider integrating an Application Performance Monitoring (APM) tool for more in-depth insights if needed.
*   **Database Backups:** Re-confirm your Supabase project's backup strategy and retention policy.
*   **Regular Updates:** Establish a process for regularly updating dependencies (Node.js, Next.js, Supabase client libraries, other packages) to patch security vulnerabilities and get new features.

## 6. Troubleshooting Common Deployment Issues

*   **Environment Variable Issues:** This is a very common source of problems. Double-check names, values, and whether they are accessible at build time vs. runtime as needed.
*   **Build Failures:** Examine the build logs provided by your hosting platform for specific error messages.
*   **Supabase Connection Errors:** Verify network connectivity from your hosting environment to Supabase, and ensure API keys/URL are correct. Check Supabase project status.
*   **RLS Policy Issues:** If data isn't appearing as expected, review and test your RLS policies in the Supabase SQL Editor against production scenarios.
*   **Static Asset Loading Problems / CORS Issues:** Ensure your `NEXT_PUBLIC_APP_URL` is correctly set. Check CORS settings in Supabase if you're making cross-origin requests in an unusual way (though typically handled by Supabase client libraries).
*   **Incorrect Redirect URIs:** For OAuth providers or Supabase Auth, ensure all redirect URIs are correctly configured for your production domain.