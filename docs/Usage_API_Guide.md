# Developer CRM - Usage and API Guide

## 1. Introduction

This guide provides developers with comprehensive instructions on how to use the Developer CRM's features programmatically and understand its Application Programming Interface (API). It covers methods for interacting with the backend, including Next.js API Routes and direct Supabase client interactions.

This document is intended for developers who are building integrations, extending functionalities, or need to understand the programmatic access points of the CRM.

**Interaction Methods:**
*   **Next.js API Routes:** Custom backend logic exposed via HTTP endpoints.
*   **Supabase Client Library:** Direct interaction with the Supabase backend (database, auth) from client-side or server-side code, respecting Row Level Security.
*   **PostgreSQL Functions (RPC):** Database functions callable via the Supabase client library for complex or security-sensitive operations.

## 2. Authentication & Authorization (API Context)

Secure access to the API is paramount. Different authentication methods apply depending on the context.

### 2.1. Admin/Team Member API Interaction
*   **Authentication:** Requests to protected Next.js API routes intended for admin/team members must include a valid Supabase JWT in the `Authorization` header (`Bearer <token>`). This token is obtained after successful login via Supabase Auth. Alternatively, if using the browser and the user is logged in, the Supabase client within the API route can leverage the user's session cookie.
*   **Authorization:** Backend logic within API routes and Supabase Row Level Security (RLS) policies enforce permissions based on the authenticated user's ID (`auth.uid()`) and their role (e.g., 'admin', 'user') stored in the `public.user_roles` table.

### 2.2. Client Portal API Interaction
*   **Authentication:** The client portal frontend makes authenticated requests using a secure, HTTPOnly session cookie. This cookie is set after a successful login via the `/api/client-auth/login` endpoint.
*   **Authorization:** API endpoints serving client portal data (e.g., `/api/client-portal/data`) validate this session cookie. Supabase RLS policies further restrict data access based on the `company_id` associated with the client's session.

For detailed mechanisms of both authentication systems, please refer to [`docs/Authentication.md`](docs/Authentication.md).

## 3. Core CRM API Endpoints (Next.js API Routes)

The following sections detail the primary Next.js API routes. For each endpoint, the method, path, description, required permissions, and expected request/response formats are outlined.

*(Note: Request/Response schemas and examples are illustrative and may need further detail based on specific route implementations.)*

### 3.1. Admin Management Endpoints

These endpoints are typically restricted to users with 'admin' roles.

*   **Endpoint:** `POST /api/admin/create-user`
    *   **Description:** Creates a new CRM admin/team user account in Supabase Auth and assigns a role in `public.user_roles`.
    *   **Permissions:** Admin.
    *   **Request Body:** `{ "email": "<EMAIL>", "password": "securepassword", "role": "user" }`
    *   **Response (Success):** `{ "success": true, "userId": "uuid" }`
    *   **Example:** `curl -X POST -H "Authorization: Bearer <admin_jwt>" -H "Content-Type: application/json" -d '{"email":"...", "password":"...", "role":"..."}' /api/admin/create-user`

*   **Endpoint:** `POST /api/admin/reset-password`
    *   **Description:** Initiates a password reset for a CRM admin/team user.
    *   **Permissions:** Admin.
    *   **Request Body:** `{ "email": "<EMAIL>" }`
    *   **Response (Success):** `{ "success": true, "message": "Password reset initiated" }`

*   **Endpoint:** `GET /api/admin/companies`
    *   **Description:** (If different from general company listing) Retrieves company data with potentially more administrative details or less restrictive RLS.
    *   **Permissions:** Admin.
    *   **Response (Success):** `[{ /* company data */ }]`

*   **Endpoint:** `GET /api/admin/contacts`
    *   **Description:** (If different from general contact listing) Retrieves contact data with potentially more administrative details.
    *   **Permissions:** Admin.
    *   **Response (Success):** `[{ /* contact data */ }]`

### 3.2. Auth Setup & Management Endpoints

*   **Endpoint:** `POST /api/auth/setup`
    *   **Description:** Handles the first-time setup of the initial admin user. Only accessible if no users exist.
    *   **Permissions:** None (special case for initial setup).
    *   **Request Body:** `{ "email": "<EMAIL>", "password": "securepassword" }`
    *   **Response (Success):** `{ "success": true, "userId": "uuid", "message": "Initial admin user created" }`

*   **Endpoint:** `GET /api/auth/check-setup`
    *   **Description:** Checks if the initial admin setup has been completed.
    *   **Permissions:** None.
    *   **Response (Success):** `{ "isSetupComplete": true/false }`

*   **Endpoint:** `GET /api/auth/list-users`
    *   **Description:** Lists all CRM admin/team users from `auth.users`.
    *   **Permissions:** Admin.
    *   **Response (Success):** `[{ /* user data from Supabase Auth */ }]`

*   **Endpoint:** `GET /api/auth/list-user-roles`
    *   **Description:** Lists users and their assigned roles from `public.user_roles`.
    *   **Permissions:** Admin.
    *   **Response (Success):** `[{ "user_id": "uuid", "role": "admin/user", /* other user details */ }]`

*   **Endpoint:** `POST /api/auth/sync-user`
    *   **Description:** (Purpose needs clarification from codebase) Potentially syncs Supabase Auth user data with local application tables or performs other user-related synchronization tasks.
    *   **Permissions:** Authenticated User / Admin (verify).
    *   **Request/Response:** (To be detailed).

### 3.3. Client Authentication & Portal Endpoints

*   **Endpoint:** `POST /api/client-auth/login`
    *   **Description:** Authenticates a client user for portal access.
    *   **Permissions:** Public.
    *   **Request Body:** `{ "email": "<EMAIL>", "password": "clientpassword" }`
    *   **Response (Success):** Sets HTTPOnly session cookie, returns `{ "success": true, "message": "Login successful" }`.
    *   **Response (Error):** `{ "error": "Invalid credentials" }` (Status 401).

*   **Endpoint:** `POST /api/client-auth/logout`
    *   **Description:** Logs out a client user and clears their session cookie.
    *   **Permissions:** Client Portal Session.
    *   **Response (Success):** `{ "success": true, "message": "Logout successful" }`.

*   **Endpoint:** `GET, POST, PATCH, PUT /api/client-auth/manage`
    *   **Description:** Manages client user accounts (`public.client_users`).
        *   `GET`: List client users for a company (Admin). Query Param: `companyId`.
        *   `POST`: Create a new client user (Admin). Body: `{ companyId, email, username (if used), password }`. Returns new user details including clearTextPassword temporarily.
        *   `PATCH`: Activate/deactivate a client user (Admin). Body: `{ id, isActive: true/false }`.
        *   `PUT`: Reset a client user's password (Admin). Body: `{ id }`. Returns new clearTextPassword temporarily.
    *   **Permissions:** Admin.
    *   **Request/Response:** Varies by method (see implementation in `app/api/client-auth/manage/route.ts`).

*   **Endpoint:** `GET /api/client-portal/data`
    *   **Description:** Retrieves data specific to the authenticated client for their portal view.
    *   **Permissions:** Client Portal Session.
    *   **Response (Success):** `{ /* client-specific data, e.g., projects, invoices */ }`.

*   **Endpoint:** `GET /api/client-portal/[id]`
    *   **Description:** (Purpose needs clarification) Potentially retrieves specific configuration or data for a client portal identified by `[id]`.
    *   **Permissions:** Client Portal Session / Admin (verify).
    *   **Request/Response:** (To be detailed).

### 3.4. Company Onboarding Endpoint

*   **Endpoint:** `POST /api/companies/onboard`
    *   **Description:** Streamlines the creation of a new company, with options to simultaneously create an initial project and a primary contact for that company.
    *   **Permissions:** Authenticated Admin/Team User (via Supabase JWT in cookies, RLS applies).
    *   **Request Body:**
        ```json
        {
          "company_name": "string (required)",
          "industry": "string (optional)",
          "company_status": "string (optional, defaults to 'active')",
          "address": "string (optional)",
          "website": "string (optional)",
          "company_notes": "string (optional)",
          "project_name": "string (optional)",
          "project_description": "string (optional)",
          "project_start_date": "string (optional, YYYY-MM-DD)",
          "project_expected_end_date": "string (optional, YYYY-MM-DD)",
          "project_status": "string (optional, defaults to 'planning')",
          "project_budget": "string (optional, e.g., \"5000.00\")",
          "contact_name": "string (optional)",
          "contact_email": "string (optional)",
          "contact_phone": "string (optional)",
          "contact_position": "string (optional)",
          "contact_notes": "string (optional)"
        }
        ```
    *   **Request Body Fields:**
        *   `company_name` (string, required): Name of the company.
        *   `industry` (string, optional): Industry the company belongs to.
        *   `company_status` (string, optional): Status of the company (e.g., 'active', 'lead'). Defaults to 'active'.
        *   `address` (string, optional): Physical address of the company.
        *   `website` (string, optional): Company's website URL.
        *   `company_notes` (string, optional): General notes about the company.
        *   `project_name` (string, optional): If provided, an initial project will be created for this company.
        *   `project_description` (string, optional): Description of the initial project.
        *   `project_start_date` (string, optional): Start date of the project (e.g., "YYYY-MM-DD").
        *   `project_expected_end_date` (string, optional): Expected end date of the project (e.g., "YYYY-MM-DD").
        *   `project_status` (string, optional): Status of the project. Defaults to 'planning'.
        *   `project_budget` (string, optional): Budget for the project, provided as a string (e.g., "5000.00") and parsed as a float.
        *   `contact_name` (string, optional): If provided, an initial contact will be created for this company.
        *   `contact_email` (string, optional): Email address of the contact.
        *   `contact_phone` (string, optional): Phone number of the contact.
        *   `contact_position` (string, optional): Position/title of the contact.
        *   `contact_notes` (string, optional): Notes about the contact.
    *   **Example Request Payload:**
        ```json
        {
          "company_name": "Innovatech Solutions",
          "industry": "Software Development",
          "company_status": "lead",
          "address": "123 Tech Park, Silicon Valley, CA",
          "website": "https://innovatech.example.com",
          "company_notes": "Promising lead, interested in CRM integration.",
          "project_name": "CRM Integration Phase 1",
          "project_description": "Initial consultation and planning for CRM integration.",
          "project_start_date": "2024-06-01",
          "project_status": "planning",
          "project_budget": "1500.00",
          "contact_name": "Alice Wonderland",
          "contact_email": "<EMAIL>",
          "contact_phone": "555-0101",
          "contact_position": "Project Manager",
          "contact_notes": "Main point of contact for the integration project."
        }
        ```
    *   **Response (Success - 201 Created):**
        ```json
        {
          "message": "Company onboarded successfully",
          "companyId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
        }
        ```
    *   **Response (Error - 500 Internal Server Error):**
        ```json
        {
          "error": "Failed to create company: <specific Supabase error or 'Unknown error'>"
        }
        ```
        or
        ```json
        {
          "error": "An unexpected error occurred during onboarding"
        }
        ```
    *   **Notes:** If `project_name` is provided, a new project linked to the company will be created. If `contact_name` is provided, a new contact linked to the company will be created. Errors during optional project or contact creation are logged on the server but do not typically cause the entire onboarding to fail if the company itself was created successfully.

### 3.5. Direct Contacts Endpoint

*   **Endpoint:** `GET /api/direct-contacts`
    *   **Description:** Retrieves a list of all contacts from the `contacts` table, ordered by name. This endpoint uses an anonymous Supabase client, meaning it fetches all contacts irrespective of the currently logged-in user or RLS policies that depend on `auth.uid()`. This is suitable for scenarios requiring a full, unfiltered list of contacts, such as certain admin views or public-facing informational sections where contact listing is intended to be open.
    *   **Permissions:** Public (uses anonymous Supabase client; RLS policies allowing anonymous access or not depending on `auth.uid()` will apply).
    *   **Request Parameters:** None.
    *   **Example Request:**
        ```
        GET /api/direct-contacts
        ```
    *   **Response (Success - 200 OK):**
        ```json
        {
          "contacts": [
            {
              "id": "uuid",
              "company_id": "uuid",
              "name": "John Doe",
              "email": "<EMAIL>",
              "phone": "************",
              "position": "Developer",
              "notes": "Notes about John",
              "created_at": "timestamp",
              "updated_at": "timestamp"
              // ... other fields from the 'contacts' table
            }
            // ... more contacts
          ],
          "meta": {
            "count": 1, // Total number of contacts returned
            "timestamp": "YYYY-MM-DDTHH:mm:ss.sssZ"
          }
        }
        ```
    *   **Response (Success - 200 OK, No Contacts Found):**
        ```json
        {
          "contacts": []
        }
        ```
    *   **Response (Error - 500 Internal Server Error):**
        ```json
        {
          "error": "Failed to fetch contacts data: <Supabase error message>"
        }
        ```
        or
        ```json
        {
          "error": "An unexpected error occurred",
          "details": "<error_message>"
        }
        ```

### 3.6. Core Entity Endpoints (Example: Contacts)

*   **Endpoint:** `GET, POST /api/contacts`
    *   **Description:**
        *   `GET`: Lists contacts, respecting RLS for the authenticated admin/team user.
        *   `POST`: Creates a new contact.
    *   **Permissions:** Authenticated Admin/Team User.
    *   **Request (POST Body):** `{ "company_id": "uuid", "name": "John Doe", "email": "<EMAIL>", ... }`
    *   **Response (GET Success):** `[{ /* contact data */ }]`
    *   **Response (POST Success):** `{ /* created contact data */ }`
    *(Similar documentation structure would apply to other core entities like `/api/companies`, `/api/projects`, etc., if they have dedicated API routes beyond direct Supabase client usage.)*

### 3.7. Migrations Endpoints (Internal/Admin Only)

*   **Endpoint:** `POST, GET /api/migrations/client-portal-auth`
    *   **Description:**
        *   `POST`: Triggers the setup of database tables and functions required for client portal authentication (`client_users`, `client_sessions`, `verify_client_credentials`).
        *   `GET`: Checks the status of these client portal authentication migrations.
    *   **Permissions:** Admin (verified via JWT).
    *   **Response (POST Success):** `{ "success": true, "message": "Client portal authentication migrations completed successfully" }`
    *   **Response (GET Success):** `{ "clientUsersExists": true, "clientSessionsExists": true, "verifyFunctionExists": true }`

### 3.8. RLS Test Endpoint (Internal/Testing)

*   **Endpoint:** `GET /api/rls-test`
    *   **Description:** Likely used for testing Row Level Security configurations. Not for production use.
    *   **Permissions:** (Verify from route, likely Admin or specific test conditions).
    *   **Request/Response:** (To be detailed).

## 4. Interacting with Supabase Directly

Besides Next.js API Routes, developers can interact with Supabase directly using the Supabase JavaScript client library.

### 4.1. Using Supabase JS Client Library (Frontend)

*   **Initialization:** The Supabase client is typically initialized once and made available throughout the application (e.g., via React Context or a utility module). Refer to `lib/supabase/client.ts`.
    ```typescript
    // Example from lib/supabase/client.ts (conceptual)
    // import { createBrowserClient } from '@supabase/ssr';
    // const supabase = createBrowserClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!);
    ```
*   **Common CRUD Operations:**
    *   **Select:** `const { data, error } = await supabase.from('companies').select('*');`
    *   **Insert:** `const { data, error } = await supabase.from('projects').insert([{ name: 'New Project', company_id: 'uuid' }]);`
    *   **Update:** `const { data, error } = await supabase.from('tasks').update({ status: 'completed' }).eq('id', 'task_uuid');`
    *   **Delete:** `const { data, error } = await supabase.from('contacts').delete().eq('id', 'contact_uuid');`
    All these operations automatically respect RLS policies defined for the authenticated user.
*   **Authentication Methods:**
    *   `supabase.auth.signInWithPassword({ email, password })`
    *   `supabase.auth.signUp({ email, password })` (if public sign-up is enabled for a flow)
    *   `supabase.auth.signOut()`
    *   `supabase.auth.getUser()`
    *   `supabase.auth.onAuthStateChange(...)`

### 4.2. Using Supabase JS Client Library (Next.js API Routes - Service Role)

*   **Initialization:** For server-side operations requiring elevated privileges (e.g., bypassing RLS for administrative tasks), a Supabase client can be initialized with the `SERVICE_ROLE_KEY`. Refer to `lib/supabase/server.ts`.
    ```typescript
    // Example from lib/supabase/server.ts (conceptual)
    // import { createServerClient } from '@supabase/ssr';
    // const supabaseAdmin = createServerClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!, { cookies: {} });
    ```
*   **Use Cases:** Creating users programmatically, complex data migrations, operations that legitimately need to bypass RLS.
*   **Security Warning:** Use the service role client with extreme caution, as it bypasses all RLS policies. Ensure such usage is restricted to secure, admin-only API routes with proper authorization checks.

### 4.3. Calling PostgreSQL Functions (RPC)

Custom database functions can be called using the `rpc()` method.
*   **Example:**
    ```typescript
    const { data, error } = await supabase.rpc('verify_client_credentials', {
      p_email: '<EMAIL>',
      p_password: 'password123'
    });
    if (error) console.error('RPC Error:', error);
    else console.log('RPC Result:', data); // `data` will be the boolean result
    ```
*   **Key Public Database Functions:**
    *   `verify_client_credentials(p_email TEXT, p_password TEXT) RETURNS BOOLEAN`
    *   `create_client_user(p_company_id UUID, p_email TEXT, p_password TEXT) RETURNS UUID`
    *   `reset_client_password(p_user_id UUID, p_new_password TEXT) RETURNS BOOLEAN`
    (Refer to [`docs/Authentication.md`](docs/Authentication.md) and [`docs/Database_Schema.md`](docs/Database_Schema.md) for more details on these functions).

## 5. Common Workflows & Examples

This section illustrates how to perform common operations using a combination of API calls and Supabase client interactions.

*   **Admin: Creating a New Client User for Portal Access:**
    1.  Admin UI calls `POST /api/client-auth/manage` with `companyId`, `email`, `username` (if applicable).
    2.  The API route generates a password, hashes it, and calls `supabase.from('client_users').insert(...)` using the service role client (or calls the `create_client_user` RPC).
    3.  The API route returns the new client user details, including the temporary cleartext password for the admin to share securely.

*   **Client: Logging into Portal and Viewing Data:**
    1.  Client submits credentials to `/client-login` page.
    2.  Frontend calls `POST /api/client-auth/login`.
    3.  API route validates credentials (via `verify_client_credentials` RPC), creates a session in `client_sessions`, and sets an HTTPOnly cookie.
    4.  Client is redirected to their portal dashboard.
    5.  Portal dashboard (React component) uses React Query and Supabase JS client (or calls `/api/client-portal/data` with the cookie) to fetch company-specific projects, invoices, etc. RLS ensures only their company's data is returned.

*(More workflows like "Creating a New Company and its First Contact", "Assigning a Task", "Generating an Invoice" can be added here with code snippets.)*

## 6. Error Handling

*   **HTTP Status Codes:**
    *   `200 OK`: Request successful.
    *   `201 Created`: Resource successfully created.
    *   `204 No Content`: Request successful, no content to return (e.g., after a DELETE).
    *   `400 Bad Request`: Invalid request payload or parameters.
    *   `401 Unauthorized`: Missing or invalid authentication credentials.
    *   `403 Forbidden`: Authenticated user does not have permission to access the resource.
    *   `404 Not Found`: Resource not found.
    *   `500 Internal Server Error`: Unexpected server-side error.
*   **Error Response Format (Typical for Next.js API Routes):**
    ```json
    {
      "error": "A descriptive error message"
    }
    ```
    Supabase client errors will have their own specific structure (e.g., `error.message`, `error.code`).

## 7. Rate Limiting

Currently, no specific custom rate limiting is documented for the Next.js API routes. However, Supabase itself has [built-in rate limiting](https://supabase.com/docs/guides/platform/limits) for its API and Auth services, which should be considered. If custom rate limiting is implemented (e.g., using a library like `express-rate-limit` adapted for Next.js), it should be documented here.

## 8. Further Development / API Evolution

This API is currently under active development. Changes may occur. While efforts will be made to maintain backward compatibility for critical endpoints, this is not guaranteed for all aspects during early development phases. Significant breaking changes will be communicated through project updates or versioned documentation if applicable in the future.