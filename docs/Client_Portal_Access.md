# Client Portal Access Management

## Overview

The Developer CRM provides a client portal feature that allows clients to access their project information, credentials, invoices, and other relevant data. This document outlines how client portal access is managed and secured.

## Authentication Architecture

The application uses a dual authentication system:

1. **Admin Authentication (Supabase Auth)**
   - Used for admin and team member access to the CRM.
   - Provides robust user management for the development team via Supabase's built-in authentication.
   - Application-specific roles (e.g., 'admin', 'user') are managed in the `public.user_roles` table, linking to Supabase's `auth.users` table.
   - Controls access to all CRM features based on these roles.
   - This system is distinct from the client authentication described below.

2. **Client Authentication (Custom)**
   - Separate authentication system for client portal access
   - Each client company has its own dedicated portal
   - Clients cannot access the main CRM features

## Client Portal Access Flow

### Creating Client Portal Access

As an admin, you can create client portal access through the following steps:

1. Navigate to the Client Portals section in the sidebar
2. Find the client company you want to provide access to
3. Click on "Manage Access" for that client
4. Create login credentials for the client (email and password)
5. The system will generate a unique access token for the client
6. Share the login credentials with the client securely

### Client Login Process

Clients access their portal through a dedicated login page:

1. Client navigates to the client portal login page (`/client-login`)
2. Client enters their email and password
3. Upon successful authentication, they are redirected to their company's portal
4. The portal displays only information relevant to their company

## Security Measures

### Access Restrictions

- Clients can only access their own company's information
- Row Level Security (RLS) in Supabase ensures data isolation
- Client portal tokens have configurable expiration dates
- Failed login attempts are rate-limited to prevent brute force attacks

### Data Protection

- Sensitive data like credentials are encrypted in the database
- Passwords are never displayed in plaintext
- Client portal sessions expire after a period of inactivity

## Implementation Details

### Database Tables

The client portal access is managed through the following database tables:

1. **client_portal**
   - Stores portal configuration for each client company
   - Links to the company record
   - Contains access control settings

2. **client_users**
   - Stores client user credentials
   - Links to the client_portal record
   - Manages authentication for client access

### API Endpoints

The following API endpoints handle client portal authentication:

- `POST /api/client-auth/login`: Authenticates client users
- `POST /api/client-auth/logout`: Ends client sessions
- `POST /api/client-auth/reset-password`: Handles password resets
- `GET /api/client-portal/data`: Retrieves client-specific data

## Admin Management

As an admin, you have the following capabilities:

- Create new client portal users
- Reset client passwords
- Revoke client access
- Set access expiration dates
- View client portal access logs

## Best Practices

1. **Credential Sharing**
   - Never send client credentials via unencrypted email
   - Use a secure credential sharing service
   - Set temporary passwords that must be changed on first login

2. **Access Management**
   - Regularly review client access
   - Revoke access for completed projects
   - Implement the principle of least privilege

3. **Security Monitoring**
   - Monitor failed login attempts
   - Track unusual access patterns
   - Implement notifications for suspicious activities

## Future Enhancements

- Two-factor authentication for client portal
- Single sign-on integration
- Customizable client portal branding
- Enhanced access control with role-based permissions within client organizations
