import { NextResponse, type NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'

// Track redirection globally
export async function middleware(request: NextRequest) {
  // Get URL and path for easy reference
  const url = new URL(request.url);
  const path = url.pathname;

  // Check for loop using the redirect counter
  const redirectCount = parseInt(url.searchParams.get('rc') || '0');
  if (redirectCount > 4) { // Increase to 4 to allow a bit more leeway
    console.warn(`Detected ${redirectCount} redirects, breaking potential loop and serving page as is`);
    return NextResponse.next();
  }

  // Public routes that don't require authentication check
  const publicRoutes = [
    "/",
    "/sign-in",
    "/sign-up",
    "/auth/callback",
    "/api/webhook",
    "/client-login",
    "/client-view",
    "/auth-test",
  ];

  // API routes should pass through without auth check
  if (path.startsWith('/api/')) {
    return NextResponse.next();
  }

  // Special handling for setup page - only allow if no admin exists
  if (path === '/setup' || path.startsWith('/setup/')) {
    try {
      // Create a Supabase client using cookies
      const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            get(name) {
              return request.cookies.get(name)?.value;
            },
            set() {}, // No-op for this check
            remove() {}, // No-op for this check
          },
        }
      );

      // Check if any users exist in user_roles
      const { count, error } = await supabase
        .from('user_roles')
        .select('*', { count: 'exact', head: true });

      if (error) {
        console.error('Error checking user count:', error);
        // On error, redirect to sign-in to be safe
        return NextResponse.redirect(new URL('/sign-in', request.url));
      }

      // If users exist in user_roles, redirect to sign-in
      if (count && count > 0) {
        console.log('Setup already completed, redirecting to sign-in');
        return NextResponse.redirect(new URL('/sign-in', request.url));
      }

      // No users exist, allow access to setup
      return NextResponse.next();
    } catch (err) {
      console.error('Error checking setup status:', err);
      // On error, redirect to sign-in to be safe
      return NextResponse.redirect(new URL('/sign-in', request.url));
    }
  }

  // Allow public routes without any authentication check
  const isPublicRoute = publicRoutes.some(route =>
    path === route || path.startsWith(`${route}/`)
  );

  if (isPublicRoute) {
    return NextResponse.next();
  }

  // Only protect specific routes we know need authentication
  const protectedPaths = [
    '/dashboard',
    '/companies',
    '/projects',
    '/contacts',
    '/credentials',
    '/invoices',
    '/settings',
    '/profile',
    '/calendar',
    '/resources',
    '/client-portals',
    '/tasks',
    '/expenses',
  ];

  // Check if this is a protected path
  const needsAuth = protectedPaths.some(route =>
    path === route || path.startsWith(`${route}/`)
  );

  // If not a protected path, let it through
  if (!needsAuth) {
    return NextResponse.next();
  }

  // Now we know it's a protected path
  try {
    // Create a response
    const response = NextResponse.next();

    // Create a Supabase client using cookies
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name) {
            return request.cookies.get(name)?.value;
          },
          set(name, value, options) {
            // This is used for setting cookies in the response
            response.cookies.set({
              name,
              value,
              ...options,
            });
          },
          remove(name, options) {
            response.cookies.set({
              name,
              value: '',
              path: '/',
              ...options,
              expires: new Date(0),
            });
          },
        },
      }
    );

    // Debug: Log all cookies to help diagnose issues
    if (process.env.NODE_ENV === 'development') {
      console.log('All cookies:', Array.from(request.cookies.getAll()).map(c => `${c.name}=${c.value.substring(0, 5)}...`));
    }

    // Check if the user is authenticated
    const { data: { session } } = await supabase.auth.getSession();

    // If no session found, redirect to login
    if (!session) {
      console.log(`No auth session for ${path}, redirecting to login`);

      // Set redirect counter with increment
      const loginUrl = new URL('/sign-in', request.url);
      loginUrl.searchParams.set('rc', (redirectCount + 1).toString());

      // Add the current path as 'next' param to allow redirection after login
      loginUrl.searchParams.set('next', path);

      return NextResponse.redirect(loginUrl);
    }

    // NEW: Verify user exists in user_roles table and has a valid role
    if (session.user) {
      const { data: userRole, error: roleError } = await supabase
        .from('user_roles')
        .select('role') // Select the role or just 'id' to check for existence
        .eq('user_id', session.user.id)
        .maybeSingle(); // Use maybeSingle if you expect at most one role entry per user

      if (roleError) {
        console.error(`Error checking user role for ${session.user.id}:`, roleError.message);
        // Decide how to handle this: redirect to error page or sign-in
        const loginUrl = new URL('/sign-in', request.url);
        loginUrl.searchParams.set('error', 'Error verifying user authorization.');
        await supabase.auth.signOut(); // Sign out the user
        return NextResponse.redirect(loginUrl);
      }

      if (!userRole || !userRole.role) { // Or !userRole if just checking existence
        console.warn(`User ${session.user.id} authenticated with Supabase but has no valid role in user_roles. Path: ${path}. Signing out and redirecting to login.`);
        const loginUrl = new URL('/sign-in', request.url);
        loginUrl.searchParams.set('error', 'Access Denied: User account not fully configured or role not assigned.');
        await supabase.auth.signOut(); // Sign out the user to prevent re-entry
        return NextResponse.redirect(loginUrl);
      }
      // Optionally, you could check userRole.role here if specific roles are needed for specific paths
      console.log(`User ${session.user.id} has role: ${userRole.role}. Allowing access to ${path}.`);
    }
    // END NEW

    // Check if token is expired
    const expiresAt = session.expires_at ? session.expires_at * 1000 : 0; // convert to milliseconds
    const isExpired = expiresAt < Date.now();
    const isAboutToExpire = expiresAt - Date.now() < 10 * 60 * 1000; // 10 minutes

    if (isExpired) {
      console.log('Session is expired, redirecting to login');
      const loginUrl = new URL('/sign-in', request.url);
      loginUrl.searchParams.set('error', 'Session expired. Please log in again.');
      return NextResponse.redirect(loginUrl);
    }

    if (isAboutToExpire) {
      console.log('Session is about to expire, attempting refresh');
      try {
        // Attempt to refresh the session - this should update the cookies automatically
        const { data, error } = await supabase.auth.refreshSession();
        if (error) {
          console.error('Error refreshing session:', error);
        } else if (data.session) {
          const newExpiresAt = data.session.expires_at || 0;
          console.log('Session refreshed successfully. New expiry:',
            new Date(newExpiresAt * 1000).toISOString());
        }
      } catch (refreshError) {
        console.error('Error during session refresh:', refreshError);
      }
    }

    // Has valid session, allow access
    console.log(`Valid session found for ${path}, allowing access`);
    return response;
  } catch (error) {
    console.error('Auth middleware error:', error);
    // On error, still allow the request rather than blocking access
    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    // Protect all routes that should require authentication
    '/dashboard(.*)',
    '/companies(.*)',
    '/projects(.*)',
    '/contacts(.*)',
    '/credentials(.*)',
    '/invoices(.*)',
    '/settings(.*)',
    '/profile(.*)',
    '/calendar(.*)',
    '/resources(.*)',
    '/client-portals(.*)',
    '/tasks(.*)',
    '/expenses(.*)',
    // Auth routes
    '/sign-in(.*)',
    '/sign-up(.*)',
    '/auth-test(.*)',
    // Setup route - needs special handling
    '/setup(.*)',
  ],
};
