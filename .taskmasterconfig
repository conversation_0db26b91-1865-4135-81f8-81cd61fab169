{"models": {"main": {"provider": "google", "modelId": "gemini-2.5-pro-exp-03-25", "maxTokens": 120000, "temperature": 0.2}, "research": {"provider": "openrouter", "modelId": "x-ai/grok-3-mini-beta", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "openrouter", "modelId": "google/gemini-2.5-pro-preview-03-25", "maxTokens": 120000, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseUrl": "http://localhost:11434/api", "azureOpenaiBaseUrl": "https://your-endpoint.openai.azure.com/"}}