# Developer CRM

A comprehensive CRM system designed for independent developers and small development teams to manage clients, projects, credentials, and invoicing.

## Table of Contents

- [Overview](#overview)
- [Core Features](#core-features)
- [Technology Stack](#technology-stack)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Environment Setup](#environment-setup)
  - [Database Setup / Migrations](#database-setup--migrations)
  - [First-Time Application Setup (Admin User)](#first-time-application-setup-admin-user)
  - [Running the Development Server](#running-the-development-server)
- [Project Structure](#project-structure)
- [Key Scripts](#key-scripts)
- [Security Best Practices](#security-best-practices)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [License](#license)

## Overview

Developer CRM serves as a centralized hub for managing client relationships, projects, and finances. It aims to streamline workflows for freelancers and small development agencies by providing tools for:

*   Client and contact organization.
*   Detailed project and task tracking.
*   Secure storage and management of sensitive client credentials.
*   Efficient invoicing and payment tracking.
*   A dedicated portal for clients to view their project status and relevant information.

This system is built for:
*   **Independent Developers:** Freelancers juggling multiple clients and projects.
*   **Small Dev Teams:** Teams of 2-5 developers collaborating on client work.
*   **Development Agency Owners:** Individuals managing client relationships and team workloads.
*   **Project Managers:** Professionals tracking project progress and task completion.

## Core Features

*   **Company Management:** Store and organize client company information, track status, and manage relationships.
*   **Contact Management:** Associate contacts with companies, store details, and log communication.
*   **Project Management:** Create and track client projects, assign tasks, monitor progress, and store requirements.
*   **Task Management:** Define, assign, and track tasks within projects, including deadlines and status.
*   **Secure Credential Storage:** Encrypted storage for client credentials with role-based access control.
*   **Invoice Management:** Generate professional invoices, track payment status, and record payment history.
*   **Client Portal:** A dedicated, secure portal for clients to access their project information, shared credentials, and invoices.
*   **Admin User Management:** Secure setup for the first admin user and subsequent admin-controlled user creation for team members.
*   **Role-Based Access Control (RBAC):** Distinct roles (e.g., 'admin', 'user') for CRM team members, managed via Supabase Auth and a `public.user_roles` table.

## Technology Stack

*   **Frontend:** [Next.js](https://nextjs.org/) (App Router), [React](https://react.dev/), [TypeScript](https://www.typescriptlang.org/), [Tailwind CSS](https://tailwindcss.com/)
*   **Backend & Database:** [Supabase](https://supabase.com/) (PostgreSQL Database, Supabase Auth, Supabase Storage if used for file uploads like logos/receipts)
*   **Data Fetching/State Management:** React Query (or SWR/other, as per current implementation - *verify from `package.json` if necessary*)
*   **Styling:** Tailwind CSS
*   **Font:** [Geist](https://vercel.com/font) (via `next/font`)

## Getting Started

### Prerequisites

*   [Node.js](https://nodejs.org/) (Recommended: v18.x or v20.x)
*   [npm](https://www.npmjs.com/), [yarn](https://yarnpkg.com/), [pnpm](https://pnpm.io/), or [bun](https://bun.sh/)
*   A Supabase project. You can create one for free at [supabase.com](https://supabase.com/).

### Environment Setup

1.  **Clone the repository (if applicable):**
    ```bash
    git clone <repository-url>
    cd <project-directory>
    ```
2.  **Install dependencies:**
    ```bash
    npm install
    # or
    yarn install
    # or
    pnpm install
    # or
    bun install
    ```
3.  **Set up environment variables:**
    Create a `.env.local` file in the root directory by copying `.env.example` (if one exists) or creating it manually. Add the following variables, replacing the placeholder values with your actual Supabase project details:
    ```env
    NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
    NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
    SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key # Keep this secret!
    
    # Optional: Add any other required environment variables
    # NEXT_PUBLIC_APP_URL=http://localhost:3000 # For example
    ```
    You can find your Supabase URL and API keys in your Supabase project settings (Project Settings > API).

### Database Setup / Migrations

This project uses Supabase Migrations to manage database schema changes. Migrations are located in the `supabase/migrations` directory.

*   **For Local Development (using Supabase CLI):**
    If you are developing locally and have the Supabase CLI installed and [linked to your project](https://supabase.com/docs/guides/cli/local-development#linking-your-project):
    1.  Start your local Supabase services: `supabase start`
    2.  Apply migrations: `supabase db reset` (This will reset your local database and apply all migrations).
*   **For a Hosted Supabase Project:**
    If you are connecting directly to a hosted Supabase project, ensure the schema defined in `supabase/migrations` has been applied. This might be done through the Supabase dashboard (SQL Editor) or by linking the project and running `supabase migration up` with the Supabase CLI.

Refer to the [Supabase Migrations documentation](https://supabase.com/docs/guides/database/migrations) for more details.

### First-Time Application Setup (Admin User)

The application includes a special first-time setup process for the initial administrator:

1.  Ensure your Supabase project is running and environment variables are correctly set.
2.  When no users exist in Supabase Auth (`auth.users` table), the `/setup` page of the application will be available.
3.  Navigate to `http://localhost:3000/setup` in your browser.
4.  Create your admin account with an email and password. This account will be created in Supabase Auth.
5.  The system will then assign the 'admin' role to this account in the `public.user_roles` table.
6.  After the first admin user is created and their role is set, the `/setup` page becomes inaccessible.
7.  You'll be automatically signed in using Supabase Auth and redirected to the dashboard.

For more details on the authentication systems, see [`docs/Authentication.md`](docs/Authentication.md).

### Running the Development Server

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```
Open [http://localhost:3000](http://localhost:3000) with your browser to see the result. You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

## Project Structure

A brief overview of key directories:

*   `app/`: Contains the core application code, following the Next.js App Router structure (pages, layouts, components, API routes).
*   `components/`: Shared UI components used across the application.
*   `lib/`: Utility functions, Supabase client configurations, and other shared library code.
*   `public/`: Static assets that are publicly accessible.
*   `supabase/migrations/`: Database schema migrations managed by Supabase.
*   `docs/`: Project documentation files.

## Key Scripts

(Refer to `package.json` for a full list)

*   `npm run dev`: Starts the development server.
*   `npm run build`: Builds the application for production.
*   `npm run start`: Starts a production server (after building).
*   `npm run lint`: Runs ESLint to check for code quality issues.

## Security Best Practices

This application aims to follow security best practices:

1.  **Environment Variables**: Sensitive information like API keys are managed via environment variables and are not committed to version control. Use `.env.local` for local development and configure them securely in your hosting environment for production.
2.  **Data Encryption**: Sensitive data, particularly in the `credentials` table, is encrypted in the database.
3.  **Authentication Systems**:
    *   Admin/Team Member authentication is handled by **Supabase Auth**, leveraging Supabase's built-in user management. User roles (e.g., 'admin', 'user') are managed in the `public.user_roles` table, linking to Supabase's `auth.users` table.
    *   Client portal access uses a custom authentication system, detailed in [`docs/Authentication.md`](docs/Authentication.md).
4.  **Authorization & Row Level Security (RLS)**: Access control, primarily through Supabase Row Level Security, ensures users can only access data they are authorized to view or modify.
5.  **Input Validation**: (Ensure proper input validation is implemented for all user inputs and API requests).
6.  **Secure API Endpoints**: API routes are protected and validate requests.

For more details on authentication and security, refer to [`docs/Authentication.md`](docs/Authentication.md) and [`docs/Database_Schema.md`](docs/Database_Schema.md).

## Deployment

For production deployment:

1.  Ensure all necessary environment variables (Supabase URL, anon key, service role key, etc.) are securely configured in your hosting platform (e.g., Vercel, Netlify, Docker environment).
2.  Ensure your Supabase project (Auth settings, database schema, RLS policies) is properly configured for production.
3.  Build the application: `npm run build`.
4.  Deploy the built application according to your hosting provider's instructions.
    *   For Vercel (from the creators of Next.js), deployment can often be streamlined by connecting your Git repository. See the [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
5.  Double-check all security policies and configurations before going live.

A more detailed deployment guide will be available in `docs/Deployment_Guide.md` (once created).

## Contributing

Contributions are welcome! Please see `docs/CONTRIBUTING.md` (once created) for guidelines on how to contribute to this project, including code style, testing, and the pull request process.

## License

(Specify project license here - e.g., MIT License. If unknown, this needs to be determined.)

---

This project was bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app) and uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font).
