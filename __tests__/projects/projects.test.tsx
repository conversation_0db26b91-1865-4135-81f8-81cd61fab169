import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import { useSupabase } from '../../app/components/SupabaseProvider';
import ProjectsPage from '../../app/projects/page';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn().mockReturnValue(new URLSearchParams()),
}));

// Mock Supabase provider
jest.mock('../../app/components/SupabaseProvider', () => ({
  useSupabase: jest.fn(),
}));

// Mock formatCurrency and formatDate utils
jest.mock('../../lib/utils', () => ({
  formatCurrency: jest.fn((amount) => `$${amount}`),
  formatDate: jest.fn((date) => date ? new Date(date).toLocaleDateString() : ''),
}));

describe('Projects Page', () => {
  const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
  };
  
  const mockSupabase = {
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } },
      }),
    },
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSupabase as jest.Mock).mockReturnValue({ supabase: mockSupabase });
  });
  
  describe('Projects List Page', () => {
    beforeEach(() => {
      // Mock the projects data
      mockSupabase.select.mockReturnThis();
      mockSupabase.order.mockReturnThis();
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        then: jest.fn().mockResolvedValue({
          data: [
            {
              id: '1',
              name: 'Test Project',
              description: 'This is a test project',
              status: 'active',
              start_date: '2023-01-01',
              end_date: '2023-12-31',
              budget: 10000,
              company_id: '1',
              companies: {
                id: '1',
                name: 'Test Company',
              },
              created_at: '2023-01-01T00:00:00Z',
              updated_at: '2023-01-01T00:00:00Z',
            },
          ],
          error: null,
        }),
      });
    });
    
    test('renders loading state initially', () => {
      render(<ProjectsPage />);
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });
    
    test('renders project list when data is loaded', async () => {
      render(<ProjectsPage />);
      
      // Wait for the loading state to disappear
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });
      
      // Check if project data is displayed
      expect(screen.getByText('Test Project')).toBeInTheDocument();
      expect(screen.getByText('Test Company')).toBeInTheDocument();
      expect(screen.getByText('active')).toBeInTheDocument();
    });
    
    test('filters projects by search term', async () => {
      render(<ProjectsPage />);
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });
      
      // Type in search box
      const searchInput = screen.getByPlaceholderText(/search projects/i);
      fireEvent.change(searchInput, { target: { value: 'Test' } });
      
      // Check if filtering works
      expect(screen.getByText('Test Project')).toBeInTheDocument();
      
      // Change search to something that doesn't match
      fireEvent.change(searchInput, { target: { value: 'Nonexistent' } });
      
      // Check if no results message is shown
      expect(screen.getByText(/no projects match your search/i)).toBeInTheDocument();
    });
    
    test('filters projects by status', async () => {
      render(<ProjectsPage />);
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });
      
      // Select status filter
      const statusFilter = screen.getByLabelText(/all statuses/i);
      fireEvent.change(statusFilter, { target: { value: 'active' } });
      
      // Check if filtering works
      expect(screen.getByText('Test Project')).toBeInTheDocument();
      
      // Change filter to something that doesn't match
      fireEvent.change(statusFilter, { target: { value: 'completed' } });
      
      // Check if no results message is shown
      expect(screen.getByText(/no projects match your search/i)).toBeInTheDocument();
    });
  });
});
