import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import { useSupabase } from '../../app/components/SupabaseProvider';
// Adjust the path if your ProjectDetail component is located elsewhere
import ProjectDetail from '../../app/projects/[id]/page'; 
// Mock utility functions if your ProjectDetail page uses them
// jest.mock('../../lib/utils', () => ({
//   formatCurrency: jest.fn((amount) => `$${amount}`),
//   formatDate: jest.fn((date) => date ? new Date(date).toLocaleDateString() : ''),
// }));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn().mockReturnValue(new URLSearchParams()),
}));

// Mock Supabase provider
jest.mock('../../app/components/SupabaseProvider', () => ({
  useSupabase: jest.fn(),
}));

describe('Project Detail Page', () => {
  const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
  };
  
  // Updated mockSupabase structure
  const mockSupabase = {
    auth: {
      getSession: jest.fn().mockResolvedValue({ data: { session: { user: { id: 'test-user-id' } } } }),
    },
    from: jest.fn(), // Will have the main logic
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn(), // Will be configured by 'from' to return a Promise
    order: jest.fn(),  // Will be configured by 'from' to return a Promise
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    // 'then' is not typically part of the core Supabase client like this
  };
  
  const mockProjectData = {
    id: 'test-project-id',
    name: 'Awesome Project Alpha',
    description: 'Detailed description of Awesome Project Alpha.',
    status: 'in-progress',
    start_date: '2024-01-15',
    end_date: '2024-07-15',
    budget: 50000,
    company_id: 'test-company-id',
    companies: { id: 'test-company-id', name: 'Innovatech Solutions' },
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  };

  const mockTasksData = [
    { id: 'task-1', name: 'Design Phase', status: 'completed' },
    { id: 'task-2', name: 'Development', status: 'in_progress' },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSupabase as jest.Mock).mockReturnValue({ supabase: mockSupabase });

    // Configure `from` to set up the promise resolution for `single` or `order`
    mockSupabase.from.mockImplementation((tableName: string) => {
      if (tableName === 'projects') {
        (mockSupabase.single as jest.Mock).mockResolvedValueOnce({ data: mockProjectData, error: null });
      } else if (tableName === 'tasks') {
        (mockSupabase.order as jest.Mock).mockResolvedValueOnce({ data: mockTasksData, error: null });
      }
      return mockSupabase; // Allows chaining of .select(), .eq()
    });
  });

  test('renders loading state initially', () => {
    // Override 'from' for this specific test to simulate pending state
    mockSupabase.from.mockImplementationOnce((tableName: string) => {
      if (tableName === 'projects') {
        (mockSupabase.single as jest.Mock).mockImplementationOnce(() => new Promise(() => {})); // Never resolves
      } else if (tableName === 'tasks') {
        (mockSupabase.order as jest.Mock).mockImplementationOnce(() => new Promise(() => {}));  // Never resolves
      }
      return mockSupabase;
    });

    render(<ProjectDetail params={{ id: 'test-project-id' }} />);
    expect(document.querySelector('.animate-spin.rounded-full.h-12.w-12')).toBeInTheDocument();
  });

  test('renders project details when data is loaded', async () => {
    render(<ProjectDetail params={{ id: 'test-project-id' }} />);
    
    await waitFor(() => {
      expect(screen.getByText(mockProjectData.name)).toBeInTheDocument();
    });
    
    expect(screen.getByText(mockProjectData.description)).toBeInTheDocument();
    expect(screen.getByText(mockProjectData.status, { exact: false })).toBeInTheDocument();
    expect(screen.getByText(mockProjectData.companies.name)).toBeInTheDocument();
    expect(screen.getByText(/Design Phase/i)).toBeInTheDocument(); 
  });

  test('shows error message when project fails to load', async () => {
    // Override 'from' for this specific test to simulate an error
    mockSupabase.from.mockImplementationOnce((tableName: string) => {
      if (tableName === 'projects') {
        (mockSupabase.single as jest.Mock).mockResolvedValueOnce({ data: null, error: { message: 'DB Error' } });
      } else if (tableName === 'tasks') {
        // If project fails, tasks might not be fetched, but good to have a mock
        (mockSupabase.order as jest.Mock).mockResolvedValueOnce({ data: mockTasksData, error: null }); 
      }
      return mockSupabase;
    });
    
    render(<ProjectDetail params={{ id: 'test-project-id' }} />);
    
    await waitFor(() => {
      expect(screen.getByText(/Failed to load project data. Please try again./i)).toBeInTheDocument();
    });
  });

  test('navigates to the edit page when Edit link is clicked', async () => {
    render(<ProjectDetail params={{ id: mockProjectData.id }} />);
    await waitFor(() =>  expect(screen.getByText(mockProjectData.name)).toBeInTheDocument());

    const editLink = screen.getByRole('link', { name: /edit/i });
    expect(editLink).toHaveAttribute('href', `/projects/${mockProjectData.id}/edit`);
    fireEvent.click(editLink);
  });

  describe('Deleting Project', () => {
    let originalConfirm: typeof window.confirm;
    beforeEach(() => {
      originalConfirm = window.confirm;
      window.confirm = jest.fn().mockReturnValue(true);
      
      // Mock for .delete().eq(...)
      // mockSupabase.delete should return an object that has an .eq method.
      // That .eq method should then return a promise.
      const mockEqResolver = jest.fn().mockResolvedValue({ error: null }); // This is what .eq() should resolve to
      (mockSupabase.delete as jest.Mock).mockReturnValue({
        eq: mockEqResolver 
      });

      // Ensure `from` still works for the initial page load before delete button is clicked
      // This needs to be robust so it doesn't get cleared or misconfigured by other test setups
      mockSupabase.from.mockImplementation((tableName: string) => {
        if (tableName === 'projects') {
          (mockSupabase.single as jest.Mock).mockResolvedValue({ data: mockProjectData, error: null });
        } else if (tableName === 'tasks') {
          (mockSupabase.order as jest.Mock).mockResolvedValue({ data: mockTasksData, error: null });
        }
        return mockSupabase;
      });
    });
    afterEach(() => { window.confirm = originalConfirm; });

    test('deletes project when delete button is clicked and confirmed', async () => {
      render(<ProjectDetail params={{ id: mockProjectData.id }} />);
      await waitFor(() => expect(screen.getByText(mockProjectData.name)).toBeInTheDocument());

      fireEvent.click(screen.getByRole('button', { name: /delete/i }));
      expect(window.confirm).toHaveBeenCalled();
      
      // We need to await the promise returned by the .eq() call in the chain.
      // The mockSupabase.delete().eq() call is awaited in the component.
      // So, we wait for the assertions that depend on its completion.
      await waitFor(() => {
        expect(mockSupabase.from).toHaveBeenCalledWith('projects');
        expect(mockSupabase.delete).toHaveBeenCalled(); 
        // Check that the eq method on the object returned by delete was called
        expect((mockSupabase.delete() as any).eq).toHaveBeenCalledWith('id', mockProjectData.id);
      });
      expect(mockRouter.push).toHaveBeenCalledWith('/projects');
    });

    test('does not delete project when delete confirmation is cancelled', async () => {
      (window.confirm as jest.Mock).mockReturnValueOnce(false);
      render(<ProjectDetail params={{ id: mockProjectData.id }} />);
      await waitFor(() => expect(screen.getByText(mockProjectData.name)).toBeInTheDocument());

      fireEvent.click(screen.getByRole('button', { name: /delete/i }));
      expect(window.confirm).toHaveBeenCalled();
      expect(mockSupabase.delete).not.toHaveBeenCalled();
      expect(mockRouter.push).not.toHaveBeenCalled();
    });
  });

  // Add more describe blocks and test cases for Edit, Update, Delete functionalities
  // e.g., describe('Edit Mode', () => { ... });
  // e.g., describe('Deleting Project', () => { ... });

});

// Placeholder for tests related to creating a new project
// These might go into a separate file like 'project-create.test.tsx' 
// or be part of 'projects.test.tsx' if creation happens in a modal on the list page.
describe('Create New Project', () => {
  // test.todo('renders new project form');
  // test.todo('validates form inputs');
  // test.todo('submits new project data to Supabase');
  // test.todo('handles errors during project creation');
}); 