import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import { useSupabase } from '../../app/components/SupabaseProvider';
import CredentialDetail from '../../app/credentials/[id]/page';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn().mockReturnValue(new URLSearchParams()),
}));

// Mock Supabase provider
jest.mock('../../app/components/SupabaseProvider', () => ({
  useSupabase: jest.fn(),
}));

// Mock formatDate util
jest.mock('../../../lib/utils', () => ({
  formatDate: jest.fn((date) => date ? new Date(date).toLocaleDateString() : ''),
}));

describe('Credential Detail Page', () => {
  const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
  };
  
  const mockSupabase = {
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } },
      }),
    },
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    then: jest.fn().mockResolvedValue({
      data: {
        id: 'test-credential-id',
        name: 'Test Credential',
        username: 'testuser',
        password: 'testpassword',
        url: 'https://example.com',
        notes: 'Test notes',
        credential_type: 'Website Login',
        expiry_date: '2023-12-31',
        company_id: 'test-company-id',
        project_id: 'test-project-id',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        companies: {
          id: 'test-company-id',
          name: 'Test Company',
        },
        projects: {
          id: 'test-project-id',
          name: 'Test Project',
        },
      },
      error: null,
    }),
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSupabase as jest.Mock).mockReturnValue({ supabase: mockSupabase });
    
    // Mock clipboard API
    Object.defineProperty(navigator, 'clipboard', {
      value: {
        writeText: jest.fn().mockResolvedValue(undefined),
      },
      configurable: true,
    });
  });
  
  test('renders loading state initially', () => {
    render(<CredentialDetail params={{ id: 'test-credential-id' }} />);
    expect(screen.getByRole('status')).toBeInTheDocument();
  });
  
  test('renders credential details when data is loaded', async () => {
    render(<CredentialDetail params={{ id: 'test-credential-id' }} />);
    
    // Wait for the loading state to disappear
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Check if credential data is displayed
    expect(screen.getByText('Test Credential')).toBeInTheDocument();
    expect(screen.getByText('testuser')).toBeInTheDocument();
    expect(screen.getByText('Website Login')).toBeInTheDocument();
    expect(screen.getByText('Test Company')).toBeInTheDocument();
    expect(screen.getByText('Test Project')).toBeInTheDocument();
    expect(screen.getByText('Test notes')).toBeInTheDocument();
  });
  
  test('toggles password visibility', async () => {
    render(<CredentialDetail params={{ id: 'test-credential-id' }} />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Password should be hidden initially
    const passwordInput = screen.getByDisplayValue('••••••••');
    expect(passwordInput).toHaveAttribute('type', 'password');
    
    // Click the eye icon to show password
    const eyeIcon = screen.getByTitle('Show password');
    fireEvent.click(eyeIcon);
    
    // Password should now be visible
    expect(passwordInput).toHaveAttribute('type', 'text');
    expect(passwordInput).toHaveValue('testpassword');
    
    // Click the eye-off icon to hide password again
    const eyeOffIcon = screen.getByTitle('Hide password');
    fireEvent.click(eyeOffIcon);
    
    // Password should be hidden again
    expect(passwordInput).toHaveAttribute('type', 'password');
  });
  
  test('enables edit mode when edit button is clicked', async () => {
    render(<CredentialDetail params={{ id: 'test-credential-id' }} />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Click the edit button
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);
    
    // Check if form elements are displayed
    expect(screen.getByLabelText('Credential Name *')).toBeInTheDocument();
    expect(screen.getByLabelText('Username *')).toBeInTheDocument();
    expect(screen.getByLabelText('Password *')).toBeInTheDocument();
    expect(screen.getByLabelText('URL')).toBeInTheDocument();
    expect(screen.getByLabelText('Notes')).toBeInTheDocument();
    
    // Check if form values are pre-filled
    expect(screen.getByLabelText('Credential Name *')).toHaveValue('Test Credential');
    expect(screen.getByLabelText('Username *')).toHaveValue('testuser');
    expect(screen.getByLabelText('Password *')).toHaveValue('testpassword');
    expect(screen.getByLabelText('URL')).toHaveValue('https://example.com');
    expect(screen.getByLabelText('Notes')).toHaveValue('Test notes');
  });
  
  test('updates credential when form is submitted', async () => {
    render(<CredentialDetail params={{ id: 'test-credential-id' }} />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Click the edit button
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);
    
    // Update form values
    fireEvent.change(screen.getByLabelText('Credential Name *'), { target: { value: 'Updated Credential' } });
    fireEvent.change(screen.getByLabelText('Username *'), { target: { value: 'updateduser' } });
    
    // Submit the form
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);
    
    // Check if update was called with correct values
    await waitFor(() => {
      expect(mockSupabase.update).toHaveBeenCalledWith(expect.objectContaining({
        name: 'Updated Credential',
        username: 'updateduser',
      }));
    });
  });
  
  test('deletes credential when delete button is clicked and confirmed', async () => {
    // Mock window.confirm
    const originalConfirm = window.confirm;
    window.confirm = jest.fn().mockReturnValue(true);
    
    render(<CredentialDetail params={{ id: 'test-credential-id' }} />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Click the delete button
    const deleteButton = screen.getByText('Delete');
    fireEvent.click(deleteButton);
    
    // Check if delete was called
    await waitFor(() => {
      expect(mockSupabase.delete).toHaveBeenCalled();
    });
    
    // Check if router was called to redirect
    expect(mockRouter.push).toHaveBeenCalledWith('/credentials');
    
    // Restore original confirm
    window.confirm = originalConfirm;
  });
  
  test('copies text to clipboard when copy button is clicked', async () => {
    render(<CredentialDetail params={{ id: 'test-credential-id' }} />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Click the copy username button
    const copyUsernameButton = screen.getByTitle('Copy username');
    fireEvent.click(copyUsernameButton);
    
    // Check if clipboard API was called with correct value
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith('testuser');
    
    // Check if success message is displayed
    await waitFor(() => {
      expect(screen.getByText('Username copied!')).toBeInTheDocument();
    });
  });
});
