import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import { useSupabase } from '../../app/components/SupabaseProvider';
import CompaniesPage from '../../app/companies/page';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn().mockReturnValue(new URLSearchParams()),
}));

// Mock Supabase provider
jest.mock('../../app/components/SupabaseProvider', () => ({
  useSupabase: jest.fn(),
}));

describe('Companies Page', () => {
  const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
  };
  
  const mockSupabase = {
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } },
      }),
    },
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSupabase as jest.Mock).mockReturnValue({ supabase: mockSupabase });
  });
  
  describe('Companies List Page', () => {
    beforeEach(() => {
      // Mock the companies data
      mockSupabase.select.mockReturnThis();
      mockSupabase.order.mockReturnThis();
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        then: jest.fn().mockResolvedValue({
          data: [
            {
              id: '1',
              name: 'Test Company',
              industry: 'Technology',
              website: 'https://example.com',
              phone: '************',
              email: '<EMAIL>',
              address: '123 Main St',
              city: 'Test City',
              state: 'TS',
              zip: '12345',
              country: 'Test Country',
              logo_url: null,
              created_at: '2023-01-01T00:00:00Z',
              updated_at: '2023-01-01T00:00:00Z',
            },
          ],
          error: null,
        }),
      });
    });
    
    test('renders loading state initially', () => {
      render(<CompaniesPage />);
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });
    
    test('renders company list when data is loaded', async () => {
      render(<CompaniesPage />);
      
      // Wait for the loading state to disappear
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });
      
      // Check if company data is displayed
      expect(screen.getByText('Test Company')).toBeInTheDocument();
      expect(screen.getByText('Technology')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });
    
    test('filters companies by search term', async () => {
      render(<CompaniesPage />);
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });
      
      // Type in search box
      const searchInput = screen.getByPlaceholderText(/search companies/i);
      fireEvent.change(searchInput, { target: { value: 'Test' } });
      
      // Check if filtering works
      expect(screen.getByText('Test Company')).toBeInTheDocument();
      
      // Change search to something that doesn't match
      fireEvent.change(searchInput, { target: { value: 'Nonexistent' } });
      
      // Check if no results message is shown
      expect(screen.getByText(/no companies match your search/i)).toBeInTheDocument();
    });
    
    test('navigates to company detail page when clicked', async () => {
      render(<CompaniesPage />);
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });
      
      // Click on company name
      fireEvent.click(screen.getByText('Test Company'));
      
      // Check if router was called with correct path
      expect(mockRouter.push).toHaveBeenCalledWith('/companies/1');
    });
  });
});
