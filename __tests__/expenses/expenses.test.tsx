import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import { useSupabase } from '../../app/components/SupabaseProvider';
import ExpensesPage from '../../app/expenses/page';
import NewExpensePage from '../../app/expenses/new/page';
import ExpenseDetailPage from '../../app/expenses/[id]/page';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn().mockReturnValue(new URLSearchParams()),
}));

// Mock Supabase provider
jest.mock('../../app/components/SupabaseProvider', () => ({
  useSupabase: jest.fn(),
}));

// Mock formatCurrency and formatDate utils
jest.mock('../../lib/utils', () => ({
  formatCurrency: jest.fn((amount) => `$${amount}`),
  formatDate: jest.fn((date) => date ? new Date(date).toLocaleDateString() : ''),
}));

describe('Expenses Page', () => {
  const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
  };
  
  const mockSupabase = {
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } },
      }),
    },
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
    storage: {
      from: jest.fn().mockReturnValue({
        upload: jest.fn().mockResolvedValue({ data: {}, error: null }),
        getPublicUrl: jest.fn().mockReturnValue({ data: { publicUrl: 'https://example.com/receipt.jpg' } }),
      }),
    },
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSupabase as jest.Mock).mockReturnValue({ supabase: mockSupabase });
  });
  
  describe('Expenses List Page', () => {
    beforeEach(() => {
      // Mock the expenses data
      mockSupabase.select.mockReturnThis();
      mockSupabase.order.mockReturnThis();
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        in: jest.fn().mockReturnThis(),
        then: jest.fn().mockResolvedValue({
          data: [
            {
              id: '1',
              name: 'Test Expense',
              amount: 100,
              date: '2023-05-01',
              category: 'Office Supplies',
              payment_method: 'Credit Card',
              is_reimbursable: true,
              reimbursement_status: 'pending',
              company_id: '1',
              project_id: '1',
            },
          ],
          error: null,
        }),
      });
    });
    
    test('renders loading state initially', () => {
      render(<ExpensesPage />);
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });
    
    test('renders expense list when data is loaded', async () => {
      render(<ExpensesPage />);
      
      // Wait for the loading state to disappear
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });
      
      // Check if expense data is displayed
      expect(screen.getByText('Test Expense')).toBeInTheDocument();
      expect(screen.getByText('$100')).toBeInTheDocument();
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
    });
    
    test('filters expenses by search term', async () => {
      render(<ExpensesPage />);
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });
      
      // Type in search box
      const searchInput = screen.getByPlaceholderText(/search expenses/i);
      fireEvent.change(searchInput, { target: { value: 'Test' } });
      
      // Check if filtering works
      expect(screen.getByText('Test Expense')).toBeInTheDocument();
      
      // Change search to something that doesn't match
      fireEvent.change(searchInput, { target: { value: 'Nonexistent' } });
      
      // Check if no results message is shown
      expect(screen.getByText(/no expenses match your search/i)).toBeInTheDocument();
    });
  });
  
  describe('New Expense Page', () => {
    beforeEach(() => {
      // Mock the companies and projects data
      mockSupabase.from.mockImplementation((table) => {
        if (table === 'companies') {
          return {
            select: jest.fn().mockReturnThis(),
            order: jest.fn().mockReturnThis(),
            then: jest.fn().mockResolvedValue({
              data: [{ id: '1', name: 'Test Company' }],
              error: null,
            }),
          };
        } else if (table === 'projects') {
          return {
            select: jest.fn().mockReturnThis(),
            order: jest.fn().mockReturnThis(),
            then: jest.fn().mockResolvedValue({
              data: [{ id: '1', name: 'Test Project', company_id: '1' }],
              error: null,
            }),
          };
        } else if (table === 'expenses') {
          return {
            insert: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            then: jest.fn().mockResolvedValue({
              data: { id: '1' },
              error: null,
            }),
          };
        }
        return mockSupabase;
      });
    });
    
    test('renders the new expense form', async () => {
      render(<NewExpensePage />);
      
      // Check if form elements are rendered
      expect(screen.getByText('New Expense')).toBeInTheDocument();
      expect(screen.getByLabelText(/expense name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/amount/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/date/i)).toBeInTheDocument();
      
      // Wait for companies and projects to load
      await waitFor(() => {
        expect(screen.getByText('Test Company')).toBeInTheDocument();
      });
    });
    
    test('submits the form with valid data', async () => {
      render(<NewExpensePage />);
      
      // Wait for form to load
      await waitFor(() => {
        expect(screen.getByLabelText(/expense name/i)).toBeInTheDocument();
      });
      
      // Fill out the form
      fireEvent.change(screen.getByLabelText(/expense name/i), { target: { value: 'New Test Expense' } });
      fireEvent.change(screen.getByLabelText(/amount/i), { target: { value: '150' } });
      
      // Submit the form
      fireEvent.click(screen.getByText('Save Expense'));
      
      // Check if the expense was created
      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/expenses');
      });
    });
  });
});
