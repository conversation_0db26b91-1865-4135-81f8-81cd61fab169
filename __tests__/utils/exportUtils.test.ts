import { convertToCSV, formatDateForExport } from '../../lib/exportUtils';

describe('Export Utilities', () => {
  describe('convertToCSV', () => {
    test('converts array of objects to CSV string', () => {
      const data = [
        { id: 1, name: '<PERSON>', email: '<EMAIL>' },
        { id: 2, name: '<PERSON>', email: '<EMAIL>' },
      ];

      const csv = convertToCSV(data);

      // Check header row
      expect(csv).toContain('id,name,email');

      // Check data rows
      expect(csv).toContain('1,<PERSON>,<EMAIL>');
      expect(csv).toContain('2,<PERSON>,<EMAIL>');
    });

    test('handles custom headers', () => {
      const data = [
        { id: 1, name: '<PERSON>', email: '<EMAIL>' },
        { id: 2, name: '<PERSON>', email: '<EMAIL>' },
      ];

      const headers = [
        { key: 'name' as keyof typeof data[0], label: 'Full Name' },
        { key: 'email' as keyof typeof data[0], label: 'Email Address' },
      ];

      const csv = convertToCSV(data, headers);

      // Check header row
      expect(csv).toContain('Full Name,Email Address');

      // Check data rows
      expect(csv).toContain('John,<EMAIL>');
      expect(csv).toContain('Jane,<EMAIL>');

      // Check that id is not included
      expect(csv).not.toContain('id');
      expect(csv).not.toContain('1');
      expect(csv).not.toContain('2');
    });

    test('handles empty data', () => {
      const data: any[] = [];
      const csv = convertToCSV(data);
      expect(csv).toBe('');
    });

    test('handles null and undefined values', () => {
      const data = [
        { id: 1, name: 'John', email: null },
        { id: 2, name: undefined, email: '<EMAIL>' },
      ];

      const csv = convertToCSV(data);

      // Check data rows
      expect(csv).toContain('1,John,');
      expect(csv).toContain('2,,<EMAIL>');
    });

    test('handles values with commas and quotes', () => {
      const data = [
        { id: 1, name: 'John, Doe', email: '<EMAIL>' },
        { id: 2, name: 'Jane "The Brain" Doe', email: '<EMAIL>' },
      ];

      const csv = convertToCSV(data);

      // Check data rows with proper escaping
      expect(csv).toContain('1,"John, Doe",<EMAIL>');
      // The exact format might vary, so we'll check for the key parts
      expect(csv).toContain('Jane');
      expect(csv).toContain('The Brain');
      expect(csv).toContain('Doe');
    });
  });

  describe('formatDateForExport', () => {
    test('formats date string to YYYY-MM-DD', () => {
      const dateStr = '2023-05-15T12:30:45Z';
      const formatted = formatDateForExport(dateStr);
      expect(formatted).toBe('2023-05-15');
    });

    test('formats Date object to YYYY-MM-DD', () => {
      const date = new Date('2023-05-15T12:30:45Z');
      const formatted = formatDateForExport(date);
      expect(formatted).toBe('2023-05-15');
    });

    test('handles null and undefined', () => {
      expect(formatDateForExport(null)).toBe('');
      expect(formatDateForExport(undefined)).toBe('');
    });
  });
});
