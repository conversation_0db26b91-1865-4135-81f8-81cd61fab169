import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import { useSupabase } from '../../app/components/SupabaseProvider';
import ClientPortalsPage from '../../app/client-portals/page';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn().mockReturnValue(new URLSearchParams()),
}));

// Mock @supabase/ssr
jest.mock('@supabase/ssr', () => {
  const mockCreateBrowserClient = jest.fn(() => ({
    from: jest.fn((tableName: string) => {
      if (tableName === 'companies') {
        return {
          select: jest.fn().mockReturnThis(),
          order: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          then: jest.fn((onfulfilled) => 
            Promise.resolve(onfulfilled({
              data: [
                {
                  id: '1',
                  name: 'Test Company',
                  industry: 'Technology',
                  website: 'https://example.com',
                  email: '<EMAIL>',
                  logo_url: null,
                  created_at: '2023-01-01T00:00:00Z',
                  updated_at: '2023-01-01T00:00:00Z',
                },
              ],
              error: null,
            }))
          ),
        };
      }
      // Default mock for other tables
      return {
        select: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        rpc: jest.fn().mockResolvedValue({ data: null, error: null }), // Default for rpc
        then: jest.fn((onfulfilled) => Promise.resolve(onfulfilled({ data: [], error: null }))),
      };
    }),
    rpc: jest.fn((functionName: string) => {
      // Attempt to match potential RPC calls from the component
      if (functionName === 'check_client_portal_auth_migration_status' || functionName === 'get_client_portal_config_status' || functionName === 'check_migration_status') {
        return Promise.resolve({ data: { migration_applied: true, setup_complete: true }, error: null });
      }
      return Promise.resolve({ data: null, error: { message: `RPC function '${functionName}' not mocked` } });
    }),
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } },
      }),
    },
  }));

  return {
    createBrowserClient: mockCreateBrowserClient,
  };
});
// Mock Supabase provider
jest.mock('../../app/components/SupabaseProvider', () => ({
  useSupabase: jest.fn(),
}));

describe('Client Portals Page', () => {
  const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
  };
  
  const mockSupabase = {
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } },
      }),
    },
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSupabase as jest.Mock).mockReturnValue({ supabase: mockSupabase });
  });
  
  describe('Client Portals List Page', () => {
    test('renders loading state initially', () => {
      render(<ClientPortalsPage />);
      expect(screen.getByTestId('loading-indicator')).toBeInTheDocument();
    });
    
    test('renders client portals list when data is loaded', async () => {
      render(<ClientPortalsPage />);
      
      // Wait for the loading state to disappear
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });
      
      // Check if company data is displayed
      expect(screen.getByText('Test Company')).toBeInTheDocument();
      expect(screen.getByText('Technology')).toBeInTheDocument();
    });
    
    test('filters client portals by search term', async () => {
      render(<ClientPortalsPage />);
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });
      
      // Type in search box
      const searchInput = screen.getByPlaceholderText(/search client portals/i);
      fireEvent.change(searchInput, { target: { value: 'Test' } });
      
      // Check if filtering works
      expect(screen.getByText('Test Company')).toBeInTheDocument();
      
      // Change search to something that doesn't match
      fireEvent.change(searchInput, { target: { value: 'Nonexistent' } });
      
      // Check if no results message is shown
      expect(screen.getByText(/no client portals match your search/i)).toBeInTheDocument();
    });
    
    test('navigates to client portal detail page when clicked', async () => {
      render(<ClientPortalsPage />);
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });
      
      // Click on view portal button
      fireEvent.click(screen.getByText('View Portal'));
      
      // Check if router was called with correct path
      expect(mockRouter.push).toHaveBeenCalledWith('/client-portals/1');
    });
  });
});
