import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import ContactsPage from '../../app/contacts/page';
import { useSupabase } from '../../app/components/SupabaseProvider';

// Mock Next.js navigation
const mockRouterPush = jest.fn(); // Create the mock function separately
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => {
    // console.log('useRouter mock called, returning:', { push: mockRouterPush }); // Debugging line
    return { push: mockRouterPush }; // Explicitly return an object with the push mock
  }),
}));

// Mock Supabase provider
const mockSupabase = {
  from: jest.fn().mockReturnThis(), // Ensure 'from' is chainable
  select: jest.fn().mockReturnThis(), // Ensure 'select' is chainable
  order: jest.fn(), // This will be specifically mocked in beforeEach
};

jest.mock('../../app/components/SupabaseProvider', () => ({
  useSupabase: jest.fn(() => ({ supabase: mockSupabase, session: null, user: { id: 'test-user-id'} })),
}));

const mockContactsData = [
  {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '************',
    position: 'CEO',
    company_id: 'comp-1',
    notes: 'Test note',
    companies: { name: 'Test Company' }, // Assuming contacts page might join company name
  },
  // Add more mock contacts if needed for different scenarios
];

describe('Contacts Page', () => {
  beforeEach(() => {
    // Clear all mocks before each test, especially for router.push
    mockRouterPush.mockClear(); 
    (useSupabase as jest.Mock).mockReturnValue({
      supabase: mockSupabase,
      session: null,
      user: { id: 'test-user-id' },
    });

    // Mock the successful resolution of the Supabase query for contacts
    (mockSupabase.order as jest.Mock).mockResolvedValue({
      data: mockContactsData,
      error: null,
    });
    
    // Reset any specific mocks for .from if needed, though the general one should cover it
    // (mockSupabase.from as jest.Mock).mockClear(); 
    (mockSupabase.from as jest.Mock).mockImplementation((tableName: string) => {
      if (tableName === 'contacts') {
          return {
              select: jest.fn().mockReturnThis(),
              order: jest.fn().mockResolvedValue({ data: mockContactsData, error: null }),
          };
      }
      // Fallback for other tables if any are called unexpectedly
      return {
          select: jest.fn().mockReturnThis(),
          order: jest.fn().mockResolvedValue({ data: [], error: null }),
      };
    });
  });

  test('renders loading state initially', async () => {
    render(<ContactsPage />);
    // Check for the loading spinner by its role
    expect(screen.getByRole('status')).toBeInTheDocument();
    // Wait for loading to complete
    await waitFor(() => expect(screen.queryByRole('status')).not.toBeInTheDocument());
  });

  test('renders contact list when data is loaded', async () => {
    render(<ContactsPage />);
    // Wait for loading to complete and for all contact details to appear
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      // Ensure these are also checked within waitFor as their appearance might be tied to the same render cycle
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('CEO')).toBeInTheDocument(); 
      expect(screen.getByRole('link', { name: 'Company Details' })).toBeInTheDocument();
    });
  });

  test('filters contacts by search term', async () => {
    render(<ContactsPage />);
    await waitFor(() => expect(screen.queryByRole('status')).not.toBeInTheDocument());

    const searchInput = screen.getByPlaceholderText(/search contacts/i);
    fireEvent.change(searchInput, { target: { value: 'John' } });
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    fireEvent.change(searchInput, { target: { value: 'Nonexistent' } });
    
    await waitFor(() => {
      expect(screen.getByText('No contacts found')).toBeInTheDocument();
      expect(screen.getByText('Try changing your search terms')).toBeInTheDocument();
    });
  });

  test('navigates to contact detail page when clicked', async () => {
    render(<ContactsPage />);
    // Find the link by its role and name
    const contactNameLink = await screen.findByRole('link', { name: 'John Doe' });
    
    // Assert that the link has the correct href attribute
    expect(contactNameLink).toHaveAttribute('href', '/contacts/1');
    
    // Optionally, we can still fire the click and ensure no errors occur,
    // but the primary assertion is now on the href.
    // fireEvent.click(contactNameLink);
    // expect(mockRouterPush).not.toHaveBeenCalled(); // Or check it wasn't called if Link handles it differently in test
  });

  // Placeholder for Create/Edit tests for contacts
  // describe('Create New Contact Page', () => { ... });
  // describe('Edit Contact Page', () => { ... });
});
