import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import { useSupabase } from '../../app/components/SupabaseProvider';
import DashboardPage from '../../app/dashboard/page';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn().mockReturnValue(new URLSearchParams()),
}));

// Mock Supabase provider
jest.mock('../../app/components/SupabaseProvider', () => ({
  useSupabase: jest.fn(),
}));

// Mock formatCurrency and formatDate utils
jest.mock('../../lib/utils', () => ({
  formatCurrency: jest.fn((amount) => `$${amount}`),
  formatDate: jest.fn((date) => date ? new Date(date).toLocaleDateString() : ''),
}));

describe('Dashboard Page', () => {
  const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
  };
  
  const mockSupabase = {
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } },
      }),
    },
    from: jest.fn().mockImplementation((table) => {
      if (table === 'companies') {
        return {
          select: jest.fn().mockReturnThis(),
          count: jest.fn().mockReturnThis(),
          then: jest.fn().mockResolvedValue({
            data: [{ count: 5 }],
            error: null,
          }),
        };
      } else if (table === 'projects') {
        return {
          select: jest.fn().mockReturnThis(),
          count: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          then: jest.fn().mockResolvedValue({
            data: [{ count: 10 }],
            error: null,
          }),
        };
      } else if (table === 'tasks') {
        return {
          select: jest.fn().mockReturnThis(),
          count: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          then: jest.fn().mockResolvedValue({
            data: [{ count: 15 }],
            error: null,
          }),
        };
      } else if (table === 'contacts') {
        return {
          select: jest.fn().mockReturnThis(),
          count: jest.fn().mockReturnThis(),
          then: jest.fn().mockResolvedValue({
            data: [{ count: 20 }],
            error: null,
          }),
        };
      } else if (table === 'expenses') {
        return {
          select: jest.fn().mockReturnThis(),
          sum: jest.fn().mockReturnThis(),
          then: jest.fn().mockResolvedValue({
            data: [{ sum: 5000 }],
            error: null,
          }),
        };
      } else if (table === 'invoices') {
        return {
          select: jest.fn().mockReturnThis(),
          sum: jest.fn().mockReturnThis(),
          then: jest.fn().mockResolvedValue({
            data: [{ sum: 10000 }],
            error: null,
          }),
        };
      }
      return mockSupabase;
    }),
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSupabase as jest.Mock).mockReturnValue({ supabase: mockSupabase });
  });
  
  test('renders loading state initially', () => {
    render(<DashboardPage />);
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });
  
  test('renders dashboard stats when data is loaded', async () => {
    render(<DashboardPage />);
    
    // Wait for the loading state to disappear
    await waitFor(() => {
      expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
    });
    
    // Check if stats are displayed
    expect(screen.getByText('5')).toBeInTheDocument(); // Companies
    expect(screen.getByText('10')).toBeInTheDocument(); // Projects
    expect(screen.getByText('15')).toBeInTheDocument(); // Tasks
    expect(screen.getByText('20')).toBeInTheDocument(); // Contacts
    expect(screen.getByText('$5,000')).toBeInTheDocument(); // Expenses
    expect(screen.getByText('$10,000')).toBeInTheDocument(); // Revenue
  });
  
  test('renders recent activity section', async () => {
    render(<DashboardPage />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
    });
    
    // Check if recent activity section is displayed
    expect(screen.getByText(/recent activity/i)).toBeInTheDocument();
  });
  
  test('renders quick actions section', async () => {
    render(<DashboardPage />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
    });
    
    // Check if quick actions section is displayed
    expect(screen.getByText(/quick actions/i)).toBeInTheDocument();
    expect(screen.getByText(/add company/i)).toBeInTheDocument();
    expect(screen.getByText(/add project/i)).toBeInTheDocument();
    expect(screen.getByText(/add task/i)).toBeInTheDocument();
    expect(screen.getByText(/add contact/i)).toBeInTheDocument();
  });
});
