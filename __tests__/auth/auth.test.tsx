import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import LoginPage from '../../app/client-login/page';
// No longer directly mocking useSupabase for auth methods here, as login uses fetch
// import { useSupabase } from '../../app/components/SupabaseProvider'; 

// Mock Next.js navigation
const mockRouterPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({ push: mockRouterPush })),
}));

// Mock global fetch
global.fetch = jest.fn();

describe('Login Page', () => {
  beforeEach(() => {
    mockRouterPush.mockClear();
    // Use mockReset to clear implementation and calls, ensuring a clean slate for fetch in each test
    (global.fetch as jest.Mock).mockReset(); 
  });

  test('renders login form correctly', () => {
    render(<LoginPage />);
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  test.todo('shows client-side validation errors for empty fields (HTML required attributes might prevent onSubmit)');

  test('calls API on valid submission and redirects on success', async () => {
    const mockCompanyId = 'company-123';
    (global.fetch as jest.Mock).mockResolvedValueOnce({ 
      ok: true, 
      // Ensure json() is a function that returns a promise
      json: jest.fn().mockResolvedValueOnce({ companyId: mockCompanyId }), 
    });

    render(<LoginPage />);
    fireEvent.change(screen.getByLabelText(/email/i), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByLabelText(/password/i), { target: { value: 'password123' } });
    fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/client-auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: '<EMAIL>', password: 'password123' }),
      });
      expect(mockRouterPush).toHaveBeenCalledWith(`/client-view/${mockCompanyId}`);
    });
  });

  test('shows error message from API on failed login', async () => {
    const apiErrorMessage = 'Invalid credentials from API';
    (global.fetch as jest.Mock).mockResolvedValueOnce({ 
      ok: false, 
      // Ensure json() is a function that returns a promise
      json: jest.fn().mockResolvedValueOnce({ error: apiErrorMessage }), 
    });

    render(<LoginPage />);
    fireEvent.change(screen.getByLabelText(/email/i), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByLabelText(/password/i), { target: { value: 'wrongpassword' } });
    fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/client-auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: '<EMAIL>', password: 'wrongpassword' }),
      });
      // The component catches the error and sets its own generic message
      // If you want to display the specific API error, the component would need to be changed
      expect(screen.getByText('Authentication failed. Please check your email and password.')).toBeInTheDocument();
    });
  });
}); 