import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import { useSupabase } from '../../app/components/SupabaseProvider';
import TaskBoard from '../../app/tasks/board/page';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn().mockReturnValue(new URLSearchParams()),
}));

// Mock Supabase provider
jest.mock('../../app/components/SupabaseProvider', () => ({
  useSupabase: jest.fn(),
}));

// Mock formatDate util
jest.mock('../../lib/utils', () => ({
  formatDate: jest.fn((date) => date ? new Date(date).toLocaleDateString() : ''),
}));

// Mock react-beautiful-dnd
jest.mock('@hello-pangea/dnd', () => ({
  DragDropContext: ({ children }) => <div data-testid="drag-drop-context">{children}</div>,
  Droppable: ({ children, droppableId }) => (
    <div data-testid={`droppable-${droppableId}`}>
      {children(
        {
          droppableProps: {
            'data-testid': `droppable-${droppableId}-props`,
          },
          innerRef: jest.fn(),
        },
        { isDraggingOver: false }
      )}
    </div>
  ),
  Draggable: ({ children, draggableId, index }) => (
    <div data-testid={`draggable-${draggableId}`}>
      {children(
        {
          draggableProps: {
            'data-testid': `draggable-${draggableId}-props`,
          },
          dragHandleProps: {
            'data-testid': `draggable-${draggableId}-handle`,
          },
          innerRef: jest.fn(),
        },
        { isDragging: false }
      )}
    </div>
  ),
}));

describe('Task Board Page', () => {
  const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
  };
  
  const mockSupabase = {
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } },
      }),
    },
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    then: jest.fn(),
  };
  
  const mockTasks = [
    {
      id: 'task-1',
      name: 'Task 1',
      description: 'Description for task 1',
      status: 'todo',
      priority: 'high',
      due_date: '2023-05-01',
      assigned_to: 'user-1',
      project_id: 'project-1',
      company_id: 'company-1',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      projects: {
        id: 'project-1',
        name: 'Project 1',
        companies: {
          id: 'company-1',
          name: 'Company 1',
        },
      },
      users: {
        id: 'user-1',
        email: '<EMAIL>',
        full_name: 'User One',
      },
    },
    {
      id: 'task-2',
      name: 'Task 2',
      description: 'Description for task 2',
      status: 'in_progress',
      priority: 'medium',
      due_date: '2023-05-15',
      assigned_to: 'user-2',
      project_id: 'project-2',
      company_id: 'company-2',
      created_at: '2023-01-02T00:00:00Z',
      updated_at: '2023-01-02T00:00:00Z',
      projects: {
        id: 'project-2',
        name: 'Project 2',
        companies: {
          id: 'company-2',
          name: 'Company 2',
        },
      },
      users: {
        id: 'user-2',
        email: '<EMAIL>',
        full_name: 'User Two',
      },
    },
    {
      id: 'task-3',
      name: 'Task 3',
      description: 'Description for task 3',
      status: 'review',
      priority: 'low',
      due_date: '2023-05-30',
      assigned_to: null,
      project_id: 'project-1',
      company_id: 'company-1',
      created_at: '2023-01-03T00:00:00Z',
      updated_at: '2023-01-03T00:00:00Z',
      projects: {
        id: 'project-1',
        name: 'Project 1',
        companies: {
          id: 'company-1',
          name: 'Company 1',
        },
      },
      users: null,
    },
    {
      id: 'task-4',
      name: 'Task 4',
      description: 'Description for task 4',
      status: 'completed',
      priority: 'high',
      due_date: '2023-04-15',
      assigned_to: 'user-1',
      project_id: 'project-2',
      company_id: 'company-2',
      created_at: '2023-01-04T00:00:00Z',
      updated_at: '2023-01-04T00:00:00Z',
      projects: {
        id: 'project-2',
        name: 'Project 2',
        companies: {
          id: 'company-2',
          name: 'Company 2',
        },
      },
      users: {
        id: 'user-1',
        email: '<EMAIL>',
        full_name: 'User One',
      },
    },
  ];
  
  const mockProjects = [
    { id: 'project-1', name: 'Project 1' },
    { id: 'project-2', name: 'Project 2' },
  ];
  
  const mockCompanies = [
    { id: 'company-1', name: 'Company 1' },
    { id: 'company-2', name: 'Company 2' },
  ];
  
  const mockUsers = [
    { id: 'user-1', email: '<EMAIL>', full_name: 'User One' },
    { id: 'user-2', email: '<EMAIL>', full_name: 'User Two' },
  ];
  
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    
    // Configure the main mockSupabase object for chaining
    // The .from() method will be the primary entry point for queries
    (mockSupabase.from as jest.Mock).mockImplementation((tableName: string) => {
      let dataToReturn = [];
      if (tableName === 'tasks') {
        dataToReturn = mockTasks;
      } else if (tableName === 'projects') {
        dataToReturn = mockProjects;
      } else if (tableName === 'companies') {
        dataToReturn = mockCompanies;
      } else if (tableName === 'users') {
        dataToReturn = mockUsers;
      }

      // This is the object returned by .from('tableName')
      const queryBuilder = {
        select: jest.fn().mockReturnThis(), // .select() returns queryBuilder for further chaining
        eq: jest.fn().mockReturnThis(),     // .eq() returns queryBuilder
        order: jest.fn().mockReturnThis(),  // .order() returns queryBuilder
        update: jest.fn().mockReturnThis(), // .update() returns queryBuilder
        // And finally, the method that resolves the query (simulating await or .then())
        // For simplicity, we'll make it a direct mockResolvedValue here.
        // In a real scenario, .select(), .order() etc. might modify what this resolves to.
        then: jest.fn((callback) => {
          // Supabase .then callback receives an object { data, error }
          if (callback) {
            return Promise.resolve(callback({ data: dataToReturn, error: null }));
          }
          // If used with await, it resolves to { data, error }
          return Promise.resolve({ data: dataToReturn, error: null });
        }),
        // Add other chainable methods if used by the component (e.g., single, limit)
      };
      // Ensure 'then' is also directly resolvable if the component does e.g. `await supabase.from(...).select()`
      // This is a common pattern where the promise resolves directly from select, not just then.
      // However, the structure above where 'then' is a function should cover most promise-like uses.
      // To be super safe for `await supabase.from(...).select()`:
      // Object.assign(queryBuilder, { then: queryBuilder.then }); // This line might be redundant depending on Jest's Promise handling

      return queryBuilder;
    });

    // Ensure useSupabase returns the correctly configured mockSupabase
    (useSupabase as jest.Mock).mockReturnValue({ supabase: mockSupabase });
  });
  
  test('renders loading state initially', () => {
    render(<TaskBoard />);
    expect(screen.getByRole('status')).toBeInTheDocument();
  });
  
  test('renders task board with columns when data is loaded', async () => {
    render(<TaskBoard />);
    
    // Wait for the loading state to disappear
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Check if columns are rendered
    expect(screen.getByText('To Do')).toBeInTheDocument();
    expect(screen.getByText('In Progress')).toBeInTheDocument();
    expect(screen.getByText('Review')).toBeInTheDocument();
    expect(screen.getByText('Completed')).toBeInTheDocument();
    
    // Check if tasks are rendered in the correct columns
    expect(screen.getByText('Task 1')).toBeInTheDocument();
    expect(screen.getByText('Task 2')).toBeInTheDocument();
    expect(screen.getByText('Task 3')).toBeInTheDocument();
    expect(screen.getByText('Task 4')).toBeInTheDocument();
  });
  
  test('filters tasks by search term', async () => {
    render(<TaskBoard />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Type in search box
    const searchInput = screen.getByPlaceholderText(/search tasks/i);
    fireEvent.change(searchInput, { target: { value: 'Task 1' } });
    
    // Check if filtering works
    expect(screen.getByText('Task 1')).toBeInTheDocument();
    expect(screen.queryByText('Task 2')).not.toBeInTheDocument();
    expect(screen.queryByText('Task 3')).not.toBeInTheDocument();
    expect(screen.queryByText('Task 4')).not.toBeInTheDocument();
  });
  
  test('filters tasks by priority', async () => {
    render(<TaskBoard />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Select priority filter
    const priorityFilter = screen.getByLabelText(/priority filter/i);
    fireEvent.change(priorityFilter, { target: { value: 'high' } });
    
    // Check if filtering works
    expect(screen.getByText('Task 1')).toBeInTheDocument();
    expect(screen.queryByText('Task 2')).not.toBeInTheDocument();
    expect(screen.queryByText('Task 3')).not.toBeInTheDocument();
    expect(screen.getByText('Task 4')).toBeInTheDocument();
  });
  
  test('filters tasks by project', async () => {
    render(<TaskBoard />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Select project filter
    const projectFilter = screen.getByLabelText(/project filter/i);
    fireEvent.change(projectFilter, { target: { value: 'project-1' } });
    
    // Check if filtering works
    expect(screen.getByText('Task 1')).toBeInTheDocument();
    expect(screen.queryByText('Task 2')).not.toBeInTheDocument();
    expect(screen.getByText('Task 3')).toBeInTheDocument();
    expect(screen.queryByText('Task 4')).not.toBeInTheDocument();
  });
  
  test('filters tasks by company', async () => {
    render(<TaskBoard />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Select company filter
    const companyFilter = screen.getByLabelText(/company filter/i);
    fireEvent.change(companyFilter, { target: { value: 'company-2' } });
    
    // Check if filtering works
    expect(screen.queryByText('Task 1')).not.toBeInTheDocument();
    expect(screen.getByText('Task 2')).toBeInTheDocument();
    expect(screen.queryByText('Task 3')).not.toBeInTheDocument();
    expect(screen.getByText('Task 4')).toBeInTheDocument();
  });
  
  test('filters tasks by assignee', async () => {
    render(<TaskBoard />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Select assignee filter
    const assigneeFilter = screen.getByLabelText(/assignee filter/i);
    fireEvent.change(assigneeFilter, { target: { value: 'user-1' } });
    
    // Check if filtering works
    expect(screen.getByText('Task 1')).toBeInTheDocument();
    expect(screen.queryByText('Task 2')).not.toBeInTheDocument();
    expect(screen.queryByText('Task 3')).not.toBeInTheDocument();
    expect(screen.getByText('Task 4')).toBeInTheDocument();
  });
});
