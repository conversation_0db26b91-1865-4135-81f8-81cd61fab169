import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import { useSupabase } from '../../app/components/SupabaseProvider';
import TasksPage from '../../app/tasks/page';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(() => new URLSearchParams()),
}));

// Mock Supabase provider
const mockSupabase = {
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  order: jest.fn(),
};

jest.mock('../../app/components/SupabaseProvider', () => ({
  useSupabase: jest.fn(() => ({ supabase: mockSupabase, session: null })),
}));

const mockTasksData = [
  {
    id: '1',
    name: 'Test Task',
    description: 'This is a test task',
    status: 'pending',
    priority: 'medium',
    due_date: '2023-05-01',
    task_type: 'general',
    assigned_to: 'test-user-id',
    project_id: '1',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    projects: {
      id: '1',
      name: 'Test Project',
      company_id: '101',
      companies: {
        id: '101',
        name: 'Test Company',
      },
    },
  },
  // Add more mock tasks if needed for other filter scenarios
];

describe('Tasks Page', () => {
  const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSupabase as jest.Mock).mockReturnValue({ supabase: mockSupabase, session: null });
  });
  
  describe('Tasks List Page', () => {
    beforeEach(() => {
      // Reset mocks before each test
      jest.clearAllMocks();

      // Default successful mock for most tests
      (mockSupabase.from as jest.Mock).mockImplementation((tableName: string) => {
        if (tableName === 'tasks') {
          return {
            select: jest.fn().mockReturnThis(),
            order: jest.fn().mockResolvedValue({ data: mockTasksData, error: null }),
          };
        }
        return { // Default for other tables if any are called unexpectedly
            select: jest.fn().mockReturnThis(),
            order: jest.fn().mockResolvedValue({ data: [], error: null })
        };
      });
      
      // Ensure useSupabase is re-mocked to return the fresh mockSupabase object
      (useSupabase as jest.Mock).mockReturnValue({ supabase: mockSupabase, session: null });
    });
    
    test('renders loading state initially', () => {
      // Specific mock for this test to ensure loading state persists
      (mockSupabase.from as jest.Mock).mockImplementationOnce((tableName: string) => {
        if (tableName === 'tasks') {
          return {
            select: jest.fn().mockReturnThis(),
            order: jest.fn(() => new Promise(() => {})), // Promise that never resolves
          };
        }
        return {}; // Should not be reached if component only calls 'tasks'
      });

      render(<TasksPage />);
      expect(document.querySelector('.animate-spin')).toBeInTheDocument();
    });
    
    test('renders task list when data is loaded', async () => {
      render(<TasksPage />);
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      });
      
      expect(screen.getByText('Test Task')).toBeInTheDocument();
      expect(screen.getByText('Test Project')).toBeInTheDocument();
      expect(screen.getByText('Test Company')).toBeInTheDocument();
    });
    
    test('filters tasks by search term', async () => {
      render(<TasksPage />);
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText(/search tasks/i);
      fireEvent.change(searchInput, { target: { value: 'Test Task' } }); // Exact match for initial filtering
      expect(screen.getByText('Test Task')).toBeInTheDocument();

      fireEvent.change(searchInput, { target: { value: 'Nonexistent' } });
      await waitFor(() => {
        expect(screen.getByText('No tasks found')).toBeInTheDocument();
        expect(screen.getByText('Try adjusting your search or filters')).toBeInTheDocument();
      });
    });
    
    test('filters tasks by status', async () => {
      render(<TasksPage />);
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
      });

      const statusFilterDropdown = screen.getAllByRole('combobox')[0];
      expect(statusFilterDropdown).toHaveValue('all');

      fireEvent.change(statusFilterDropdown, { target: { value: 'pending' } });
      // The mock task is 'pending', so it should still be visible
      expect(screen.getByText('Test Task')).toBeInTheDocument(); 

      fireEvent.change(statusFilterDropdown, { target: { value: 'completed' } });
      await waitFor(() => {
        expect(screen.getByText('No tasks found')).toBeInTheDocument();
        expect(screen.getByText('Try adjusting your search or filters')).toBeInTheDocument();
      });
    });
  });
});
