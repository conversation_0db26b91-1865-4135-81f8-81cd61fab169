import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import { useSupabase } from '../../app/components/SupabaseProvider';
import InvoiceDetail from '../../app/invoices/[id]/page';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn().mockReturnValue(new URLSearchParams()),
}));

// Mock Supabase provider
jest.mock('../../app/components/SupabaseProvider', () => ({
  useSupabase: jest.fn(),
}));

// Mock formatCurrency and formatDate utils
jest.mock('../../../lib/utils', () => ({
  formatCurrency: jest.fn((amount) => `$${amount}`),
  formatDate: jest.fn((date) => date ? new Date(date).toLocaleDateString() : ''),
}));

describe('Invoice Detail Page', () => {
  const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
  };
  
  const mockSupabase = {
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } },
      }),
    },
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    then: jest.fn().mockResolvedValue({
      data: {
        id: 'test-invoice-id',
        invoice_number: 'INV-001',
        amount: 1000,
        status: 'Pending',
        issue_date: '2023-01-01',
        due_date: '2023-01-31',
        company_id: 'test-company-id',
        project_id: 'test-project-id',
        description: 'Test description',
        notes: 'Test notes',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        companies: {
          id: 'test-company-id',
          name: 'Test Company',
        },
        projects: {
          id: 'test-project-id',
          name: 'Test Project',
        },
      },
      error: null,
    }),
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSupabase as jest.Mock).mockReturnValue({ supabase: mockSupabase });
  });
  
  test('renders loading state initially', () => {
    render(<InvoiceDetail params={{ id: 'test-invoice-id' }} />);
    expect(screen.getByRole('status')).toBeInTheDocument();
  });
  
  test('renders invoice details when data is loaded', async () => {
    render(<InvoiceDetail params={{ id: 'test-invoice-id' }} />);
    
    // Wait for the loading state to disappear
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Check if invoice data is displayed
    expect(screen.getByText('Invoice #INV-001')).toBeInTheDocument();
    expect(screen.getByText('$1000')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
    expect(screen.getByText('Test Company')).toBeInTheDocument();
    expect(screen.getByText('Test Project')).toBeInTheDocument();
    expect(screen.getByText('Test description')).toBeInTheDocument();
    expect(screen.getByText('Test notes')).toBeInTheDocument();
  });
  
  test('enables edit mode when edit button is clicked', async () => {
    render(<InvoiceDetail params={{ id: 'test-invoice-id' }} />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Click the edit button
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);
    
    // Check if form elements are displayed
    expect(screen.getByLabelText('Invoice Number *')).toBeInTheDocument();
    expect(screen.getByLabelText('Amount *')).toBeInTheDocument();
    expect(screen.getByLabelText('Status *')).toBeInTheDocument();
    expect(screen.getByLabelText('Issue Date *')).toBeInTheDocument();
    expect(screen.getByLabelText('Due Date *')).toBeInTheDocument();
    
    // Check if form values are pre-filled
    expect(screen.getByLabelText('Invoice Number *')).toHaveValue('INV-001');
    expect(screen.getByLabelText('Amount *')).toHaveValue('1000');
    expect(screen.getByLabelText('Status *')).toHaveValue('Pending');
    expect(screen.getByLabelText('Issue Date *')).toHaveValue('2023-01-01');
    expect(screen.getByLabelText('Due Date *')).toHaveValue('2023-01-31');
    expect(screen.getByLabelText('Description')).toHaveValue('Test description');
    expect(screen.getByLabelText('Notes')).toHaveValue('Test notes');
  });
  
  test('updates invoice when form is submitted', async () => {
    render(<InvoiceDetail params={{ id: 'test-invoice-id' }} />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Click the edit button
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);
    
    // Update form values
    fireEvent.change(screen.getByLabelText('Invoice Number *'), { target: { value: 'INV-002' } });
    fireEvent.change(screen.getByLabelText('Amount *'), { target: { value: '2000' } });
    fireEvent.change(screen.getByLabelText('Status *'), { target: { value: 'Paid' } });
    
    // Submit the form
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);
    
    // Check if update was called with correct values
    await waitFor(() => {
      expect(mockSupabase.update).toHaveBeenCalledWith(expect.objectContaining({
        invoice_number: 'INV-002',
        amount: 2000,
        status: 'Paid',
      }));
    });
  });
  
  test('deletes invoice when delete button is clicked and confirmed', async () => {
    // Mock window.confirm
    const originalConfirm = window.confirm;
    window.confirm = jest.fn().mockReturnValue(true);
    
    render(<InvoiceDetail params={{ id: 'test-invoice-id' }} />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Click the delete button
    const deleteButton = screen.getByText('Delete');
    fireEvent.click(deleteButton);
    
    // Check if delete was called
    await waitFor(() => {
      expect(mockSupabase.delete).toHaveBeenCalled();
    });
    
    // Check if router was called to redirect
    expect(mockRouter.push).toHaveBeenCalledWith('/invoices');
    
    // Restore original confirm
    window.confirm = originalConfirm;
  });
  
  test('shows error message when invoice fails to load', async () => {
    // Mock error response
    mockSupabase.then.mockResolvedValueOnce({
      data: null,
      error: { message: 'Failed to load invoice' },
    });
    
    render(<InvoiceDetail params={{ id: 'test-invoice-id' }} />);
    
    // Wait for loading to finish
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
    
    // Check if error message is displayed
    expect(screen.getByText('Failed to load invoice details')).toBeInTheDocument();
    expect(screen.getByText('Return to invoices list')).toBeInTheDocument();
  });
});
